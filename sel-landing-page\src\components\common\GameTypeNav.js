import React, { useState, useImperativeHandle, forwardRef } from "react";
import PropTypes from "prop-types";
// components
import DetailLeftNav from "../common/DetailLeftNav";
// images
import PolygonBg from "icons/polygon_bg.png";
import PolygonYellow from "icons/polygon_yellow.png";
// constants
import { navList } from "constant";
import classNames from "classnames";

// eslint-disable-next-line react/display-name
const GameTypeNav = forwardRef((props, ref) => {
  const [activeIndex, setActiveIndex] = useState(0);

  const selectType = (e, i) => {
    e.preventDefault();
    setTimeout(() => {
      setActiveIndex(i);
    }, 450);
    props.scrollFn(props.list[i].name);
  };

  useImperativeHandle(ref, () => ({
    setType(e, i) {
      setActiveIndex(i);
    },
    getType() {
      return activeIndex;
    },
  }));

  return (
    <div className="game_type_list_container">
      {props.list.map((item, index) => (
        <div key={item.name + "_" + index}>
          {props.showMoreGames ? (
            index === activeIndex && (
              <>
                <div
                  className={classNames("list_item", {
                    ["list_item_active"]: index === activeIndex,
                  })}
                  onClick={(e) => selectType(e, index)}
                >
                  <span className="gtlc_nav_icon">
                    <img
                      src={PolygonBg}
                      alt="nav icon background"
                      className="nav_brown_bg"
                    />
                    <img
                      src={PolygonYellow}
                      alt="nav background"
                      className="nav_yellow_bg"
                    />
                    <img src={item.icon} alt={item.name} className="nav_img" />
                  </span>
                  <span className="gtlc_nav_item">
                    <span className="nav_item_bg">&nbsp;</span>
                    {item.name}
                  </span>
                </div>
                <DetailLeftNav list={navList} style={{ width: "95%" }} />
              </>
            )
          ) : (
            <div
              className={`list_item ${
                index === activeIndex ? "list_item_active" : ""
              }`}
              onClick={(e) => selectType(e, index)}
            >
              <span className="gtlc_nav_icon">
                <img
                  src={PolygonBg}
                  alt="nav icon background"
                  className="nav_brown_bg"
                />
                <img
                  src={PolygonYellow}
                  alt="nav background"
                  className="nav_yellow_bg"
                />
                <img src={item.icon} alt={item.name} className="nav_img" />
              </span>
              <span className="gtlc_nav_item">
                <span className="nav_item_bg">&nbsp;</span>
                {item.name}
              </span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
});

export default GameTypeNav;

GameTypeNav.propTypes = {
  list: PropTypes.array.isRequired,
  showMoreGames: PropTypes.bool,
  selectedIndex: PropTypes.string, // use this to take reference of selected type
  scrollFn: PropTypes.func,
};
