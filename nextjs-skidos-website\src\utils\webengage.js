import Cookies from "js-cookie";
import { getLocale } from "./helperFunctions";
const locale = Cookies.get("locale") || "en";
const lang = getLocale(locale);
const getDeviceInfo = () => {
  if (typeof window === "undefined") return {};

  return {
    orientation: window.screen.orientation
      ? window.screen.orientation.type
      : window.innerHeight > window.innerWidth
        ? "portrait"
        : "landscape",
    device_model: navigator.userAgent,
    current_osVersion: getOSVersion(),
    current_platform: navigator.platform || "unknown",
  };
};

const getOSVersion = () => {
  const userAgent = window.navigator.userAgent;
  const platform = window.navigator.platform;
  const macosPlatforms = ["Macintosh", "MacIntel", "MacPPC", "Mac68K"];
  const windowsPlatforms = ["Win32", "Win64", "Windows", "WinCE"];
  const iosPlatforms = ["iPhone", "iPad", "iPod"];

  if (macosPlatforms.indexOf(platform) !== -1) {
    return "MacOS";
  } else if (iosPlatforms.indexOf(platform) !== -1) {
    return "iOS";
  } else if (windowsPlatforms.indexOf(platform) !== -1) {
    return "Windows";
  } else if (/Android/.test(userAgent)) {
    return "Android";
  } else if (/Linux/.test(platform)) {
    return "Linux";
  }

  return "unknown";
};

// Modify your trackWebEngageEvent function to include these values:
export const trackWebEngageEvent = (eventName, eventData = {}) => {
  if (typeof window !== "undefined" && window.webengage) {
    let userEmail = "anonymaous-website-user";
    let existingUserDetails = {};
    try {
      const userDetails = localStorage.getItem("UserDetails");
      if (userDetails) {
        const parsed = JSON.parse(userDetails);
        existingUserDetails = parsed;
        userEmail = parsed.email || "";
      }
    } catch (error) {
      console.error("Error parsing UserDetails:", error);
    }

    const deviceInfo = getDeviceInfo();

    const previousPath = window.history.state?.prevPath || document.referrer || "";
    window.webengage.track(eventName, {
      timestamp: new Date().toISOString(),
      user_id: existingUserDetails?.Id,
      email_id: userEmail,
      orientation: deviceInfo.orientation,
      device_model: deviceInfo.device_model,
      current_sdk_version: existingUserDetails?.SDKVersion,
      current_language: lang,
      current_locale: lang,
      current_osVersion: deviceInfo.current_osVersion,
      current_platform: deviceInfo.current_platform,
      previous_screen: previousPath,
      subscription_platform: existingUserDetails?.SubscriptionPlatform,
      subscription_game: "web",
      //       session_id: ,
      // subscription_game: ,
      // product_id: ,

      ...eventData,
    });
  }
};

/**
 * Login user and set their attributes in WebEngage
 * @param {{
 *   userId: string,
 *   email: string,
 *   firstName?: string,
 *   lastName?: string,
 *   phone?: string,
 *   birthDate?: string,
 * }} userDetails
 */
export const webEngagelogin = (userDetails) => {
  if (typeof window === "undefined" || !window.webengage) return;

  const { userId, email, firstName, lastName, phone, birthDate } = userDetails;

  // First login the user
  window.webengage.user.login(userId);

  // Then set all available system attributes
  window.webengage.user.setAttribute("we_email", email);
  if (firstName) {
    window.webengage.user.setAttribute("we_first_name", firstName);
  }

  if (lastName) {
    window.webengage.user.setAttribute("we_last_name", lastName);
  }

  if (phone) {
    window.webengage.user.setAttribute("we_phone", phone);
  }

  if (birthDate) {
    window.webengage.user.setAttribute("we_birth_date", birthDate);
  }
};

/**
 * Logout user from WebEngage
 */
export const webEngageLogout = () => {
  if (typeof window === "undefined" || !window.webengage) return;
  window.webengage.user.logout();
};
