{"name": "skidos-sel", "version": "0.1.0", "private": true, "dependencies": {"@stripe/stripe-js": "^2.1.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "bootstrap": "^5.2.3", "bootstrap-icons": "^1.10.2", "classnames": "^2.3.2", "gsap": "^3.11.4", "hls.js": "^1.4.5", "plyr-react": "^5.2.0", "react": "^18.2.0", "react-bootstrap-icons": "^1.10.2", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-gtm-module": "^2.0.11", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.4.5", "react-scripts": "5.0.1", "shaka-player": "^3.2.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.29.0", "eslint-plugin-react": "^7.31.11"}, "homepage": "/rekindle"}