.userHomeScreenWrapper {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 90vh;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  max-width: 1728px;
  margin: 0 auto;
  position: relative;
  transition: background-image 0.3s ease-in-out;
}

.backgroundImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease-in-out;
}

.lightBackground {
  opacity: 1;
}

.darkBackground {
  opacity: 0;
}

.darkTheme .lightBackground {
  opacity: 0;
}

.darkTheme .darkBackground {
  opacity: 1;
}

.contentWrapper {
  position: relative;
  z-index: 1;
}

.userProfileWrapper {
  display: flex;
  align-items: center;
  font-size: 2.1rem;
  gap: 10px;
  font-family: var(--font-poppins);
  font-weight: 600;
  padding: 1rem 2rem;
  justify-content: space-between;
}

.profileIconWrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.gamesSectionWrapper {
  margin: 1rem 0;
  display: flex;
  gap: 20px;
}

.homeScreenTilesWrapper {
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  align-items: center;
  padding: 1rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.homeScreenTilesWrapper::-webkit-scrollbar {
  display: none;
}

.gameThemeCard {
  width: 290px;
  height: 200px;
  transform: skew(10deg);
  position: relative;
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0px 6.13px 0px 0px #4a2d99;
  background: radial-gradient(66.18% 167.97% at 2.89% 3.83%, #c3b1ff 0%, #7a4bf8 77.08%);
  flex-shrink: 0;
  box-sizing: border-box;
  cursor: pointer;
  font-family: var(--font-poppins);
  font-weight: 600;
}

.labelWrapper {
  position: absolute;
  color: #ffff;
  bottom: 0;
  padding: 0 0.63rem;
  overflow: hidden;
  border-end-start-radius: 10px;
  left: 0;
  width: 100%;
  background: linear-gradient(180deg, #000000 -64.04%, rgba(0, 0, 0, 0) 96.49%);
  height: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.labelWrapper span {
  display: block;
  transform: skew(-10deg) !important;
}

.gamesSectionWrapper:nth-child(2) {
  padding-left: 2.3rem;
}

.homeScreenHeroTile {
  position: relative;
}

.gameCardImg {
  object-fit: cover;
  max-height: 160px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.heroTileLable {
  position: absolute;
  font-size: 1.8rem;
  top: 12px;
  left: 38%;
  color: #fff;
}

.playBtn {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  animation: pulsate 1.5s infinite ease-in-out;
}

.userImage {
  height: 80px;
  width: 80px;
  object-fit: cover;
}

.themeImage {
  cursor: pointer;
}

.themeWrapper {
  cursor: pointer;
  position: relative;
  width: 115px;
  height: 65px;
  overflow: hidden;
}

.themeImageContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

.themeImage {
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease-in-out;
}

.spaceTheme {
  opacity: 1;
}

.natureTheme {
  opacity: 0;
}

.darkTheme .spaceTheme {
  opacity: 0;
}

.darkTheme .natureTheme {
  opacity: 1;
}

@keyframes pulsate {
  0%,
  100% {
    transform: translateX(-50%) scale(1);
  }

  50% {
    transform: translateX(-50%) scale(1.1);
  }
}

@media screen and (max-height: 500px) and (orientation: landscape) {
  .userHomeScreenWrapper {
    min-height: 100vh;
  }

  .homeScreenTilesWrapper {
    padding-top: 2rem;
  }

  .userProfileWrapper {
    font-size: 1rem;
    padding: 1rem 2rem 0rem 2rem;
  }

  .userAvatarImg {
    height: 35px;
    width: 35px;
  }

  .wavingHand {
    width: 20px;
    height: 20px;
  }

  .heroGameImage {
    width: 230px;
    height: 230px;
  }

  .playBtn {
    bottom: -10px;
    width: 40px;
    height: 40px;
  }

  .gamesSectionWrapper {
    margin: 1rem 0;
    gap: 20px;
  }

  .gameThemeCard {
    width: 155px;
    height: 105px;
    border-radius: 14px;
  }

  .gameThemeCardShimmer {
    width: 155px;
    height: 105px;
  }

  .gameCardImg {
    max-height: 90px;
  }

  .labelWrapper {
    padding: 0.2rem 1rem;
    border-end-start-radius: 10px;
    font-size: 0.6rem;
    box-sizing: border-box;
    height: 15px;
  }

  .gamesSectionWrapper:nth-child(2) {
    padding-left: 1.3rem;
  }

  .heroTileLable {
    font-size: 1.2rem;
    top: 6px;
    left: 34%;
    color: #fff;
  }

  .themeImage {
    width: 65px;
    height: 38px;
  }
}

.orientationWarning {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  color: #fff;
  font-size: 1.5rem;
  text-align: center;
}
