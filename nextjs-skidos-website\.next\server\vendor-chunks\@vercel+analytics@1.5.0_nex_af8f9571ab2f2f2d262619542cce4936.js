"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936";
exports.ids = ["vendor-chunks/@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936/node_modules/@vercel/analytics/dist/next/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936/node_modules/@vercel/analytics/dist/next/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a(...params) {\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode(mode = \"auto\") {\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/insights/script.js`;\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject(props = {\n    debug: true\n}) {\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/insights`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction pageview({ route, path }) {\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        inject({\n            framework: props.framework || \"react\",\n            basePath: props.basePath ?? getBasePath(),\n            ...props.route !== void 0 && {\n                disableAutoTrack: true\n            },\n            ...props\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (props.route && props.path) {\n            pageview({\n                route: props.route,\n                path: props.path\n            });\n        }\n    }, [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return {\n            route: null,\n            path\n        };\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return {\n        route: computeRoute(path, finalParams),\n        path\n    };\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction AnalyticsComponent(props) {\n    const { route, path } = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Analytics, {\n        path,\n        route,\n        ...props,\n        basePath: getBasePath2(),\n        framework: \"next\"\n    });\n}\nfunction Analytics2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(AnalyticsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHZlcmNlbCthbmFseXRpY3NAMS41LjBfbmV4X2FmOGY5NTcxYWIyZjJmMmQyNjI2MTk1NDJjY2U0OTM2L25vZGVfbW9kdWxlcy9AdmVyY2VsL2FuYWx5dGljcy9kaXN0L25leHQvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFDZ0Q7O0FDQXRCOztBQ0F4QixJQUFBRyxPQUFRO0FBQ1IsSUFBQUMsVUFBVzs7QUNGTixJQUFNQyxZQUFZO0lBRXZCLElBQUlDLE9BQU9DLEVBQUEsRUFBSTtJQUVmRCxPQUFPQyxFQUFBLEdBQUssU0FBU0MsRUFBQSxHQUFLQyxNQUFBO1FBQ3ZCSCxDQUFBQSxPQUFPSSxHQUFBLEdBQU1KLE9BQU9JLEdBQUEsSUFBTyxFQUFDLEVBQUdDLElBQUEsQ0FBS0Y7SUFDdkM7QUFDRjs7QUNMTyxTQUFTRztJQUNkLE9BQU8sZ0JBQWtCO0FBQzNCO0FBRUEsU0FBU0M7SUFDUCxJQUFJO1FBQ0YsTUFBTUM7UUFDTixJQUFJQSxRQUFRLGlCQUFpQkEsUUFBUSxRQUFRO1lBQzNDLE9BQU87UUFDVDtJQUNGLFNBQVNDLEdBQUcsQ0FFWjtJQUNBLE9BQU87QUFDVDtBQUVPLFNBQVNDLFFBQVFDLE9BQWE7SUFDbkMsSUFBSUEsU0FBUyxRQUFRO1FBQ25CWCxPQUFPWSxHQUFBLEdBQU1MO1FBQ2I7SUFDRjtJQUVBUCxPQUFPWSxHQUFBLEdBQU1EO0FBQ2Y7QUFFTyxTQUFTRTtJQUNkLE1BQU1GLE9BQU9MLGNBQWNOLE9BQU9ZLEdBQUEsR0FBTUw7SUFDeEMsT0FBT0ksUUFBUTtBQUNqQjtBQU1PLFNBQVNHO0lBQ2QsT0FBT0QsY0FBYztBQUN2QjtBQXNDTyxTQUFTRSxhQUNkQyxRQUFBLEVBQ0FDLFVBQUE7SUFFQSxJQUFJLENBQUNELFlBQVksQ0FBQ0MsWUFBWTtRQUM1QixPQUFPRDtJQUNUO0lBRUEsSUFBSUUsU0FBU0Y7SUFDYixJQUFJO1FBQ0YsTUFBTUcsVUFBVUMsT0FBT0QsT0FBQSxDQUFRRjtRQUUvQixXQUFXLENBQUNJLEtBQUtDLE1BQUssSUFBS0gsUUFBUztZQUNsQyxJQUFJLENBQUNJLE1BQU1DLE9BQUEsQ0FBUUYsUUFBUTtnQkFDekIsTUFBTUcsVUFBVUMsa0JBQWtCSjtnQkFDbEMsSUFBSUcsUUFBUUUsSUFBQSxDQUFLVCxTQUFTO29CQUN4QkEsU0FBU0EsT0FBT1UsT0FBQSxDQUFRSCxTQUFTLEtBQUtKLElBQUcsRUFBRztnQkFDOUM7WUFDRjtRQUNGO1FBRUEsV0FBVyxDQUFDQSxLQUFLQyxNQUFLLElBQUtILFFBQVM7WUFDbEMsSUFBSUksTUFBTUMsT0FBQSxDQUFRRixRQUFRO2dCQUN4QixNQUFNRyxVQUFVQyxrQkFBa0JKLE1BQU1PLElBQUEsQ0FBSztnQkFDN0MsSUFBSUosUUFBUUUsSUFBQSxDQUFLVCxTQUFTO29CQUN4QkEsU0FBU0EsT0FBT1UsT0FBQSxDQUFRSCxTQUFTLFFBQVFKLElBQUcsRUFBRztnQkFDakQ7WUFDRjtRQUNGO1FBQ0EsT0FBT0g7SUFDVCxTQUFTVCxHQUFHO1FBQ1YsT0FBT087SUFDVDtBQUNGO0FBRUEsU0FBU1Usa0JBQWtCSixLQUFBO0lBQ3pCLE9BQU8sSUFBSVEsT0FBTyxJQUFJQyxhQUFhVCxPQUFNLFlBQWE7QUFDeEQ7QUFFQSxTQUFTUyxhQUFhQyxNQUFBO0lBQ3BCLE9BQU9BLE9BQU9KLE9BQUEsQ0FBUSx1QkFBdUI7QUFDL0M7QUFFTyxTQUFTSyxhQUNkQyxLQUFBO0lBRUEsSUFBSUEsTUFBTUMsU0FBQSxFQUFXO1FBQ25CLE9BQU9ELE1BQU1DLFNBQUE7SUFDZjtJQUNBLElBQUlyQixpQkFBaUI7UUFDbkIsT0FBTztJQUNUO0lBQ0EsSUFBSW9CLE1BQU1FLFFBQUEsRUFBVTtRQUNsQixPQUFPLEdBQUdGLE1BQU1FLFFBQVE7SUFDMUI7SUFDQSxPQUFPO0FBQ1Q7O0FDckdBLFNBQVNDLE9BQ1BILFFBSUk7SUFDRkksT0FBTztBQUNUO0lBdENGLElBQUFDO0lBd0NFLElBQUksQ0FBQ2pDLGFBQWE7SUFFbEJJLFFBQVF3QixNQUFNdkIsSUFBSTtJQUVsQlo7SUFFQSxJQUFJbUMsTUFBTU0sVUFBQSxFQUFZO1FBQ3BCRCxDQUFBQSxLQUFBdkMsT0FBT0MsRUFBQSxLQUFQLGdCQUFBc0MsR0FBQUUsSUFBQSxDQUFBekMsUUFBWSxjQUFja0MsTUFBTU0sVUFBQTtJQUNsQztJQUVBLE1BQU1FLE1BQU1ULGFBQWFDO0lBRXpCLElBQUlTLFNBQVNDLElBQUEsQ0FBS0MsYUFBQSxDQUFjLGdCQUFnQkgsSUFBRyxHQUFJLEdBQUc7SUFFMUQsTUFBTUksU0FBU0gsU0FBU0ksYUFBQSxDQUFjO0lBQ3RDRCxPQUFPSixHQUFBLEdBQU1BO0lBQ2JJLE9BQU9FLEtBQUEsR0FBUTtJQUNmRixPQUFPRyxPQUFBLENBQVFDLElBQUEsR0FDYnJELE9BQWVxQyxDQUFBQSxNQUFNaUIsU0FBQSxHQUFZLElBQUlqQixNQUFNaUIsU0FBUyxLQUFLO0lBQzNETCxPQUFPRyxPQUFBLENBQVFHLElBQUEsR0FBT3REO0lBRXRCLElBQUlvQyxNQUFNbUIsZ0JBQUEsRUFBa0I7UUFDMUJQLE9BQU9HLE9BQUEsQ0FBUUksZ0JBQUEsR0FBbUI7SUFDcEM7SUFDQSxJQUFJbkIsTUFBTW9CLFFBQUEsRUFBVTtRQUNsQlIsT0FBT0csT0FBQSxDQUFRSyxRQUFBLEdBQVdwQixNQUFNb0IsUUFBQTtJQUNsQyxXQUFXcEIsTUFBTUUsUUFBQSxFQUFVO1FBQ3pCVSxPQUFPRyxPQUFBLENBQVFLLFFBQUEsR0FBVyxHQUFHcEIsTUFBTUUsUUFBUTtJQUM3QztJQUNBLElBQUlGLE1BQU1xQixHQUFBLEVBQUs7UUFDYlQsT0FBT0csT0FBQSxDQUFRTSxHQUFBLEdBQU1yQixNQUFNcUIsR0FBQTtJQUM3QjtJQUVBVCxPQUFPVSxPQUFBLEdBQVU7UUFDZixNQUFNQyxlQUFlM0Msa0JBQ2pCLCtEQUNBO1FBR0o0QyxRQUFRQyxHQUFBLENBQ04scURBQXFEakIsSUFBRyxJQUFLZSxhQUFZO0lBRTdFO0lBRUEsSUFBSTNDLG1CQUFtQm9CLE1BQU1JLEtBQUEsS0FBVSxPQUFPO1FBQzVDUSxPQUFPRyxPQUFBLENBQVFYLEtBQUEsR0FBUTtJQUN6QjtJQUVBSyxTQUFTQyxJQUFBLENBQUtnQixXQUFBLENBQVlkO0FBQzVCO0FBb0RBLFNBQVNlLFNBQVMsRUFDaEJDLEtBQUEsRUFDQUMsSUFBQSxFQUNGO0lBaEpBLElBQUF4QjtJQW9KRUEsQ0FBQUEsS0FBQXZDLE9BQU9DLEVBQUEsS0FBUCxnQkFBQXNDLEdBQUFFLElBQUEsQ0FBQXpDLFFBQVksWUFBWTtRQUFFOEQ7UUFBT0M7SUFBSztBQUN4Qzs7QUNySk8sU0FBU0M7SUFLZCxJQUFJLE9BQU9DLFlBQVksZUFBZSxPQUFPQSxRQUFRekQsR0FBQSxLQUFRLGFBQWE7UUFDeEUsT0FBTztJQUNUO0lBQ0EsT0FBT3lELFFBQVF6RCxHQUFBLENBQUkwRCx1Q0FBQTtBQUNyQjs7QUxvQkEsU0FBU0MsVUFDUGpDLEtBQUE7SUFPQXRDLGdEQUFTQSxDQUFDO1FBckNaLElBQUEyQztRQXNDSSxJQUFJTCxNQUFNTSxVQUFBLEVBQVk7WUFDcEJELENBQUFBLEtBQUF2QyxPQUFPQyxFQUFBLEtBQVAsZ0JBQUFzQyxHQUFBRSxJQUFBLENBQUF6QyxRQUFZLGNBQWNrQyxNQUFNTSxVQUFBO1FBQ2xDO0lBQ0YsR0FBRztRQUFDTixNQUFNTSxVQUFVO0tBQUM7SUFHckI1QyxnREFBU0EsQ0FBQztRQUNSeUMsT0FBTztZQUNMYyxXQUFXakIsTUFBTWlCLFNBQUEsSUFBYTtZQUM5QmYsVUFBVUYsTUFBTUUsUUFBQSxJQUFZNEI7WUFDNUIsR0FBSTlCLE1BQU00QixLQUFBLEtBQVUsVUFBYTtnQkFBRVQsa0JBQWtCO1lBQUs7WUFDMUQsR0FBR25CLEtBQUE7UUFDTDtJQUVGLEdBQUcsRUFBRTtJQUVMdEMsZ0RBQVNBLENBQUM7UUFFUixJQUFJc0MsTUFBTTRCLEtBQUEsSUFBUzVCLE1BQU02QixJQUFBLEVBQU07WUFDN0JGLFNBQVM7Z0JBQUVDLE9BQU81QixNQUFNNEIsS0FBQTtnQkFBT0MsTUFBTTdCLE1BQU02QixJQUFBO1lBQUs7UUFDbEQ7SUFDRixHQUFHO1FBQUM3QixNQUFNNEIsS0FBQTtRQUFPNUIsTUFBTTZCLElBQUk7S0FBQztJQUU1QixPQUFPO0FBQ1Q7O0FNNUR3RDtBQUdqRCxJQUFNUSxXQUFXO0lBSXRCLE1BQU1wRSxTQUFTaUUsNkRBQVNBO0lBQ3hCLE1BQU1JLGVBQWVGLG1FQUFlQTtJQUNwQyxNQUFNUCxPQUFPTSwrREFBV0E7SUFHeEIsSUFBSSxDQUFDbEUsUUFBUTtRQUNYLE9BQU87WUFBRTJELE9BQU87WUFBTUM7UUFBSztJQUM3QjtJQUVBLE1BQU1VLGNBQWNyRCxPQUFPc0QsSUFBQSxDQUFLdkUsUUFBUXdFLE1BQUEsR0FDcEN4RSxTQUNBaUIsT0FBT3dELFdBQUEsQ0FBWUosYUFBYXJELE9BQUE7SUFDcEMsT0FBTztRQUFFMkMsT0FBTy9DLGFBQWFnRCxNQUFNVTtRQUFjVjtJQUFLO0FBQ3hEO0FBRU8sU0FBU0M7SUFLZCxJQUFJLE9BQU9DLFlBQVksZUFBZSxPQUFPQSxRQUFRekQsR0FBQSxLQUFRLGFBQWE7UUFDeEUsT0FBTztJQUNUO0lBQ0EsT0FBT3lELFFBQVF6RCxHQUFBLENBQUlxRSx5Q0FBQTtBQUNyQjs7QVB6QkEsU0FBU0MsbUJBQW1CNUMsS0FBQTtJQUMxQixNQUFNLEVBQUU0QixLQUFBLEVBQU9DLElBQUEsRUFBSyxHQUFJUTtJQUN4QixPQUNFLDhCQUFBN0UsZ0RBQUEsQ0FBQ3lFLFdBQUE7UUFDQ0o7UUFDQUQ7UUFDQyxHQUFHNUIsS0FBQTtRQUNKRSxVQUFVNEI7UUFDVmIsV0FBVTtJQUFBO0FBR2hCO0FBRU8sU0FBU2dCLFdBQVVqQyxLQUFBO0lBRXhCLE9BQ0UsOEJBQUF4QyxnREFBQSxDQUFDQywyQ0FBUUEsRUFBUjtRQUFTb0YsVUFBVTtJQUFBLEdBQ2xCLDhCQUFBckYsZ0RBQUEsQ0FBQ29GLG9CQUFBO1FBQW9CLEdBQUc1QyxLQUFBO0lBQUE7QUFHOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LXNraWRvcy13ZWJzaXRlLy4uLy4uL3NyYy9uZXh0anMvaW5kZXgudHN4P2RiMzciLCJ3ZWJwYWNrOi8vbmV4dC1za2lkb3Mtd2Vic2l0ZS8uLi8uLi9zcmMvcmVhY3QvaW5kZXgudHN4P2U0YjciLCJ3ZWJwYWNrOi8vbmV4dC1za2lkb3Mtd2Vic2l0ZS8uLi8uLi9wYWNrYWdlLmpzb24/NmZjNiIsIndlYnBhY2s6Ly9uZXh0LXNraWRvcy13ZWJzaXRlLy4uLy4uL3NyYy9xdWV1ZS50cz84NzNlIiwid2VicGFjazovL25leHQtc2tpZG9zLXdlYnNpdGUvLi4vLi4vc3JjL3V0aWxzLnRzPzYyNTIiLCJ3ZWJwYWNrOi8vbmV4dC1za2lkb3Mtd2Vic2l0ZS8uLi8uLi9zcmMvZ2VuZXJpYy50cz8wYmRmIiwid2VicGFjazovL25leHQtc2tpZG9zLXdlYnNpdGUvLi4vLi4vc3JjL3JlYWN0L3V0aWxzLnRzP2NjZTkiLCJ3ZWJwYWNrOi8vbmV4dC1za2lkb3Mtd2Vic2l0ZS8uLi8uLi9zcmMvbmV4dGpzL3V0aWxzLnRzPzE3NjgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IFJlYWN0LCB7IFN1c3BlbnNlLCB0eXBlIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEFuYWx5dGljcyBhcyBBbmFseXRpY3NTY3JpcHQgfSBmcm9tICcuLi9yZWFjdCc7XG5pbXBvcnQgdHlwZSB7IEFuYWx5dGljc1Byb3BzLCBCZWZvcmVTZW5kLCBCZWZvcmVTZW5kRXZlbnQgfSBmcm9tICcuLi90eXBlcyc7XG5pbXBvcnQgeyBnZXRCYXNlUGF0aCwgdXNlUm91dGUgfSBmcm9tICcuL3V0aWxzJztcblxudHlwZSBQcm9wcyA9IE9taXQ8QW5hbHl0aWNzUHJvcHMsICdyb3V0ZScgfCAnZGlzYWJsZUF1dG9UcmFjayc+O1xuXG5mdW5jdGlvbiBBbmFseXRpY3NDb21wb25lbnQocHJvcHM6IFByb3BzKTogUmVhY3ROb2RlIHtcbiAgY29uc3QgeyByb3V0ZSwgcGF0aCB9ID0gdXNlUm91dGUoKTtcbiAgcmV0dXJuIChcbiAgICA8QW5hbHl0aWNzU2NyaXB0XG4gICAgICBwYXRoPXtwYXRofVxuICAgICAgcm91dGU9e3JvdXRlfVxuICAgICAgey4uLnByb3BzfVxuICAgICAgYmFzZVBhdGg9e2dldEJhc2VQYXRoKCl9XG4gICAgICBmcmFtZXdvcms9XCJuZXh0XCJcbiAgICAvPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gQW5hbHl0aWNzKHByb3BzOiBQcm9wcyk6IG51bGwge1xuICAvLyBCZWNhdXNlIG9mIGluY29tcGF0aWJsZSB0eXBlcyBiZXR3ZWVuIFJlYWN0Tm9kZSBpbiBSZWFjdCAxOSBhbmQgUmVhY3QgMTggd2UgcmV0dXJuIG51bGwgKHdoaWNoIGlzIGFsc28gd2hhdCB3ZSByZW5kZXIpXG4gIHJldHVybiAoXG4gICAgPFN1c3BlbnNlIGZhbGxiYWNrPXtudWxsfT5cbiAgICAgIDxBbmFseXRpY3NDb21wb25lbnQgey4uLnByb3BzfSAvPlxuICAgIDwvU3VzcGVuc2U+XG4gICkgYXMgbmV2ZXI7XG59XG5cbmV4cG9ydCB0eXBlIHsgQW5hbHl0aWNzUHJvcHMsIEJlZm9yZVNlbmQsIEJlZm9yZVNlbmRFdmVudCB9O1xuIiwiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaW5qZWN0LCB0cmFjaywgcGFnZXZpZXcgfSBmcm9tICcuLi9nZW5lcmljJztcbmltcG9ydCB0eXBlIHsgQW5hbHl0aWNzUHJvcHMsIEJlZm9yZVNlbmQsIEJlZm9yZVNlbmRFdmVudCB9IGZyb20gJy4uL3R5cGVzJztcbmltcG9ydCB7IGdldEJhc2VQYXRoIH0gZnJvbSAnLi91dGlscyc7XG5cbi8qKlxuICogSW5qZWN0cyB0aGUgVmVyY2VsIFdlYiBBbmFseXRpY3Mgc2NyaXB0IGludG8gdGhlIHBhZ2UgaGVhZCBhbmQgc3RhcnRzIHRyYWNraW5nIHBhZ2Ugdmlld3MuIFJlYWQgbW9yZSBpbiBvdXIgW2RvY3VtZW50YXRpb25dKGh0dHBzOi8vdmVyY2VsLmNvbS9kb2NzL2NvbmNlcHRzL2FuYWx5dGljcy9wYWNrYWdlKS5cbiAqIEBwYXJhbSBbcHJvcHNdIC0gQW5hbHl0aWNzIG9wdGlvbnMuXG4gKiBAcGFyYW0gW3Byb3BzLm1vZGVdIC0gVGhlIG1vZGUgdG8gdXNlIGZvciB0aGUgYW5hbHl0aWNzIHNjcmlwdC4gRGVmYXVsdHMgdG8gYGF1dG9gLlxuICogIC0gYGF1dG9gIC0gQXV0b21hdGljYWxseSBkZXRlY3QgdGhlIGVudmlyb25tZW50LiAgVXNlcyBgcHJvZHVjdGlvbmAgaWYgdGhlIGVudmlyb25tZW50IGNhbm5vdCBiZSBkZXRlcm1pbmVkLlxuICogIC0gYHByb2R1Y3Rpb25gIC0gQWx3YXlzIHVzZSB0aGUgcHJvZHVjdGlvbiBzY3JpcHQuIChTZW5kcyBldmVudHMgdG8gdGhlIHNlcnZlcilcbiAqICAtIGBkZXZlbG9wbWVudGAgLSBBbHdheXMgdXNlIHRoZSBkZXZlbG9wbWVudCBzY3JpcHQuIChMb2dzIGV2ZW50cyB0byB0aGUgY29uc29sZSlcbiAqIEBwYXJhbSBbcHJvcHMuZGVidWddIC0gV2hldGhlciB0byBlbmFibGUgZGVidWcgbG9nZ2luZyBpbiBkZXZlbG9wbWVudC4gRGVmYXVsdHMgdG8gYHRydWVgLlxuICogQHBhcmFtIFtwcm9wcy5iZWZvcmVTZW5kXSAtIEEgbWlkZGxld2FyZSBmdW5jdGlvbiB0byBtb2RpZnkgZXZlbnRzIGJlZm9yZSB0aGV5IGFyZSBzZW50LiBTaG91bGQgcmV0dXJuIHRoZSBldmVudCBvYmplY3Qgb3IgYG51bGxgIHRvIGNhbmNlbCB0aGUgZXZlbnQuXG4gKiBAZXhhbXBsZVxuICogYGBganNcbiAqIGltcG9ydCB7IEFuYWx5dGljcyB9IGZyb20gJ0B2ZXJjZWwvYW5hbHl0aWNzL3JlYWN0JztcbiAqXG4gKiBleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoKSB7XG4gKiAgcmV0dXJuIChcbiAqICAgPGRpdj5cbiAqICAgIDxBbmFseXRpY3MgLz5cbiAqICAgIDxoMT5NeSBBcHA8L2gxPlxuICogIDwvZGl2PlxuICogKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiBBbmFseXRpY3MoXG4gIHByb3BzOiBBbmFseXRpY3NQcm9wcyAmIHtcbiAgICBmcmFtZXdvcms/OiBzdHJpbmc7XG4gICAgcm91dGU/OiBzdHJpbmcgfCBudWxsO1xuICAgIHBhdGg/OiBzdHJpbmcgfCBudWxsO1xuICAgIGJhc2VQYXRoPzogc3RyaW5nO1xuICB9XG4pOiBudWxsIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocHJvcHMuYmVmb3JlU2VuZCkge1xuICAgICAgd2luZG93LnZhPy4oJ2JlZm9yZVNlbmQnLCBwcm9wcy5iZWZvcmVTZW5kKTtcbiAgICB9XG4gIH0sIFtwcm9wcy5iZWZvcmVTZW5kXSk7XG5cbiAgLy8gYmlvbWUtaWdub3JlIGxpbnQvY29ycmVjdG5lc3MvdXNlRXhoYXVzdGl2ZURlcGVuZGVuY2llczogb25seSBydW4gb25jZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGluamVjdCh7XG4gICAgICBmcmFtZXdvcms6IHByb3BzLmZyYW1ld29yayB8fCAncmVhY3QnLFxuICAgICAgYmFzZVBhdGg6IHByb3BzLmJhc2VQYXRoID8/IGdldEJhc2VQYXRoKCksXG4gICAgICAuLi4ocHJvcHMucm91dGUgIT09IHVuZGVmaW5lZCAmJiB7IGRpc2FibGVBdXRvVHJhY2s6IHRydWUgfSksXG4gICAgICAuLi5wcm9wcyxcbiAgICB9KTtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzIC0tIG9ubHkgcnVuIG9uY2VcbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gZXhwbGljaXRlbHkgdHJhY2sgcGFnZSB2aWV3LCBzaW5jZSB3ZSBkaXNhYmxlZCBhdXRvIHRyYWNraW5nXG4gICAgaWYgKHByb3BzLnJvdXRlICYmIHByb3BzLnBhdGgpIHtcbiAgICAgIHBhZ2V2aWV3KHsgcm91dGU6IHByb3BzLnJvdXRlLCBwYXRoOiBwcm9wcy5wYXRoIH0pO1xuICAgIH1cbiAgfSwgW3Byb3BzLnJvdXRlLCBwcm9wcy5wYXRoXSk7XG5cbiAgcmV0dXJuIG51bGw7XG59XG5cbmV4cG9ydCB7IHRyYWNrLCBBbmFseXRpY3MgfTtcbmV4cG9ydCB0eXBlIHsgQW5hbHl0aWNzUHJvcHMsIEJlZm9yZVNlbmQsIEJlZm9yZVNlbmRFdmVudCB9O1xuIiwie1xuICBcIm5hbWVcIjogXCJAdmVyY2VsL2FuYWx5dGljc1wiLFxuICBcInZlcnNpb25cIjogXCIxLjUuMFwiLFxuICBcImRlc2NyaXB0aW9uXCI6IFwiR2FpbiByZWFsLXRpbWUgdHJhZmZpYyBpbnNpZ2h0cyB3aXRoIFZlcmNlbCBXZWIgQW5hbHl0aWNzXCIsXG4gIFwia2V5d29yZHNcIjogW1xuICAgIFwiYW5hbHl0aWNzXCIsXG4gICAgXCJ2ZXJjZWxcIlxuICBdLFxuICBcInJlcG9zaXRvcnlcIjoge1xuICAgIFwidXJsXCI6IFwiZ2l0aHViOnZlcmNlbC9hbmFseXRpY3NcIixcbiAgICBcImRpcmVjdG9yeVwiOiBcInBhY2thZ2VzL3dlYlwiXG4gIH0sXG4gIFwibGljZW5zZVwiOiBcIk1QTC0yLjBcIixcbiAgXCJleHBvcnRzXCI6IHtcbiAgICBcIi4vcGFja2FnZS5qc29uXCI6IFwiLi9wYWNrYWdlLmpzb25cIixcbiAgICBcIi5cIjoge1xuICAgICAgXCJicm93c2VyXCI6IFwiLi9kaXN0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvaW5kZXguanNcIlxuICAgIH0sXG4gICAgXCIuL2FzdHJvXCI6IHtcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L2FzdHJvL2NvbXBvbmVudC50c1wiXG4gICAgfSxcbiAgICBcIi4vbmV4dFwiOiB7XG4gICAgICBcImJyb3dzZXJcIjogXCIuL2Rpc3QvbmV4dC9pbmRleC5tanNcIixcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L25leHQvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvbmV4dC9pbmRleC5qc1wiXG4gICAgfSxcbiAgICBcIi4vbnV4dFwiOiB7XG4gICAgICBcImJyb3dzZXJcIjogXCIuL2Rpc3QvbnV4dC9pbmRleC5tanNcIixcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L251eHQvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvbnV4dC9pbmRleC5qc1wiXG4gICAgfSxcbiAgICBcIi4vcmVhY3RcIjoge1xuICAgICAgXCJicm93c2VyXCI6IFwiLi9kaXN0L3JlYWN0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvcmVhY3QvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvcmVhY3QvaW5kZXguanNcIlxuICAgIH0sXG4gICAgXCIuL3JlbWl4XCI6IHtcbiAgICAgIFwiYnJvd3NlclwiOiBcIi4vZGlzdC9yZW1peC9pbmRleC5tanNcIixcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L3JlbWl4L2luZGV4Lm1qc1wiLFxuICAgICAgXCJyZXF1aXJlXCI6IFwiLi9kaXN0L3JlbWl4L2luZGV4LmpzXCJcbiAgICB9LFxuICAgIFwiLi9zZXJ2ZXJcIjoge1xuICAgICAgXCJub2RlXCI6IFwiLi9kaXN0L3NlcnZlci9pbmRleC5tanNcIixcbiAgICAgIFwiZWRnZS1saWdodFwiOiBcIi4vZGlzdC9zZXJ2ZXIvaW5kZXgubWpzXCIsXG4gICAgICBcImltcG9ydFwiOiBcIi4vZGlzdC9zZXJ2ZXIvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3Qvc2VydmVyL2luZGV4LmpzXCIsXG4gICAgICBcImRlZmF1bHRcIjogXCIuL2Rpc3Qvc2VydmVyL2luZGV4LmpzXCJcbiAgICB9LFxuICAgIFwiLi9zdmVsdGVraXRcIjoge1xuICAgICAgXCJzdmVsdGVcIjogXCIuL2Rpc3Qvc3ZlbHRla2l0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJ0eXBlc1wiOiBcIi4vZGlzdC9zdmVsdGVraXQvaW5kZXguZC50c1wiXG4gICAgfSxcbiAgICBcIi4vdnVlXCI6IHtcbiAgICAgIFwiYnJvd3NlclwiOiBcIi4vZGlzdC92dWUvaW5kZXgubWpzXCIsXG4gICAgICBcImltcG9ydFwiOiBcIi4vZGlzdC92dWUvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvdnVlL2luZGV4LmpzXCJcbiAgICB9XG4gIH0sXG4gIFwibWFpblwiOiBcIi4vZGlzdC9pbmRleC5tanNcIixcbiAgXCJ0eXBlc1wiOiBcIi4vZGlzdC9pbmRleC5kLnRzXCIsXG4gIFwidHlwZXNWZXJzaW9uc1wiOiB7XG4gICAgXCIqXCI6IHtcbiAgICAgIFwiKlwiOiBbXG4gICAgICAgIFwiZGlzdC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcIm5leHRcIjogW1xuICAgICAgICBcImRpc3QvbmV4dC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcIm51eHRcIjogW1xuICAgICAgICBcImRpc3QvbnV4dC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcInJlYWN0XCI6IFtcbiAgICAgICAgXCJkaXN0L3JlYWN0L2luZGV4LmQudHNcIlxuICAgICAgXSxcbiAgICAgIFwicmVtaXhcIjogW1xuICAgICAgICBcImRpc3QvcmVtaXgvaW5kZXguZC50c1wiXG4gICAgICBdLFxuICAgICAgXCJzZXJ2ZXJcIjogW1xuICAgICAgICBcImRpc3Qvc2VydmVyL2luZGV4LmQudHNcIlxuICAgICAgXSxcbiAgICAgIFwic3ZlbHRla2l0XCI6IFtcbiAgICAgICAgXCJkaXN0L3N2ZWx0ZWtpdC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcInZ1ZVwiOiBbXG4gICAgICAgIFwiZGlzdC92dWUvaW5kZXguZC50c1wiXG4gICAgICBdXG4gICAgfVxuICB9LFxuICBcInNjcmlwdHNcIjoge1xuICAgIFwiYnVpbGRcIjogXCJ0c3VwICYmIHBucG0gY29weS1hc3Ryb1wiLFxuICAgIFwiY29weS1hc3Ryb1wiOiBcImNwIC1SIHNyYy9hc3RybyBkaXN0L1wiLFxuICAgIFwiZGV2XCI6IFwicG5wbSBjb3B5LWFzdHJvICYmIHRzdXAgLS13YXRjaFwiLFxuICAgIFwibGludFwiOiBcImVzbGludCAuXCIsXG4gICAgXCJsaW50LWZpeFwiOiBcImVzbGludCAuIC0tZml4XCIsXG4gICAgXCJ0ZXN0XCI6IFwidml0ZXN0XCIsXG4gICAgXCJ0eXBlLWNoZWNrXCI6IFwidHNjIC0tbm9FbWl0XCJcbiAgfSxcbiAgXCJlc2xpbnRDb25maWdcIjoge1xuICAgIFwiZXh0ZW5kc1wiOiBbXG4gICAgICBcIkB2ZXJjZWwvZXNsaW50LWNvbmZpZ1wiXG4gICAgXSxcbiAgICBcInJ1bGVzXCI6IHtcbiAgICAgIFwidHNkb2Mvc3ludGF4XCI6IFwib2ZmXCJcbiAgICB9LFxuICAgIFwiaWdub3JlUGF0dGVybnNcIjogW1xuICAgICAgXCJqZXN0LnNldHVwLnRzXCJcbiAgICBdXG4gIH0sXG4gIFwiZGV2RGVwZW5kZW5jaWVzXCI6IHtcbiAgICBcIkBzd2MvY29yZVwiOiBcIl4xLjkuMlwiLFxuICAgIFwiQHRlc3RpbmctbGlicmFyeS9qZXN0LWRvbVwiOiBcIl42LjYuM1wiLFxuICAgIFwiQHRlc3RpbmctbGlicmFyeS9yZWFjdFwiOiBcIl4xNi4wLjFcIixcbiAgICBcIkB0eXBlcy9ub2RlXCI6IFwiXjIyLjkuMFwiLFxuICAgIFwiQHR5cGVzL3JlYWN0XCI6IFwiXjE4LjMuMTJcIixcbiAgICBcIkB2ZXJjZWwvZXNsaW50LWNvbmZpZ1wiOiBcIndvcmtzcGFjZTowLjAuMFwiLFxuICAgIFwic2VydmVyLW9ubHlcIjogXCJeMC4wLjFcIixcbiAgICBcInN2ZWx0ZVwiOiBcIl41LjEuMTBcIixcbiAgICBcInRzdXBcIjogXCI4LjMuNVwiLFxuICAgIFwidml0ZXN0XCI6IFwiXjIuMS41XCIsXG4gICAgXCJ2dWVcIjogXCJeMy41LjEyXCIsXG4gICAgXCJ2dWUtcm91dGVyXCI6IFwiXjQuNC41XCJcbiAgfSxcbiAgXCJwZWVyRGVwZW5kZW5jaWVzXCI6IHtcbiAgICBcIkByZW1peC1ydW4vcmVhY3RcIjogXCJeMlwiLFxuICAgIFwiQHN2ZWx0ZWpzL2tpdFwiOiBcIl4xIHx8IF4yXCIsXG4gICAgXCJuZXh0XCI6IFwiPj0gMTNcIixcbiAgICBcInJlYWN0XCI6IFwiXjE4IHx8IF4xOSB8fCBeMTkuMC4wLXJjXCIsXG4gICAgXCJzdmVsdGVcIjogXCI+PSA0XCIsXG4gICAgXCJ2dWVcIjogXCJeM1wiLFxuICAgIFwidnVlLXJvdXRlclwiOiBcIl40XCJcbiAgfSxcbiAgXCJwZWVyRGVwZW5kZW5jaWVzTWV0YVwiOiB7XG4gICAgXCJAcmVtaXgtcnVuL3JlYWN0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJAc3ZlbHRlanMva2l0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJuZXh0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJyZWFjdFwiOiB7XG4gICAgICBcIm9wdGlvbmFsXCI6IHRydWVcbiAgICB9LFxuICAgIFwic3ZlbHRlXCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJ2dWVcIjoge1xuICAgICAgXCJvcHRpb25hbFwiOiB0cnVlXG4gICAgfSxcbiAgICBcInZ1ZS1yb3V0ZXJcIjoge1xuICAgICAgXCJvcHRpb25hbFwiOiB0cnVlXG4gICAgfVxuICB9XG59XG4iLCJleHBvcnQgY29uc3QgaW5pdFF1ZXVlID0gKCk6IHZvaWQgPT4ge1xuICAvLyBpbml0aWFsaXplIHZhIHVudGlsIHNjcmlwdCBpcyBsb2FkZWRcbiAgaWYgKHdpbmRvdy52YSkgcmV0dXJuO1xuXG4gIHdpbmRvdy52YSA9IGZ1bmN0aW9uIGEoLi4ucGFyYW1zKTogdm9pZCB7XG4gICAgKHdpbmRvdy52YXEgPSB3aW5kb3cudmFxIHx8IFtdKS5wdXNoKHBhcmFtcyk7XG4gIH07XG59O1xuIiwiaW1wb3J0IHR5cGUgeyBBbGxvd2VkUHJvcGVydHlWYWx1ZXMsIEFuYWx5dGljc1Byb3BzLCBNb2RlIH0gZnJvbSAnLi90eXBlcyc7XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0Jyb3dzZXIoKTogYm9vbGVhbiB7XG4gIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJztcbn1cblxuZnVuY3Rpb24gZGV0ZWN0RW52aXJvbm1lbnQoKTogJ2RldmVsb3BtZW50JyB8ICdwcm9kdWN0aW9uJyB7XG4gIHRyeSB7XG4gICAgY29uc3QgZW52ID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlY7XG4gICAgaWYgKGVudiA9PT0gJ2RldmVsb3BtZW50JyB8fCBlbnYgPT09ICd0ZXN0Jykge1xuICAgICAgcmV0dXJuICdkZXZlbG9wbWVudCc7XG4gICAgfVxuICB9IGNhdGNoIChlKSB7XG4gICAgLy8gZG8gbm90aGluZywgdGhpcyBpcyBva2F5XG4gIH1cbiAgcmV0dXJuICdwcm9kdWN0aW9uJztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNldE1vZGUobW9kZTogTW9kZSA9ICdhdXRvJyk6IHZvaWQge1xuICBpZiAobW9kZSA9PT0gJ2F1dG8nKSB7XG4gICAgd2luZG93LnZhbSA9IGRldGVjdEVudmlyb25tZW50KCk7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgd2luZG93LnZhbSA9IG1vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRNb2RlKCk6IE1vZGUge1xuICBjb25zdCBtb2RlID0gaXNCcm93c2VyKCkgPyB3aW5kb3cudmFtIDogZGV0ZWN0RW52aXJvbm1lbnQoKTtcbiAgcmV0dXJuIG1vZGUgfHwgJ3Byb2R1Y3Rpb24nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNQcm9kdWN0aW9uKCk6IGJvb2xlYW4ge1xuICByZXR1cm4gZ2V0TW9kZSgpID09PSAncHJvZHVjdGlvbic7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0RldmVsb3BtZW50KCk6IGJvb2xlYW4ge1xuICByZXR1cm4gZ2V0TW9kZSgpID09PSAnZGV2ZWxvcG1lbnQnO1xufVxuXG5mdW5jdGlvbiByZW1vdmVLZXkoXG4gIGtleTogc3RyaW5nLFxuICB7IFtrZXldOiBfLCAuLi5yZXN0IH1cbik6IFJlY29yZDxzdHJpbmcsIHVua25vd24+IHtcbiAgcmV0dXJuIHJlc3Q7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZVByb3BlcnRpZXMoXG4gIHByb3BlcnRpZXM6IFJlY29yZDxzdHJpbmcsIHVua25vd24+IHwgdW5kZWZpbmVkLFxuICBvcHRpb25zOiB7XG4gICAgc3RyaXA/OiBib29sZWFuO1xuICB9XG4pOiBFcnJvciB8IFJlY29yZDxzdHJpbmcsIEFsbG93ZWRQcm9wZXJ0eVZhbHVlcz4gfCB1bmRlZmluZWQge1xuICBpZiAoIXByb3BlcnRpZXMpIHJldHVybiB1bmRlZmluZWQ7XG4gIGxldCBwcm9wcyA9IHByb3BlcnRpZXM7XG4gIGNvbnN0IGVycm9yUHJvcGVydGllczogc3RyaW5nW10gPSBbXTtcbiAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMocHJvcGVydGllcykpIHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiB2YWx1ZSAhPT0gbnVsbCkge1xuICAgICAgaWYgKG9wdGlvbnMuc3RyaXApIHtcbiAgICAgICAgcHJvcHMgPSByZW1vdmVLZXkoa2V5LCBwcm9wcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBlcnJvclByb3BlcnRpZXMucHVzaChrZXkpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGlmIChlcnJvclByb3BlcnRpZXMubGVuZ3RoID4gMCAmJiAhb3B0aW9ucy5zdHJpcCkge1xuICAgIHRocm93IEVycm9yKFxuICAgICAgYFRoZSBmb2xsb3dpbmcgcHJvcGVydGllcyBhcmUgbm90IHZhbGlkOiAke2Vycm9yUHJvcGVydGllcy5qb2luKFxuICAgICAgICAnLCAnXG4gICAgICApfS4gT25seSBzdHJpbmdzLCBudW1iZXJzLCBib29sZWFucywgYW5kIG51bGwgYXJlIGFsbG93ZWQuYFxuICAgICk7XG4gIH1cbiAgcmV0dXJuIHByb3BzIGFzIFJlY29yZDxzdHJpbmcsIEFsbG93ZWRQcm9wZXJ0eVZhbHVlcz47XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjb21wdXRlUm91dGUoXG4gIHBhdGhuYW1lOiBzdHJpbmcgfCBudWxsLFxuICBwYXRoUGFyYW1zOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmcgfCBzdHJpbmdbXT4gfCBudWxsXG4pOiBzdHJpbmcgfCBudWxsIHtcbiAgaWYgKCFwYXRobmFtZSB8fCAhcGF0aFBhcmFtcykge1xuICAgIHJldHVybiBwYXRobmFtZTtcbiAgfVxuXG4gIGxldCByZXN1bHQgPSBwYXRobmFtZTtcbiAgdHJ5IHtcbiAgICBjb25zdCBlbnRyaWVzID0gT2JqZWN0LmVudHJpZXMocGF0aFBhcmFtcyk7XG4gICAgLy8gc2ltcGxlIGtleXMgbXVzdCBiZSBoYW5kbGVkIGZpcnN0XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgZW50cmllcykge1xuICAgICAgaWYgKCFBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICBjb25zdCBtYXRjaGVyID0gdHVyblZhbHVlVG9SZWdFeHAodmFsdWUpO1xuICAgICAgICBpZiAobWF0Y2hlci50ZXN0KHJlc3VsdCkpIHtcbiAgICAgICAgICByZXN1bHQgPSByZXN1bHQucmVwbGFjZShtYXRjaGVyLCBgL1ske2tleX1dYCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgLy8gYXJyYXkgdmFsdWVzIG5leHRcbiAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBlbnRyaWVzKSB7XG4gICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgY29uc3QgbWF0Y2hlciA9IHR1cm5WYWx1ZVRvUmVnRXhwKHZhbHVlLmpvaW4oJy8nKSk7XG4gICAgICAgIGlmIChtYXRjaGVyLnRlc3QocmVzdWx0KSkge1xuICAgICAgICAgIHJlc3VsdCA9IHJlc3VsdC5yZXBsYWNlKG1hdGNoZXIsIGAvWy4uLiR7a2V5fV1gKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9IGNhdGNoIChlKSB7XG4gICAgcmV0dXJuIHBhdGhuYW1lO1xuICB9XG59XG5cbmZ1bmN0aW9uIHR1cm5WYWx1ZVRvUmVnRXhwKHZhbHVlOiBzdHJpbmcpOiBSZWdFeHAge1xuICByZXR1cm4gbmV3IFJlZ0V4cChgLyR7ZXNjYXBlUmVnRXhwKHZhbHVlKX0oPz1bLz8jXXwkKWApO1xufVxuXG5mdW5jdGlvbiBlc2NhcGVSZWdFeHAoc3RyaW5nOiBzdHJpbmcpOiBzdHJpbmcge1xuICByZXR1cm4gc3RyaW5nLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXF1cXFxcXS9nLCAnXFxcXCQmJyk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRTY3JpcHRTcmMoXG4gIHByb3BzOiBBbmFseXRpY3NQcm9wcyAmIHsgYmFzZVBhdGg/OiBzdHJpbmcgfVxuKTogc3RyaW5nIHtcbiAgaWYgKHByb3BzLnNjcmlwdFNyYykge1xuICAgIHJldHVybiBwcm9wcy5zY3JpcHRTcmM7XG4gIH1cbiAgaWYgKGlzRGV2ZWxvcG1lbnQoKSkge1xuICAgIHJldHVybiAnaHR0cHM6Ly92YS52ZXJjZWwtc2NyaXB0cy5jb20vdjEvc2NyaXB0LmRlYnVnLmpzJztcbiAgfVxuICBpZiAocHJvcHMuYmFzZVBhdGgpIHtcbiAgICByZXR1cm4gYCR7cHJvcHMuYmFzZVBhdGh9L2luc2lnaHRzL3NjcmlwdC5qc2A7XG4gIH1cbiAgcmV0dXJuICcvX3ZlcmNlbC9pbnNpZ2h0cy9zY3JpcHQuanMnO1xufVxuIiwiaW1wb3J0IHsgbmFtZSBhcyBwYWNrYWdlTmFtZSwgdmVyc2lvbiB9IGZyb20gJy4uL3BhY2thZ2UuanNvbic7XG5pbXBvcnQgeyBpbml0UXVldWUgfSBmcm9tICcuL3F1ZXVlJztcbmltcG9ydCB0eXBlIHtcbiAgQWxsb3dlZFByb3BlcnR5VmFsdWVzLFxuICBBbmFseXRpY3NQcm9wcyxcbiAgRmxhZ3NEYXRhSW5wdXQsXG4gIEJlZm9yZVNlbmQsXG4gIEJlZm9yZVNlbmRFdmVudCxcbn0gZnJvbSAnLi90eXBlcyc7XG5pbXBvcnQge1xuICBpc0Jyb3dzZXIsXG4gIHBhcnNlUHJvcGVydGllcyxcbiAgc2V0TW9kZSxcbiAgaXNEZXZlbG9wbWVudCxcbiAgaXNQcm9kdWN0aW9uLFxuICBjb21wdXRlUm91dGUsXG4gIGdldFNjcmlwdFNyYyxcbn0gZnJvbSAnLi91dGlscyc7XG5cbi8qKlxuICogSW5qZWN0cyB0aGUgVmVyY2VsIFdlYiBBbmFseXRpY3Mgc2NyaXB0IGludG8gdGhlIHBhZ2UgaGVhZCBhbmQgc3RhcnRzIHRyYWNraW5nIHBhZ2Ugdmlld3MuIFJlYWQgbW9yZSBpbiBvdXIgW2RvY3VtZW50YXRpb25dKGh0dHBzOi8vdmVyY2VsLmNvbS9kb2NzL2NvbmNlcHRzL2FuYWx5dGljcy9wYWNrYWdlKS5cbiAqIEBwYXJhbSBbcHJvcHNdIC0gQW5hbHl0aWNzIG9wdGlvbnMuXG4gKiBAcGFyYW0gW3Byb3BzLm1vZGVdIC0gVGhlIG1vZGUgdG8gdXNlIGZvciB0aGUgYW5hbHl0aWNzIHNjcmlwdC4gRGVmYXVsdHMgdG8gYGF1dG9gLlxuICogIC0gYGF1dG9gIC0gQXV0b21hdGljYWxseSBkZXRlY3QgdGhlIGVudmlyb25tZW50LiAgVXNlcyBgcHJvZHVjdGlvbmAgaWYgdGhlIGVudmlyb25tZW50IGNhbm5vdCBiZSBkZXRlcm1pbmVkLlxuICogIC0gYHByb2R1Y3Rpb25gIC0gQWx3YXlzIHVzZSB0aGUgcHJvZHVjdGlvbiBzY3JpcHQuIChTZW5kcyBldmVudHMgdG8gdGhlIHNlcnZlcilcbiAqICAtIGBkZXZlbG9wbWVudGAgLSBBbHdheXMgdXNlIHRoZSBkZXZlbG9wbWVudCBzY3JpcHQuIChMb2dzIGV2ZW50cyB0byB0aGUgY29uc29sZSlcbiAqIEBwYXJhbSBbcHJvcHMuZGVidWddIC0gV2hldGhlciB0byBlbmFibGUgZGVidWcgbG9nZ2luZyBpbiBkZXZlbG9wbWVudC4gRGVmYXVsdHMgdG8gYHRydWVgLlxuICogQHBhcmFtIFtwcm9wcy5iZWZvcmVTZW5kXSAtIEEgbWlkZGxld2FyZSBmdW5jdGlvbiB0byBtb2RpZnkgZXZlbnRzIGJlZm9yZSB0aGV5IGFyZSBzZW50LiBTaG91bGQgcmV0dXJuIHRoZSBldmVudCBvYmplY3Qgb3IgYG51bGxgIHRvIGNhbmNlbCB0aGUgZXZlbnQuXG4gKiBAcGFyYW0gW3Byb3BzLmRzbl0gLSBUaGUgRFNOIG9mIHRoZSBwcm9qZWN0IHRvIHNlbmQgZXZlbnRzIHRvLiBPbmx5IHJlcXVpcmVkIHdoZW4gc2VsZi1ob3N0aW5nLlxuICogQHBhcmFtIFtwcm9wcy5kaXNhYmxlQXV0b1RyYWNrXSAtIFdoZXRoZXIgdGhlIGluamVjdGVkIHNjcmlwdCBzaG91bGQgdHJhY2sgcGFnZSB2aWV3cyBmcm9tIHB1c2hTdGF0ZSBldmVudHMuIERpc2FibGUgaWYgcm91dGUgaXMgdXBkYXRlZCBhZnRlciBwdXNoU3RhdGUsIGEgbWFudWFsbHkgY2FsbCBwYWdlIHBhZ2V2aWV3KCkuXG4gKi9cbmZ1bmN0aW9uIGluamVjdChcbiAgcHJvcHM6IEFuYWx5dGljc1Byb3BzICYge1xuICAgIGZyYW1ld29yaz86IHN0cmluZztcbiAgICBkaXNhYmxlQXV0b1RyYWNrPzogYm9vbGVhbjtcbiAgICBiYXNlUGF0aD86IHN0cmluZztcbiAgfSA9IHtcbiAgICBkZWJ1ZzogdHJ1ZSxcbiAgfVxuKTogdm9pZCB7XG4gIGlmICghaXNCcm93c2VyKCkpIHJldHVybjtcblxuICBzZXRNb2RlKHByb3BzLm1vZGUpO1xuXG4gIGluaXRRdWV1ZSgpO1xuXG4gIGlmIChwcm9wcy5iZWZvcmVTZW5kKSB7XG4gICAgd2luZG93LnZhPy4oJ2JlZm9yZVNlbmQnLCBwcm9wcy5iZWZvcmVTZW5kKTtcbiAgfVxuXG4gIGNvbnN0IHNyYyA9IGdldFNjcmlwdFNyYyhwcm9wcyk7XG5cbiAgaWYgKGRvY3VtZW50LmhlYWQucXVlcnlTZWxlY3Rvcihgc2NyaXB0W3NyYyo9XCIke3NyY31cIl1gKSkgcmV0dXJuO1xuXG4gIGNvbnN0IHNjcmlwdCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NjcmlwdCcpO1xuICBzY3JpcHQuc3JjID0gc3JjO1xuICBzY3JpcHQuZGVmZXIgPSB0cnVlO1xuICBzY3JpcHQuZGF0YXNldC5zZGtuID1cbiAgICBwYWNrYWdlTmFtZSArIChwcm9wcy5mcmFtZXdvcmsgPyBgLyR7cHJvcHMuZnJhbWV3b3JrfWAgOiAnJyk7XG4gIHNjcmlwdC5kYXRhc2V0LnNka3YgPSB2ZXJzaW9uO1xuXG4gIGlmIChwcm9wcy5kaXNhYmxlQXV0b1RyYWNrKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQuZGlzYWJsZUF1dG9UcmFjayA9ICcxJztcbiAgfVxuICBpZiAocHJvcHMuZW5kcG9pbnQpIHtcbiAgICBzY3JpcHQuZGF0YXNldC5lbmRwb2ludCA9IHByb3BzLmVuZHBvaW50O1xuICB9IGVsc2UgaWYgKHByb3BzLmJhc2VQYXRoKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQuZW5kcG9pbnQgPSBgJHtwcm9wcy5iYXNlUGF0aH0vaW5zaWdodHNgO1xuICB9XG4gIGlmIChwcm9wcy5kc24pIHtcbiAgICBzY3JpcHQuZGF0YXNldC5kc24gPSBwcm9wcy5kc247XG4gIH1cblxuICBzY3JpcHQub25lcnJvciA9ICgpOiB2b2lkID0+IHtcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBpc0RldmVsb3BtZW50KClcbiAgICAgID8gJ1BsZWFzZSBjaGVjayBpZiBhbnkgYWQgYmxvY2tlcnMgYXJlIGVuYWJsZWQgYW5kIHRyeSBhZ2Fpbi4nXG4gICAgICA6ICdCZSBzdXJlIHRvIGVuYWJsZSBXZWIgQW5hbHl0aWNzIGZvciB5b3VyIHByb2plY3QgYW5kIGRlcGxveSBhZ2Fpbi4gU2VlIGh0dHBzOi8vdmVyY2VsLmNvbS9kb2NzL2FuYWx5dGljcy9xdWlja3N0YXJ0IGZvciBtb3JlIGluZm9ybWF0aW9uLic7XG5cbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc29sZSAtLSBMb2dnaW5nIHRvIGNvbnNvbGUgaXMgaW50ZW50aW9uYWxcbiAgICBjb25zb2xlLmxvZyhcbiAgICAgIGBbVmVyY2VsIFdlYiBBbmFseXRpY3NdIEZhaWxlZCB0byBsb2FkIHNjcmlwdCBmcm9tICR7c3JjfS4gJHtlcnJvck1lc3NhZ2V9YFxuICAgICk7XG4gIH07XG5cbiAgaWYgKGlzRGV2ZWxvcG1lbnQoKSAmJiBwcm9wcy5kZWJ1ZyA9PT0gZmFsc2UpIHtcbiAgICBzY3JpcHQuZGF0YXNldC5kZWJ1ZyA9ICdmYWxzZSc7XG4gIH1cblxuICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKHNjcmlwdCk7XG59XG5cbi8qKlxuICogVHJhY2tzIGEgY3VzdG9tIGV2ZW50LiBQbGVhc2UgcmVmZXIgdG8gdGhlIFtkb2N1bWVudGF0aW9uXShodHRwczovL3ZlcmNlbC5jb20vZG9jcy9jb25jZXB0cy9hbmFseXRpY3MvY3VzdG9tLWV2ZW50cykgZm9yIG1vcmUgaW5mb3JtYXRpb24gb24gY3VzdG9tIGV2ZW50cy5cbiAqIEBwYXJhbSBuYW1lIC0gVGhlIG5hbWUgb2YgdGhlIGV2ZW50LlxuICogKiBFeGFtcGxlczogYFB1cmNoYXNlYCwgYENsaWNrIEJ1dHRvbmAsIG9yIGBQbGF5IFZpZGVvYC5cbiAqIEBwYXJhbSBbcHJvcGVydGllc10gLSBBZGRpdGlvbmFsIHByb3BlcnRpZXMgb2YgdGhlIGV2ZW50LiBOZXN0ZWQgb2JqZWN0cyBhcmUgbm90IHN1cHBvcnRlZC4gQWxsb3dlZCB2YWx1ZXMgYXJlIGBzdHJpbmdgLCBgbnVtYmVyYCwgYGJvb2xlYW5gLCBhbmQgYG51bGxgLlxuICovXG5mdW5jdGlvbiB0cmFjayhcbiAgbmFtZTogc3RyaW5nLFxuICBwcm9wZXJ0aWVzPzogUmVjb3JkPHN0cmluZywgQWxsb3dlZFByb3BlcnR5VmFsdWVzPixcbiAgb3B0aW9ucz86IHtcbiAgICBmbGFncz86IEZsYWdzRGF0YUlucHV0O1xuICB9XG4pOiB2b2lkIHtcbiAgaWYgKCFpc0Jyb3dzZXIoKSkge1xuICAgIGNvbnN0IG1zZyA9XG4gICAgICAnW1ZlcmNlbCBXZWIgQW5hbHl0aWNzXSBQbGVhc2UgaW1wb3J0IGB0cmFja2AgZnJvbSBgQHZlcmNlbC9hbmFseXRpY3Mvc2VydmVyYCB3aGVuIHVzaW5nIHRoaXMgZnVuY3Rpb24gaW4gYSBzZXJ2ZXIgZW52aXJvbm1lbnQnO1xuXG4gICAgaWYgKGlzUHJvZHVjdGlvbigpKSB7XG4gICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc29sZSAtLSBTaG93IHdhcm5pbmcgaW4gcHJvZHVjdGlvblxuICAgICAgY29uc29sZS53YXJuKG1zZyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihtc2cpO1xuICAgIH1cblxuICAgIHJldHVybjtcbiAgfVxuXG4gIGlmICghcHJvcGVydGllcykge1xuICAgIHdpbmRvdy52YT8uKCdldmVudCcsIHsgbmFtZSwgb3B0aW9ucyB9KTtcbiAgICByZXR1cm47XG4gIH1cblxuICB0cnkge1xuICAgIGNvbnN0IHByb3BzID0gcGFyc2VQcm9wZXJ0aWVzKHByb3BlcnRpZXMsIHtcbiAgICAgIHN0cmlwOiBpc1Byb2R1Y3Rpb24oKSxcbiAgICB9KTtcblxuICAgIHdpbmRvdy52YT8uKCdldmVudCcsIHtcbiAgICAgIG5hbWUsXG4gICAgICBkYXRhOiBwcm9wcyxcbiAgICAgIG9wdGlvbnMsXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycikge1xuICAgIGlmIChlcnIgaW5zdGFuY2VvZiBFcnJvciAmJiBpc0RldmVsb3BtZW50KCkpIHtcbiAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zb2xlIC0tIExvZ2dpbmcgdG8gY29uc29sZSBpcyBpbnRlbnRpb25hbFxuICAgICAgY29uc29sZS5lcnJvcihlcnIpO1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBwYWdldmlldyh7XG4gIHJvdXRlLFxuICBwYXRoLFxufToge1xuICByb3V0ZT86IHN0cmluZyB8IG51bGw7XG4gIHBhdGg/OiBzdHJpbmc7XG59KTogdm9pZCB7XG4gIHdpbmRvdy52YT8uKCdwYWdldmlldycsIHsgcm91dGUsIHBhdGggfSk7XG59XG5cbmV4cG9ydCB7IGluamVjdCwgdHJhY2ssIHBhZ2V2aWV3LCBjb21wdXRlUm91dGUgfTtcbmV4cG9ydCB0eXBlIHsgQW5hbHl0aWNzUHJvcHMsIEJlZm9yZVNlbmQsIEJlZm9yZVNlbmRFdmVudCB9O1xuXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgaW1wb3J0L25vLWRlZmF1bHQtZXhwb3J0IC0tIERlZmF1bHQgZXhwb3J0IGlzIGludGVudGlvbmFsXG5leHBvcnQgZGVmYXVsdCB7XG4gIGluamVjdCxcbiAgdHJhY2ssXG4gIGNvbXB1dGVSb3V0ZSxcbn07XG4iLCJleHBvcnQgZnVuY3Rpb24gZ2V0QmFzZVBhdGgoKTogc3RyaW5nIHwgdW5kZWZpbmVkIHtcbiAgLy8gISEgaW1wb3J0YW50ICEhXG4gIC8vIGRvIG5vdCBhY2Nlc3MgZW52IHZhcmlhYmxlcyB1c2luZyBwcm9jZXNzLmVudlt2YXJuYW1lXVxuICAvLyBzb21lIGJ1bmRsZXMgd29uJ3QgcmVwbGFjZSB0aGUgdmFsdWUgYXQgYnVpbGQgdGltZS5cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9wcmVmZXItb3B0aW9uYWwtY2hhaW4gLS0gd2UgY2FuJ3QgdXNlIG9wdGlvbm5hbCBoZXJlLCBpdCdsbCBicmVhayBpZiBwcm9jZXNzIGRvZXMgbm90IGV4aXN0LlxuICBpZiAodHlwZW9mIHByb2Nlc3MgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBwcm9jZXNzLmVudiA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG4gIHJldHVybiBwcm9jZXNzLmVudi5SRUFDVF9BUFBfVkVSQ0VMX09CU0VSVkFCSUxJVFlfQkFTRVBBVEg7XG59XG4iLCIndXNlIGNsaWVudCc7XG4vKiBlc2xpbnQtZGlzYWJsZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5uZWNlc3NhcnktY29uZGl0aW9uIC0tIGNhbiBiZSBlbXB0eSBpbiBwYWdlcyByb3V0ZXIgKi9cbmltcG9ydCB7IHVzZVBhcmFtcywgdXNlUGF0aG5hbWUsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbi5qcyc7XG5pbXBvcnQgeyBjb21wdXRlUm91dGUgfSBmcm9tICcuLi91dGlscyc7XG5cbmV4cG9ydCBjb25zdCB1c2VSb3V0ZSA9ICgpOiB7XG4gIHJvdXRlOiBzdHJpbmcgfCBudWxsO1xuICBwYXRoOiBzdHJpbmc7XG59ID0+IHtcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKCk7XG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xuICBjb25zdCBwYXRoID0gdXNlUGF0aG5hbWUoKTtcblxuICAvLyBVbnRpbCB3ZSBoYXZlIHJvdXRlIHBhcmFtZXRlcnMsIHdlIGRvbid0IGNvbXB1dGUgdGhlIHJvdXRlXG4gIGlmICghcGFyYW1zKSB7XG4gICAgcmV0dXJuIHsgcm91dGU6IG51bGwsIHBhdGggfTtcbiAgfVxuICAvLyBpbiBOZXh0LmpzQDEzLCB1c2VQYXJhbXMoKSBjb3VsZCByZXR1cm4gYW4gZW1wdHkgb2JqZWN0IGZvciBwYWdlcyByb3V0ZXIsIGFuZCB3ZSBkZWZhdWx0IHRvIHNlYXJjaFBhcmFtcy5cbiAgY29uc3QgZmluYWxQYXJhbXMgPSBPYmplY3Qua2V5cyhwYXJhbXMpLmxlbmd0aFxuICAgID8gcGFyYW1zXG4gICAgOiBPYmplY3QuZnJvbUVudHJpZXMoc2VhcmNoUGFyYW1zLmVudHJpZXMoKSk7XG4gIHJldHVybiB7IHJvdXRlOiBjb21wdXRlUm91dGUocGF0aCwgZmluYWxQYXJhbXMpLCBwYXRoIH07XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gZ2V0QmFzZVBhdGgoKTogc3RyaW5nIHwgdW5kZWZpbmVkIHtcbiAgLy8gISEgaW1wb3J0YW50ICEhXG4gIC8vIGRvIG5vdCBhY2Nlc3MgZW52IHZhcmlhYmxlcyB1c2luZyBwcm9jZXNzLmVudlt2YXJuYW1lXVxuICAvLyBzb21lIGJ1bmRsZXMgd29uJ3QgcmVwbGFjZSB0aGUgdmFsdWUgYXQgYnVpbGQgdGltZS5cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9wcmVmZXItb3B0aW9uYWwtY2hhaW4gLS0gd2UgY2FuJ3QgdXNlIG9wdGlvbm5hbCBoZXJlLCBpdCdsbCBicmVhayBpZiBwcm9jZXNzIGRvZXMgbm90IGV4aXN0LlxuICBpZiAodHlwZW9mIHByb2Nlc3MgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBwcm9jZXNzLmVudiA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG4gIHJldHVybiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19WRVJDRUxfT0JTRVJWQUJJTElUWV9CQVNFUEFUSDtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlN1c3BlbnNlIiwidXNlRWZmZWN0IiwibmFtZSIsInZlcnNpb24iLCJpbml0UXVldWUiLCJ3aW5kb3ciLCJ2YSIsImEiLCJwYXJhbXMiLCJ2YXEiLCJwdXNoIiwiaXNCcm93c2VyIiwiZGV0ZWN0RW52aXJvbm1lbnQiLCJlbnYiLCJlIiwic2V0TW9kZSIsIm1vZGUiLCJ2YW0iLCJnZXRNb2RlIiwiaXNEZXZlbG9wbWVudCIsImNvbXB1dGVSb3V0ZSIsInBhdGhuYW1lIiwicGF0aFBhcmFtcyIsInJlc3VsdCIsImVudHJpZXMiLCJPYmplY3QiLCJrZXkiLCJ2YWx1ZSIsIkFycmF5IiwiaXNBcnJheSIsIm1hdGNoZXIiLCJ0dXJuVmFsdWVUb1JlZ0V4cCIsInRlc3QiLCJyZXBsYWNlIiwiam9pbiIsIlJlZ0V4cCIsImVzY2FwZVJlZ0V4cCIsInN0cmluZyIsImdldFNjcmlwdFNyYyIsInByb3BzIiwic2NyaXB0U3JjIiwiYmFzZVBhdGgiLCJpbmplY3QiLCJkZWJ1ZyIsIl9hIiwiYmVmb3JlU2VuZCIsImNhbGwiLCJzcmMiLCJkb2N1bWVudCIsImhlYWQiLCJxdWVyeVNlbGVjdG9yIiwic2NyaXB0IiwiY3JlYXRlRWxlbWVudCIsImRlZmVyIiwiZGF0YXNldCIsInNka24iLCJmcmFtZXdvcmsiLCJzZGt2IiwiZGlzYWJsZUF1dG9UcmFjayIsImVuZHBvaW50IiwiZHNuIiwib25lcnJvciIsImVycm9yTWVzc2FnZSIsImNvbnNvbGUiLCJsb2ciLCJhcHBlbmRDaGlsZCIsInBhZ2V2aWV3Iiwicm91dGUiLCJwYXRoIiwiZ2V0QmFzZVBhdGgiLCJwcm9jZXNzIiwiUkVBQ1RfQVBQX1ZFUkNFTF9PQlNFUlZBQklMSVRZX0JBU0VQQVRIIiwiQW5hbHl0aWNzIiwidXNlUGFyYW1zIiwidXNlUGF0aG5hbWUiLCJ1c2VTZWFyY2hQYXJhbXMiLCJ1c2VSb3V0ZSIsInNlYXJjaFBhcmFtcyIsImZpbmFsUGFyYW1zIiwia2V5cyIsImxlbmd0aCIsImZyb21FbnRyaWVzIiwiTkVYVF9QVUJMSUNfVkVSQ0VMX09CU0VSVkFCSUxJVFlfQkFTRVBBVEgiLCJBbmFseXRpY3NDb21wb25lbnQiLCJmYWxsYmFjayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936/node_modules/@vercel/analytics/dist/next/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936/node_modules/@vercel/analytics/dist/next/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936/node_modules/@vercel/analytics/dist/next/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\SKIDOS\Front-end-proj\nextjs-skidos-website\node_modules\.pnpm\@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936\node_modules\@vercel\analytics\dist\next\index.mjs`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\SKIDOS\Front-end-proj\nextjs-skidos-website\node_modules\.pnpm\@vercel+analytics@1.5.0_nex_af8f9571ab2f2f2d262619542cce4936\node_modules\@vercel\analytics\dist\next\index.mjs#Analytics`);


/***/ })

};
;