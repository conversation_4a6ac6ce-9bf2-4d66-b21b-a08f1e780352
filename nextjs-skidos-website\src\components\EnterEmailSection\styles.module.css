.bodyWrapper {
  width: 100%;
  background-color: #f9f9f9;
}

.getStartedWrapper {
  max-width: 480px;
  margin: 0 auto;
  text-align: center;
  background-color: #ffff;
}

.heading {
  margin-bottom: 0.5rem;
}

.subheading {
  margin: 0;
  font-size: 1.5rem;
  font-family: var(--font-poppins);
  color: #000000b2;
  font-weight: 500;
}

.bottomText {
  margin-top: 1.5rem;
}

.bottomText span {
  color: #0078ff;
}

.inputWrapper {
  margin-top: 2rem;
}

.enterEmailWrapper {
  width: 90%;
  margin: 0 auto;
}

.inputWrapper input {
  width: 100%;
  border-radius: 0.8rem;
  border: 0.5px solid #6c757d;
  padding: 0.8rem;
  font-size: 1.3rem;
  margin: 0 auto;
  box-sizing: border-box;
}

.errorEmailInput {
  border: 1.5px solid red !important;
}

.errorEmailInput::placeholder {
  color: red !important;
}

.inputWrapper input:focus {
  outline: none;
}

.inputWrapper input::placeholder {
  color: #6c757d;
  font-family: var(--font-poppins);
}

.inputCheckboxTextWrapper input[type="checkbox"] {
  display: none;
}

.inputCheckboxTextWrapper label {
  display: flex;
  flex-direction: column;
  color: #000000;
  font-family: var(--font-poppins);
  font-weight: 400;
  font-size: 0.9rem;
  cursor: pointer;
  padding-left: 40px; /* Increased to accommodate larger checkbox */
  position: relative;
  line-height: 1.4;
}

.inputCheckboxTextWrapper {
  margin-top: 1rem;
  text-align: left;
}

.inputCheckboxTextWrapper span {
  color: #003fb9;
  text-decoration: underline;
}

.inputCheckboxTextWrapper input[type="checkbox"] + label::before {
  content: "";
  position: absolute;
  left: 3px;
  top: 0px;
  width: 22px;
  height: 22px;
  border: 2px solid #ccc;
  border-radius: 4px;
  background-color: white;
  box-sizing: border-box;
}

.errorcheckBox input[type="checkbox"] + label::before {
  border: 2px solid red;
}

.inputCheckboxTextWrapper input[type="checkbox"]:checked + label::before {
  border: none;
  background-color: white; /* Keep background white */
}

.inputCheckboxTextWrapper input[type="checkbox"]:checked + label::after {
  content: "";
  position: absolute;
  left: 0px;
  top: -5px;
  width: 30px;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg width='31' height='30' viewBox='0 0 31 30' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.7615 20.3697L21.4671 12.6641C21.9362 12.195 21.9362 11.4344 21.4671 10.9653C20.9979 10.4961 20.2373 10.4961 19.7682 10.9653L13.7615 16.972L11.1525 14.363C10.6834 13.8939 9.92277 13.8939 9.45364 14.363C8.98451 14.8321 8.98451 15.5927 9.45364 16.0619L13.7615 20.3697ZM6.96602 26.1944C6.2986 26.1944 5.72726 25.9567 5.25198 25.4815C4.7767 25.0062 4.53906 24.4348 4.53906 23.7674V6.77876C4.53906 6.11135 4.7767 5.54 5.25198 5.06472C5.72726 4.58945 6.2986 4.35181 6.96602 4.35181H23.9547C24.6221 4.35181 25.1934 4.58945 25.6687 5.06472C26.144 5.54 26.3816 6.11135 26.3816 6.77876V23.7674C26.3816 24.4348 26.144 25.0062 25.6687 25.4815C25.1934 25.9567 24.6221 26.1944 23.9547 26.1944H6.96602ZM6.96602 23.7674H23.9547V6.77876H6.96602V23.7674Z' fill='%231DC368'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.infoWrapper {
  width: 100%;
  border-radius: 5px;
  background-color: #f8d7ce;
  font-family: var(--font-nevermind-light);
  color: #dd4a38;
  margin-top: 0.5rem;
  padding: 0.5rem 0;
}

.infoWrapper p {
  font-family: var(--font-nevermind-medium);
  margin: 0;
}

.infoWrapper span {
  color: #0169dd;
}

.infoText {
  font-size: 16px;
  margin-top: 1rem !important;
  margin-bottom: 0;
  color: #6c757d;
  font-family: var(--font-poppins);
  cursor: pointer;
}

@media only screen and (max-width: 480px) {
  .inputCheckboxTextWrapper {
    margin-top: 1rem;
  }
}

@media (max-width: 750px) {
  .getDtartedWrapper {
    max-width: 100%;
  }
}

.checkboxLabel {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.checkboxLabel div {
  display: inline-block;
}

.privacyLink {
  color: #003fb9;
  text-decoration: underline;
  display: inline;
  margin-left: 0;
}
