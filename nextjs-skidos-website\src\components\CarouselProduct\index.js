import { useAuth } from "@/context/AuthContext";
import useIsMobile from "@/hooks/useIsMobile";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import "swiper/css";
import "swiper/css/pagination";
import { Autoplay, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SideAvatar from "../../../public/images/product/Side_avatar.webp";
import SideIris from "../../../public/images/product/Side_IRIS.webp";
import SideMath from "../../../public/images/product/Side_math.webp";
import SideSel from "../../../public/images/product/Side_sel.webp";
import SideTracing from "../../../public/images/product/Side_tracing.webp";
import Loader from "../Loader";
import "./carousel.css";
import styles from "./styles.module.css";

export default function App() {
  const t = useTranslations("Carousel1");
  const isMobile = useIsMobile();
  const router = useRouter();
  const { isSubscribed } = useAuth();

  const handleRedirect = () => {
    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/acquisition");
    }
  };

  if (isMobile === null) return <Loader />;
  return (
    <>
      <Swiper
        spaceBetween={1}
        pagination={{
          clickable: true,
          el: ".custom-pagination",
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        modules={[Autoplay, Pagination]}
        className="mySwiper"
      >
        <SwiperSlide>
          {!isMobile ? (
            <div className={styles.carouselProductBackgroud}>
              <div className={styles.productTextWrapper}>
                <h2>{t("slides.math.title")}</h2>
                <p>{t("slides.math.description")}</p>
                <button className={styles.carouselBannerBtn} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div>
                <Image
                  alt={t("slides.math.imageAlt")}
                  src={SideMath}
                  className={styles.productBannerImg}
                  priority
                />
              </div>
            </div>
          ) : (
            <div className={styles.carouselProductBackgroudMb}>
              <div className={styles.productTextWrapperMb}>
                <h2>{t("slides.math.title")}</h2>
                <p>{t("slides.math.description")}</p>
                <button className={styles.carouselBannerBtnMb} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className={styles.carouselProductBackgroud2}>
              <div className={styles.productTextWrapper}>
                <h2>{t("slides.avatars.title")}</h2>
                <p>{t("slides.avatars.description")}</p>
                <button className={styles.carouselBannerBtn} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div>
                <Image
                  alt={t("slides.avatars.imageAlt")}
                  src={SideAvatar}
                  className={styles.productBannerImg}
                  priority
                />
              </div>
            </div>
          ) : (
            <div className={styles.carouselProductBackgroudMb2}>
              <div className={styles.productTextWrapperMb}>
                <h2>{t("slides.avatars.title")}</h2>
                <p>{t("slides.avatars.description")}</p>
                <button className={styles.carouselBannerBtnMb} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className={styles.carouselProductBackgroud3}>
              <div className={styles.productTextWrapper}>
                <h2>{t("slides.reading.title")}</h2>
                <p>{t("slides.reading.description")}</p>
                <button className={styles.carouselBannerBtn} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div>
                <Image
                  alt={t("slides.reading.imageAlt")}
                  src={SideIris}
                  className={styles.productBannerImg}
                  priority
                />
              </div>
            </div>
          ) : (
            <div className={styles.carouselProductBackgroudMb3}>
              <div className={styles.productTextWrapperMb}>
                <h2>{t("slides.reading.title")}</h2>
                <p>{t("slides.reading.description")}</p>
                <button className={styles.carouselBannerBtnMb} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className={styles.carouselProductBackgroud4}>
              <div className={styles.productTextWrapper}>
                <h2>{t("slides.emotional.title")}</h2>
                <p>{t("slides.emotional.description")}</p>
                <button className={styles.carouselBannerBtn} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div>
                <Image
                  alt={t("slides.emotional.imageAlt")}
                  src={SideSel}
                  className={styles.productBannerImg}
                  priority
                />
              </div>
            </div>
          ) : (
            <div className={styles.carouselProductBackgroudMb4}>
              <div className={styles.productTextWrapperMb}>
                <h2>{t("slides.emotional.title")}</h2>
                <p>{t("slides.emotional.description")}</p>
                <button className={styles.carouselBannerBtnMb} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>

        <SwiperSlide>
          {!isMobile ? (
            <div className={styles.carouselProductBackgroud5}>
              <div className={styles.productTextWrapper}>
                <h2>{t("slides.tracing.title")}</h2>
                <p>{t("slides.tracing.description")}</p>
                <button className={styles.carouselBannerBtn} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
              <div>
                <Image
                  alt={t("slides.tracing.imageAlt")}
                  src={SideTracing}
                  className={styles.productBannerImg}
                  priority
                />
              </div>
            </div>
          ) : (
            <div className={styles.carouselProductBackgroudMb5}>
              <div className={styles.productTextWrapperMb}>
                <h2>{t("slides.tracing.title")}</h2>
                <p>{t("slides.tracing.description")}</p>
                <button className={styles.carouselBannerBtnMb} onClick={handleRedirect}>
                  {t("common.getStarted")}
                </button>
              </div>
            </div>
          )}
        </SwiperSlide>
      </Swiper>
      <div className="parent-container">
        <div className="custom-pagination"></div>
      </div>
    </>
  );
}
