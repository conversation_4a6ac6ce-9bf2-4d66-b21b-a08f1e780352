.email-screen {
  font-weight: 600;
}
.email-screen .email-row {
  display: flex;
  align-items: center;
}
.email-screen .sel-logo {
  text-align: center;
}
.email-screen .sel-logo img {
  width: 175px;
}
.email-screen .hero-left {
  background: linear-gradient(180deg, #00c6a3 23.54%, #6fa0ff 133.59%),
    linear-gradient(0deg, #c5c5c5, #c5c5c5);
  margin-top: 10px;
  border-radius: 10px;
  padding: 35px 25px 75px 25px;
}
.email-screen .hero-left .hero-second {
  width: 280px;
  margin: auto;
  margin-bottom: 20px;
  margin-top: 10px;
}
.email-screen .hero-left .hero-second img {
  max-width: 100%;
}
.email-screen .hero-left h1 {
  font-weight: 700;
  color: #fff;
}
.email-screen .hero-left h4 {
  color: #fff;
  font-size: 1rem;
}
.email-screen .hero-left .highlight {
  background: rgba(255, 255, 255, 1);
  padding: 15px;
  border-radius: 10px;
}
.email-screen .hero-left .highlight p {
  margin-bottom: 0;
}
.email-screen .hero-right {
  padding: 25px 50px 0 75px;
}
.email-screen .hero-right h2 {
  font-weight: 700;
  font-size: 1.85rem;
}
.email-screen .hero-right h4 {
  font-size: 0.95rem;
  font-weight: 600;
}
.email-screen .hero-right h4 a {
  color: rgba(255, 102, 0, 1);
  text-decoration: none;
}
.email-screen .email-input {
  background-color: rgba(255, 238, 226, 1);
  outline: none;
  border: 1px solid rgba(255, 238, 226, 1);
  border-radius: 10px;
  padding: 12px 20px;
  width: 100%;
  color: #333;
  font-weight: 500;
  margin-top: 15px;
  margin-bottom: 20px;
  font-size: 0.95rem;
}
.email-screen .email-input:focus {
  border: 1px solid rgba(255, 102, 0, 1);
}
.email-screen .email-input::placeholder {
  color: #000;
}
.email-screen .email-input.invalid {
  border: 1px solid rgba(255, 0, 0, 1);
  background-color: rgba(255, 226, 226, 1);
}
.email-screen .email-input.invalid::placeholder {
  color: rgba(255, 0, 0, 1);
}
.email-screen .skidos-btn-primary {
  padding: 18px;
  border-bottom: 5px solid #b54608;
}
.email-screen .skidos-btn-primary.inactive {
  background-color: rgba(185, 185, 185, 1);
  color: #fff;
  border-color: rgba(67, 67, 67, 1);
}
.email-screen .tag {
  font-size: 1rem;
  color: rgba(185, 185, 185, 1);
  text-align: center;
}
.email-screen .tag img {
  width: 15px;
}
/*mobile*/
@media only screen and (max-device-width: 480px) {
  .email-screen .hero-right {
    padding: 25px 10px 15px 10px;
  }
}

/*tablet*/
@media only screen and (max-device-width: 960px) {
  .email-screen .hero-left .hero-second {
    width: 225px;
  }
  .email-screen .hero-right {
    padding: 25px 10px 15px 10px;
  }
}
