export const endPoint =
  "https://" + process.env.REACT_APP_API_ENV + ".skidos.com/apis/productservice/v1/";
export const getUser =
  "https://" + process.env.REACT_APP_API_ENV + ".skidos.com/apis/userservice/v1/user";
export const registerUser =
  "https://" + process.env.REACT_APP_API_ENV + ".skidos.com/apis/userservice/v1/register?version=5.0";
export const getPlayers =
  "https://" +
  process.env.REACT_APP_API_ENV +
  ".skidos.com/apis/userservice/v1/player?&version=" +
  process.env.REACT_APP_SDK_VERSION +
  "";
export const apiBasePath = "https://" + process.env.REACT_APP_API_ENV + ".skidos.com/apis/";
export const getGames = "games/instant?premium=";
export const getVideos = "videos/unathulist?version=";
export const avatarBaseUrl = "https://images.skidos.com/instant-games/";
export const appPath = "https://" + process.env.REACT_APP_BASE_PATH + "/app/";
export const homePage = "https://" + process.env.REACT_APP_BASE_PATH + "/";
export const getQuestionnaire =
  "https://" + process.env.REACT_APP_APIDEV + ".skidos.com/apis/productservice/v1/question";
export const sendQuestionnaireResponse =
  "https://" + process.env.REACT_APP_APIDEV + ".skidos.com/apis/productservice/v1/question/user";
export const refreshTokenUrl =
  "https://" + process.env.REACT_APP_API_ENV + ".skidos.com/apis/userservice/v1/refresh/token";
export const stripeKey = process.env.REACT_APP_STRIPE_KEY;
export const stripeBaseUrl =
  "https://" + process.env.REACT_APP_API_ENV + ".skidos.com/apis/subscriptionservice/v1";
