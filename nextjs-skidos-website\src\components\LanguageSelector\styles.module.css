.languageSelectorWrapper {
  /* border: 2px solid yellow; */
  cursor: pointer;
  margin-left: 1rem;
  display: flex;
  gap: 5px;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Dark background with some transparency */
  z-index: 1; /* Below the selector but above the rest of the content */
}

/* Language options container */
.languageOptionsToggleWrapper {
  position: fixed;
  top: 90px;
  right: 40px;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 3;
  width: 195px;
}

/* Style the list of languages */
.languageOptionsToggleWrapper ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.languageOptionsToggleWrapper li {
  font-family: var(--font-poppins);
  font-weight: 500;
  padding: 15px 0;
  cursor: pointer;
  border-bottom: 1px solid #666666;
  text-align: center;
}

.languageOptionsToggleWrapper li:last-child {
  border-bottom: none;
}
.selectedLanguage {
  color: #9258fe;
  font-weight: 700 !important;
}
@media (max-width: 820px) {
  .languageOptionsToggleWrapper {
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50%;
  }
  .languageSelectorWrapper {
    margin-top: 2rem;
  }
  .langTranslatorIcon {
    width: 42px;
    height: 40px;
  }
}
