/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: "https://skidos.com",
  generateRobotsTxt: true,
  changefreq: "daily",
  priority: 0.7,
  exclude: [
    "/404*",
    "/500*",
    "/api/*",
    "/_next/*",
    "/static/*",
    "/wp-content/*",
    "/wp-includes/*",
    "/wp-admin/*",
    "/wp-login.php",
    "/wp-json/*",
    // Add old paths to explicitly exclude them
    "/blog/*",
    "/article/*",
  ],
  transform: async (config, path) => {
    // Skip old paths that should be redirected
    if (path.startsWith("/blog/") || path.startsWith("/article/")) {
      return null;
    }

    const priority = path.startsWith("/blogs/")
      ? 0.8
      : path.startsWith("/news/")
        ? 0.8
        : path === "/"
          ? 1.0
          : 0.7;

    return {
      loc: path,
      changefreq: config.changefreq,
      priority,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async (config) => {
    return [
      { loc: "/blogs", changefreq: "daily", priority: 0.9 },
      { loc: "/news", changefreq: "daily", priority: 0.9 },
      // Add trailing slash versions explicitly
      { loc: "/blogs/", changefreq: "daily", priority: 0.9 },
      { loc: "/news/", changefreq: "daily", priority: 0.9 },
    ];
  },
  robotsTxtOptions: {
    policies: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/wp-content/*",
          "/wp-includes/*",
          "/wp-admin/*",
          "/wp-login.php",
          "/wp-json/*",
          // Explicitly disallow old paths
          "/blog/*",
          "/article/*",
        ],
      },
    ],
  },
  sitemapSize: 50000,
  autoLastmod: true,
};
