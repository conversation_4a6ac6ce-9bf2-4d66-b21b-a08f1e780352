import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getData } from "services/ApiHandler";
import { getUser } from "services/Constants";
import {
  appPath,
  getPlayers,
  avatarBaseUrl,
  homePage,
} from "services/Constants";

import logo from "icons/auth/logo.png";

export function Header() {
  let navigate = useNavigate();
  const getAPiData = getData;
  const [isMobileNav, setIsMobileNav] = useState(false);

  /*subscription status*/
  const [dataLoaded, setDataLoaded] = useState(false);
  const [isSubscribed, setIsSubscibed] = useState(false);
  const [userEmail, setUserEmail] = useState("");

  const [players, setPlayers] = useState([]);
  const [defaultAvatar, setDefaultAvatar] = useState("");
  const [defaultAvatarName, setDefaultAvatarName] = useState("");
  const getUserData = async () => {
    let auth_token = localStorage.getItem("token");
    let exist_avatar = localStorage.getItem("defaultAvatar");
    let exist_avatarName = localStorage.getItem("defaultAvatarName");
    if (auth_token) {
      let response = await getAPiData(getPlayers, auth_token);
      console.log("response", response);
      setPlayers(response?.players);
      if (response?.players[0]?.Nickname) {
        setDefaultAvatarName(response.players[0].Nickname);
      }
    }
    if (exist_avatar) {
      setDefaultAvatar(exist_avatar);
    }
    if (exist_avatarName) {
      setDefaultAvatarName(exist_avatarName);
    }
  };
  const updateAvatar = (src, avatarName) => {
    setDefaultAvatar(src);
    setDefaultAvatarName(avatarName);
    localStorage.setItem("defaultAvatar", src);
    localStorage.setItem("defaultAvatarName", avatarName);
  };
  const logout = () => {
    window.localStorage.clear();
    navigate("/");
    window.location.reload();
  };
  const validateAvatar = () => {
    if (players) {
      return (
        avatarBaseUrl +
        [players[0]?.AvatarName + "_" + players[0]?.AvatarIndex + ".png"]
      );
    } else {
      return avatarBaseUrl + "emma_2.png";
    }
  };
  const validateUser = async () => {
    let auth_token = localStorage.getItem("token");
    if (auth_token) {
      let response = await getAPiData(getUser, auth_token);
      setIsSubscibed(response?.isSubscribed);
      setUserEmail(response?.Email);
    }
  };
  useEffect(() => {
    validateUser();
    setDataLoaded(true);
    getUserData();
  }, []);
  return (
    <>
      <nav className="navbar header-navbar navbar-expand-lg">
        <div className="container">
          <a className="navbar-brand" href={homePage}>
            <img className="logo" src={logo} />
          </a>
          <button
            className="navbar-toggler"
            type="button"
            onClick={() => setIsMobileNav(!isMobileNav)}
          >
            <span className="navbar-toggler-icon"></span>
          </button>
          {!isMobileNav && (
            <div className="collapse navbar-collapse justify-content-end">
              <ul className="navbar-nav mb-2 mb-lg-0">
                <li className="nav-item">
                  <a className="nav-link active" href={homePage}>
                    Home
                  </a>
                </li>
                <li className="nav-item dropdown">
                  <a
                    className="nav-link dropdown-toggle"
                    href="#"
                    role="button"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    About
                  </a>
                  <ul className="dropdown-menu">
                    <li>
                      <a className="dropdown-item" href={homePage + "mascots/"}>
                        The SKIDOS Gang
                      </a>
                    </li>
                  </ul>
                </li>
                <li className="nav-item">
                  <a
                    className="nav-link active"
                    href="https://support.skidos.com"
                  >
                    Help
                  </a>
                </li>
                <li className="nav-item dropdown">
                  <a
                    className="nav-link dropdown-toggle"
                    href="#"
                    role="button"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    More
                  </a>
                  <ul className="dropdown-menu">
                    <li>
                      <a className="dropdown-item" href={homePage + "app/gift"}>
                        Gifts
                      </a>
                    </li>
                    <li>
                      <a className="dropdown-item" href={homePage + "blog/"}>
                        Blog
                      </a>
                    </li>
                    <li>
                      <a className="dropdown-item" href={homePage + "press/"}>
                        Press
                      </a>
                    </li>
                    <li>
                      <a className="dropdown-item" href={homePage + "career/"}>
                        Career
                      </a>
                    </li>
                    <li>
                      <a
                        className="dropdown-item"
                        href={homePage + "mission-vision/"}
                      >
                        Mission & Vision
                      </a>
                    </li>
                  </ul>
                </li>
                {!userEmail && (
                  <>
                    <li className="nav-item try-btn">
                      <a
                        className="nav-link"
                        // onClick={() => navigate("/subscription")}
                        href={homePage + "app/"}

                      >
                        Try for Free
                      </a>
                    </li>
                    <li className="nav-item">
                      <a className="nav-link" href={appPath + "login"}>
                        Sign in
                      </a>
                    </li>
                  </>
                )}
                {userEmail && !isSubscribed && (
                  <li className="nav-item upgrade-btn">
                    <a
                      className="nav-link"
                      onClick={() => navigate("/subscription")}
                    >
                      Upgrade
                    </a>
                  </li>
                )}
                {userEmail && (
                  <li className="nav-item dropdown profile-section">
                    <a
                      className="nav-link dropdown-toggle player-dropdown"
                      href="#"
                      role="button"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                    >
                      <img
                        className="avatar"
                        src={defaultAvatar ? defaultAvatar : validateAvatar()}
                      />
                      Hi,{" "}
                      {defaultAvatarName
                        ? defaultAvatarName
                        : userEmail.slice(0, 10) + "..."}
                    </a>
                    <div className="dropdown-menu">
                      <p className="pages">Switch Profiles</p>
                      {players?.map((el, index) => (
                        <div
                          className="profile-list"
                          key={"profile_" + index}
                          onClick={() =>
                            updateAvatar(
                              avatarBaseUrl +
                                [el.AvatarName + "_" + el.AvatarIndex + ".png"],
                              el.Nickname
                            )
                          }
                        >
                          <img
                            src={
                              avatarBaseUrl +
                              [el.AvatarName + "_" + el.AvatarIndex + ".png"]
                            }
                          />
                          <span className="player-name">{el.Nickname}</span>
                        </div>
                      ))}
                      <a href={appPath + "my-kids"} className="profile-list">
                        <span className="new-player"></span>
                        <span className="add-new">Add New</span>
                      </a>
                      {userEmail && (
                        <>
                          <p className="pages mt-5">SETTINGS</p>
                          <ul className="navbar-nav mr-auto">
                            <li className="nav-item mt-3">
                              <a
                                className="nav-link"
                                href={appPath + "profile"}
                              >
                                My Account
                              </a>
                            </li>
                            <li className="nav-item">
                              <a
                                className="nav-link"
                                href={appPath + "my-kids"}
                              >
                                Edit Player
                              </a>
                            </li>
                            <li className="nav-item">
                              <a className="nav-link" onClick={logout}>
                                Logout
                              </a>
                            </li>
                          </ul>
                        </>
                      )}
                    </div>
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
      </nav>
      {isMobileNav && (
        <div className="mobile-header-nav">
          <span
            className="close-btn"
            onClick={() => setIsMobileNav(false)}
          ></span>
          <div className="nav-block mt-4">
            {userEmail && (
              <>
                <div className="player-profile">
                  <span className="player-img">
                    <img
                      src={defaultAvatar ? defaultAvatar : validateAvatar()}
                    />
                  </span>
                  <span className="player-name">
                    Hi,{" "}
                    {defaultAvatarName
                      ? defaultAvatarName
                      : userEmail.slice(0, 10) + "..."}
                    <br />
                    <a href={appPath + "my-kids"}>Edit Player</a>
                  </span>
                </div>

                <p className="pages">Switch Profiles</p>
                {players?.map((el, index) => (
                  <div
                    className="profile-list"
                    key={"profile_" + index}
                    onClick={() =>
                      updateAvatar(
                        avatarBaseUrl +
                          [el.AvatarName + "_" + el.AvatarIndex + ".png"],
                        el.Nickname
                      )
                    }
                  >
                    <img
                      src={
                        avatarBaseUrl +
                        [el.AvatarName + "_" + el.AvatarIndex + ".png"]
                      }
                    />
                    <span className="player-name">{el.Nickname}</span>
                  </div>
                ))}
                <a href={appPath + "my-kids"} className="profile-list">
                  <span className="new-player"></span>
                  <span className="add-new">Add New</span>
                </a>
              </>
            )}
            {!userEmail && (
              <>
                <h3>Welcome to Skidos</h3>
                <a className="login-link" href={appPath + "ig_login"}>
                  Login
                </a>
              </>
            )}
            <p className="pages mt-5">PAGES</p>
            <ul className="navbar-nav mr-auto">
              <li className="nav-item">
                <a className="nav-link active" href={homePage}>
                  Home
                </a>
              </li>
              <li className="nav-item dropdown">
                <a
                  className="nav-link dropdown-toggle"
                  href="#"
                  role="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  About
                </a>
                <div className="dropdown-menu" aria-labelledby="navbarDropdown">
                  <a className="dropdown-item" href={homePage + "mascots/"}>
                    The SKIDOS Gang
                  </a>
                </div>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="https://support.skidos.com">
                  Help
                </a>
              </li>
              <li className="nav-item dropdown">
                <a
                  className="nav-link dropdown-toggle"
                  href="#"
                  role="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  More
                </a>
                <div className="dropdown-menu" aria-labelledby="navbarDropdown">
                  <a className="dropdown-item" href={homePage + "app/gift"}>
                    Gifts
                  </a>
                  <a className="dropdown-item" href={homePage + "blog/"}>
                    Blog
                  </a>
                  <a className="dropdown-item" href={homePage + "press/"}>
                    Press
                  </a>
                  <a className="dropdown-item" href={homePage + "career/"}>
                    Career
                  </a>
                  <a
                    className="dropdown-item"
                    href={homePage + "mission-vision/"}
                  >
                    Mission & Vision
                  </a>
                </div>
              </li>
            </ul>
            {userEmail && (
              <>
                <p className="pages mt-5">SETTINGS</p>
                <ul className="navbar-nav mr-auto">
                  <li className="nav-item">
                    <a className="nav-link" href={appPath + "profile"}>
                      My Account
                    </a>
                  </li>
                  <li className="nav-item">
                    <a className="nav-link" onClick={logout}>
                      Logout
                    </a>
                  </li>
                </ul>
              </>
            )}
          </div>
          {!userEmail && (
            <div className="try-free">
              <a href={homePage + "app/"}>Try for free</a>
            </div>
          )}
          {userEmail && !isSubscribed && (
            <div
              className="upgrade-btn"
              onClick={() => navigate("/subscription")}
            >
              <span>Upgrade</span>
            </div>
          )}
        </div>
      )}
    </>
  );
}

export default Header;
