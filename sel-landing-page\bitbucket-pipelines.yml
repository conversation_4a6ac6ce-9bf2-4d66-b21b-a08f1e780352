image: node:16

pipelines:
  branches:
    '{master,main}':
      - step:
          deployment: prod
          runs-on:
            - self.hosted
            - linux.arm64
          name: Build
          script:
            - |
              #!/usr/bin/env bash
              # Loop through repository variables
              for variable in ${BITBUCKET_REPO_VARIABLES}; do 
                # Export as environment variable
                export ${variable}="${!variable}"
              done
            - npm install
            - CI=false
            - npm run build
          caches:
            - node
          artifacts:
            - build/**
      
      - step:
          name: Deploy on Prod S3
          script:
            - pipe: atlassian/aws-s3-deploy:1.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $PROD_BUCKET_NAME
                ACL: "private"
                LOCAL_PATH: "build"

            - pipe: atlassian/aws-s3-deploy:1.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: ${PROD_BUCKET_NAME}/${PROD_BUCKET_SLUG}
                ACL: "private"
                LOCAL_PATH: "build"

      # - step:
      #     name: Invalidate Cloudfront Cache Prod
      #     script:
      #       - pipe: atlassian/aws-cloudfront-invalidate:0.10.0
      #         variables:
      #           AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
      #           AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
      #           AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
      #           DISTRIBUTION_ID: $PROD_DISTRIBUTION_ID


    qa:
      - step:
          deployment: qa
          runs-on:
            - self.hosted
            - linux.arm64
          name: Build
          script:
            - |
              #!/usr/bin/env bash
              # Loop through repository variables
              for variable in ${BITBUCKET_REPO_VARIABLES}; do 
                # Export as environment variable
                export ${variable}="${!variable}"
              done
            - npm install
            - CI=false
            - npm run build
          caches:
            - node
          artifacts:
            - build/**
      
      - step:
          name: Deploy on QA S3
          script:
            - pipe: atlassian/aws-s3-deploy:1.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: $QA_BUCKET_NAME
                ACL: "private"
                LOCAL_PATH: "build"

            - pipe: atlassian/aws-s3-deploy:1.6.0
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                S3_BUCKET: ${QA_BUCKET_NAME}/${QA_BUCKET_SLUG}
                ACL: "private"
                LOCAL_PATH: "build"

      # - step:
      #     name: Invalidate Cloudfront Cache QA
      #     script:
      #       - pipe: atlassian/aws-cloudfront-invalidate:0.10.0
      #         variables:
      #           AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
      #           AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
      #           AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
      #           DISTRIBUTION_ID: $QA_DISTRIBUTION_ID
