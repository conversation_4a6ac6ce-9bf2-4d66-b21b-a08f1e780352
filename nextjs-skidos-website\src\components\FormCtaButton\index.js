import styles from "./styles.module.css";

const FormCtaButton = ({ disabled = false, text, onClick, loading, customStyles = "" }) => {
  return (
    <button
      disabled={disabled}
      className={`${styles.formCtaButton} ${customStyles}`}
      onClick={onClick}
    >
      {loading ? (
        <div className={styles.loaderContainer}>
          <div className={styles.loader}></div>
        </div>
      ) : (
        text
      )}
    </button>
  );
};

export default FormCtaButton;
