import React from "react";
import PropTypes from "prop-types";
// external css
import "./Auth.css";

export function AuthPricingBlock(props) {
  return (
    <div className="auth_pricing_block" onClick={props.onClick}>
      <span className="apb_title">{props.title}</span>
      {props.offer && <span className="apb_offer">{props.offer}</span>}
      <div className="row">
        <div className="col-2 text-center">
          <span className="apb_radio">
            {props.isSelected && <span className="selected">&nbsp;</span>}
          </span>
        </div>
        <div className="col-6 padding-left-none">
          <div className="apb_price">
            <span className="currency">${props.monthlyPrice}</span>/month
          </div>
          <div className="apb_content topgap7">
            Billed yearly at ${props.annualPrice}
          </div>
        </div>
        <div className="col-4">
          <div className="apb_content topgap15">
            <b>7 days free trial</b>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthPricingBlock;

AuthPricingBlock.propTypes = {
  title: PropTypes.string.isRequired,
  offer: PropTypes.string,
  isSelected: PropTypes.bool.isRequired,
  monthlyPrice: PropTypes.string.isRequired,
  annualPrice: PropTypes.string.isRequired,
  onClick: PropTypes.string.isRequired,
};
