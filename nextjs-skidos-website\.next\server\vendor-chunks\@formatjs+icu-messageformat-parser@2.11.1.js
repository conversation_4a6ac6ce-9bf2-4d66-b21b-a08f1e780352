"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+icu-messageformat-parser@2.11.1";
exports.ids = ["vendor-chunks/@formatjs+icu-messageformat-parser@2.11.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isStructurallySame: () => (/* reexport safe */ _manipulator__WEBPACK_IMPORTED_MODULE_4__.isStructurallySame),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _manipulator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./manipulator */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hoistSelectors: () => (/* binding */ hoistSelectors),\n/* harmony export */   isStructurallySame: () => (/* binding */ isStructurallySame)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return (0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nfunction hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nfunction isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[type], \" vs \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjEvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtc2tpZG9zLXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjEvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcz9mZDNiIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEBnZW5lcmF0ZWQgZnJvbSByZWdleC1nZW4udHNcbmV4cG9ydCB2YXIgU1BBQ0VfU0VQQVJBVE9SX1JFR0VYID0gL1sgXFx4QTBcXHUxNjgwXFx1MjAwMC1cXHUyMDBBXFx1MjAyRlxcdTIwNUZcXHUzMDAwXS87XG5leHBvcnQgdmFyIFdISVRFX1NQQUNFX1JFR0VYID0gL1tcXHQtXFxyIFxceDg1XFx1MjAwRVxcdTIwMEZcXHUyMDI4XFx1MjAyOV0vO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isStructurallySame: () => (/* reexport safe */ _manipulator__WEBPACK_IMPORTED_MODULE_4__.isStructurallySame),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _manipulator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./manipulator */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hoistSelectors: () => (/* binding */ hoistSelectors),\n/* harmony export */   isStructurallySame: () => (/* binding */ isStructurallySame)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return (0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nfunction hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nfunction isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[type], \" vs \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjEvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtc2tpZG9zLXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlckAyLjExLjEvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3UtbWVzc2FnZWZvcm1hdC1wYXJzZXIvbGliL3JlZ2V4LmdlbmVyYXRlZC5qcz83YzhkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEBnZW5lcmF0ZWQgZnJvbSByZWdleC1nZW4udHNcbmV4cG9ydCB2YXIgU1BBQ0VfU0VQQVJBVE9SX1JFR0VYID0gL1sgXFx4QTBcXHUxNjgwXFx1MjAwMC1cXHUyMDBBXFx1MjAyRlxcdTIwNUZcXHUzMDAwXS87XG5leHBvcnQgdmFyIFdISVRFX1NQQUNFX1JFR0VYID0gL1tcXHQtXFxyIFxceDg1XFx1MjAwRVxcdTIwMEZcXHUyMDI4XFx1MjAyOV0vO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-messageformat-parser@2.11.1/node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n");

/***/ })

};
;