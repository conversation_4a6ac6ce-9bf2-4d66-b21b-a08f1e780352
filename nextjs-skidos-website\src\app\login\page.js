"use client";

import FormCtaButton from "@/components/FormCtaButton";
import Stepper from "@/components/Stepper";
import { useAuth } from "@/context/AuthContext";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import apiClient from "@/utils/axiosUtil";
import { getLocale } from "@/utils/helperFunctions";
import { webEngagelogin } from "@/utils/webengage";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useMemo, useState } from "react";
import styles from "./styles.module.css";

const isValidEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

const LoginPage = () => {
  const router = useRouter();
  const [formState, setFormState] = useState({
    email: "",
    password: "",
    showPassword: false,
    loading: false,
    errorMsg: "",
    isServerError: false,
  });
  const { login } = useAuth();
  const locale = useLocale();
  const t = useTranslations("LoginEmail");
  const lang = getLocale(locale);
  const validationResult = useMemo(() => {
    const { email, password } = formState;

    if (!email) return t("AllMandotry");
    if (!isValidEmail(email)) return t("EmailIncorrect");
    if (!password) return t("AllMandotry");

    return "";
  }, [formState.email, formState.password]);

  const handleInputChange = useCallback(
    (field) => (e) => {
      setFormState((prev) => ({
        ...prev,
        [field]: e.target.value,
        errorMsg: "",
        isServerError: false,
      }));
    },
    []
  );

  const togglePasswordVisibility = useCallback(() => {
    setFormState((prev) => ({
      ...prev,
      showPassword: !prev.showPassword,
    }));
  }, []);

  const submitForm = useCallback(
    async (e) => {
      e.preventDefault();

      if (validationResult) {
        setFormState((prev) => ({ ...prev, errorMsg: validationResult }));
        return;
      }

      try {
        setFormState((prev) => ({ ...prev, loading: true }));
        const url = `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/login?version=8.0&gameid=doctor&l=${lang}&platform=ios`;
        const response = await apiClient.post(`${url}`, {
          email: formState.email,
          password: formState.password,
        });

        if (response) {
          const auth_token = localStorage.getItem("auth_token");
          document.cookie = `token=${auth_token}; Path=/; Secure; SameSite=Strict; Max-Age=${process.env.NEXT_PUBLIC_COOKIE_EXPIRY}`;
          localStorage.setItem(
            "UserDetails",
            JSON.stringify({
              email: response.data.Email,
              expiryDate: response.data.SubscriptionExpire,
              isSubscribed: response.data.isSubscribed,
              ...response.data,
            })
          );
          login(response.data.isSubscribed);

          webEngagelogin({ email: response.data.Email, userId: response.data.Id });
          if (response.data.isSubscribed) {
            router.push("/user-home-screen");
          } else {
            router.push("/profile");
          }
        } else {
          setFormState((prev) => ({
            ...prev,
            loading: false,
            errorMsg: response.Message ?? "Login failed. Please check your credentials.",
            isServerError: true,
          }));
        }
      } catch (error) {
        setFormState((prev) => ({
          ...prev,
          loading: false,
          errorMsg: error.response.data.Message || "An error occurred. Please try again later.",
          isServerError: true,
        }));
      }
    },
    [formState.email, formState.password, validationResult, router]
  );

  const handleForgotPassword = useCallback(() => {
    router.push("/login-otp");
  }, [router]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  useTogglePinkFooter(true);

  return (
    <div className={styles.loginContainer}>
      <div className={styles.container}>
        <div style={{ width: "100%" }}>
          {" "}
          <Stepper currentStep={4} handleBack={handleBack} steps={[1, 2, 3, 4]} />
        </div>
        <h1 className={styles.title}>{t("Heading")}</h1>
        <form className={styles.form} onSubmit={submitForm}>
          <div className={styles.formContainer}>
            <div className={styles.inputWrapper}>
              <input
                type="email"
                placeholder={t("InputPlaceholder")}
                className={`${styles.input} ${formState.errorMsg ? styles.formInputError : ""}`}
                value={formState.email}
                onChange={handleInputChange("email")}
              />
            </div>
            <div className={styles.inputWrapper}>
              <input
                type={formState.showPassword ? "text" : "password"}
                placeholder={t("PasswordPlaceholder")}
                className={`${styles.input} ${formState.errorMsg ? styles.formInputError : ""}`}
                value={formState.password}
                onChange={handleInputChange("password")}
                maxLength={15}
              />
              <button type="button" className={styles.eyeIcon} onClick={togglePasswordVisibility}>
                <FontAwesomeIcon icon={formState.showPassword ? faEyeSlash : faEye} />
              </button>
            </div>

            {formState.errorMsg && (
              <div className={styles.errorMessage}>
                <Image src="/images/webGl/warning.png" height={20} width={20} alt="Warning" />
                {formState.errorMsg}
              </div>
            )}
            <button type="button" onClick={handleForgotPassword} className={styles.forgotPassword}>
              {t("ForgetPassCta")}
            </button>

            <Image
              src="/images/webGl/EmailId/activeGraphic.png"
              className={styles.BackgroundImg}
              width={300}
              height={400}
              alt="BackgroundImg"
            />
          </div>
          <FormCtaButton
            text={t("CtaBtn")}
            loading={formState.loading}
            customStyles="width: 100%; margin: 0 auto;"
          />
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
