"use client";
import CarouselGetStarted from "@/components/CarouselGetStarted";
import styles from "./styles.module.css";
import FormCtaButton from "@/components/FormCtaButton";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { getLocale } from "@/utils/helperFunctions";
import { useLocale, useTranslations } from "next-intl";
import { carouselDataGetStarted } from "@/constants";

const GetStarted = ({ onGetStarted }) => {
  const t = useTranslations("GetStarted");
  const carouselData = carouselDataGetStarted.map((item) => ({
    ...item,
    subHeading: t(item.key),
  }));
  const [currentSubHeading, setCurrentSubHeading] = useState(carouselData[0].subHeading);
  const router = useRouter();
  const locale = useLocale();
  const lang = getLocale(locale);

  const handleSlideChange = (currentSlide) => {
    setCurrentSubHeading(carouselData[currentSlide].subHeading);
  };

  const redirectToLogin = () => {
    router.push("/login");
  };

  return (
    <div className={styles.getStartedWrapper}>
      <CarouselGetStarted onSlideChange={handleSlideChange} />
      <h1 className={styles.heading}>{t("Greeting")}</h1>
      <p className={styles.subheading}>{currentSubHeading}</p>
      <FormCtaButton text={t("CtaText")} onClick={onGetStarted} />
      <p className={styles.bottomText}>
        {t("LoginMessage")}
        <span style={{ cursor: "pointer" }} onClick={redirectToLogin}>
          {" "}
          {t("LoginCta")}
        </span>
      </p>
    </div>
  );
};

export default GetStarted;
