"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-aptor@2.0.0_react@18.3.1";
exports.ids = ["vendor-chunks/react-aptor@2.0.0_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-aptor@2.0.0_react@18.3.1/node_modules/react-aptor/esm/index.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-aptor@2.0.0_react@18.3.1/node_modules/react-aptor/esm/index.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAptor),\n/* harmony export */   useAptor: () => (/* binding */ useAptor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction useAptor(ref, configuration, deps = []) {\n  const [instance, setInstance] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const domRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const { instantiate, destroy, getAPI, params } = configuration;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const instanceReference = instantiate(domRef.current, params);\n    setInstance(instanceReference);\n    return () => {\n      if (destroy)\n        destroy(instanceReference, params);\n    };\n  }, deps);\n  const api = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => getAPI(instance, params), [instance]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, api, [api]);\n  return domRef;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtYXB0b3JAMi4wLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1hcHRvci9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrRjs7QUFFbEY7QUFDQSxrQ0FBa0MsK0NBQVE7QUFDMUMsaUJBQWlCLDZDQUFNO0FBQ3ZCLFVBQVUsdUNBQXVDO0FBQ2pELEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsY0FBYyw4Q0FBTztBQUNyQixFQUFFLDBEQUFtQjtBQUNyQjtBQUNBOztBQUV5QyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtc2tpZG9zLXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtYXB0b3JAMi4wLjBfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1hcHRvci9lc20vaW5kZXguanM/MmMxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZUltcGVyYXRpdmVIYW5kbGUgfSBmcm9tICdyZWFjdCc7XG5cbmZ1bmN0aW9uIHVzZUFwdG9yKHJlZiwgY29uZmlndXJhdGlvbiwgZGVwcyA9IFtdKSB7XG4gIGNvbnN0IFtpbnN0YW5jZSwgc2V0SW5zdGFuY2VdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IGRvbVJlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3QgeyBpbnN0YW50aWF0ZSwgZGVzdHJveSwgZ2V0QVBJLCBwYXJhbXMgfSA9IGNvbmZpZ3VyYXRpb247XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaW5zdGFuY2VSZWZlcmVuY2UgPSBpbnN0YW50aWF0ZShkb21SZWYuY3VycmVudCwgcGFyYW1zKTtcbiAgICBzZXRJbnN0YW5jZShpbnN0YW5jZVJlZmVyZW5jZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChkZXN0cm95KVxuICAgICAgICBkZXN0cm95KGluc3RhbmNlUmVmZXJlbmNlLCBwYXJhbXMpO1xuICAgIH07XG4gIH0sIGRlcHMpO1xuICBjb25zdCBhcGkgPSB1c2VNZW1vKCgpID0+IGdldEFQSShpbnN0YW5jZSwgcGFyYW1zKSwgW2luc3RhbmNlXSk7XG4gIHVzZUltcGVyYXRpdmVIYW5kbGUocmVmLCBhcGksIFthcGldKTtcbiAgcmV0dXJuIGRvbVJlZjtcbn1cblxuZXhwb3J0IHsgdXNlQXB0b3IgYXMgZGVmYXVsdCwgdXNlQXB0b3IgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-aptor@2.0.0_react@18.3.1/node_modules/react-aptor/esm/index.js\n");

/***/ })

};
;