.container {
  min-height: 100vh;
  background: white;
}

.bestValuePlan {
  position: relative;
}

.bestValueBadge {
  position: absolute;
  top: -1.5rem;
  right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.badgeText {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.97rem;
  color: white;
  font-weight: bold;
  white-space: nowrap;
  pointer-events: none;
}

.banner {
  position: relative;
  width: 100%;
  height: 15.625rem;
  overflow: hidden;
}

.bannerImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bannerContent {
  position: absolute;
  top: -6rem;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 2.5rem;
  color: #7c4dff;
  margin-bottom: 1rem;
  font-weight: 700;
  text-align: center;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  color: #666;
  font-size: 1.1rem;
}

.main {
  max-width: 33.125rem;
  margin: -2rem auto;
  text-align: center;
  padding: 0 1rem;
  margin-bottom: 2rem;
}

.pricingSection {
  padding: 2rem;
}

.noPayment {
  margin-bottom: 2rem;
}

.noPayment h2 {
  font-size: 1.5rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.noPayment p {
  color: #333;
}

.annualPlan,
.quarterlyPlan {
  border-radius: 0.938rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  cursor: pointer;
}

.annualPlan {
  border: 0.5rem solid #eaeaea;
  cursor: pointer;
}

.quarterlyPlan {
  border: 0.5rem solid #eaeaea;
}

.annualPlan .planHeader,
.quarterlyPlan .planHeader {
  margin: -2rem -1.5rem 1rem -1.5rem;
  padding: 1rem;
  border-radius: 0.813rem 0.813rem 0 0;
}

.annualPlan .planHeader {
  background: #eaeaea;
}

.quarterlyPlan .planHeader {
  background: #eaeaea;
}

.planHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.planHeader > span {
  font-size: 1.13rem;
}

.discount {
  background: #5a54af;
  color: white;
  padding: 0.5rem 1.3rem;
  border-radius: 0.938rem;
  font-size: 0.875rem !important;
}

.bestValue {
  background: #4caf50;
  color: white;
  padding: 0.25rem 1.3rem;
  border-radius: 0.938rem;
  font-size: 0.9rem;
}

.pricing {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.originalPrice {
  text-decoration: line-through;
  color: #1a1713;
  font-size: 1.45rem;
  margin-right: 0.5rem;
}

.price {
  font-size: 2.91rem;
  font-weight: bold;
  color: #1a1713;
}

.monthly {
  background: #306f5b;
  color: white;
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 1.25rem;
  margin: 0.5rem 0;
  font-size: 1.29rem;
}

.billingInfo {
  color: #6c757d;
  font-family: var(--font-poppins);
  font-size: 1.13rem;
  font-weight: 400;
  text-align: center;
  margin: 0;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.selectedPlan {
  border: 0.5rem solid #ffdf00;
  cursor: pointer;
}

.selectedPlan .planHeader {
  background: #ffdf00;
}

.footer {
  margin: 2rem 0;
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.footer a {
  color: #333;
  text-decoration: none;
  font-size: 0.9rem;
}

.footer a:hover {
  text-decoration: underline;
}

.ctaButton {
  font-size: 1.5rem !important;
  padding: 0.8rem 2rem !important;
  width: 100% !important;
}

@media (max-width: 768px) {
  .container {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .container {
    font-size: 0.75rem;
    /* 12px */
  }
}
