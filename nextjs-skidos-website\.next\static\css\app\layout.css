/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[3].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[3].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/local/target.css?{"path":"src\\app\\layout.js","import":"","arguments":[{"src":"../assets/fonts/NeverMindDisplay-Bold-BF6501204d66b5c.ttf","weight":"700","style":"normal","variable":"--font-nevermind-bold"}],"variableName":"NeverMindBold"} ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: '__NeverMindBold_24169b';
src: url(/_next/static/media/d32d3dbfb184cc47-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 700;
font-style: normal;
}@font-face {font-family: '__NeverMindBold_Fallback_24169b';src: local("Arial");ascent-override: 84.03%;descent-override: 17.32%;line-gap-override: 3.63%;size-adjust: 118.42%
}.__className_24169b {font-family: '__NeverMindBold_24169b', '__NeverMindBold_Fallback_24169b';font-weight: 700;font-style: normal
}.__variable_24169b {--font-nevermind-bold: '__NeverMindBold_24169b', '__NeverMindBold_Fallback_24169b'
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[3].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[3].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/local/target.css?{"path":"src\\app\\layout.js","import":"","arguments":[{"src":"../assets/fonts/NeverMindDisplay-Medium-BF6501205310ee9.ttf","weight":"600","style":"normal","variable":"--font-nevermind-medium"}],"variableName":"NeverMindMedium"} ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: '__NeverMindMedium_5c17e7';
src: url(/_next/static/media/ccaad0531077f67c-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 600;
font-style: normal;
}@font-face {font-family: '__NeverMindMedium_Fallback_5c17e7';src: local("Arial");ascent-override: 89.23%;descent-override: 18.39%;line-gap-override: 3.85%;size-adjust: 111.52%
}.__className_5c17e7 {font-family: '__NeverMindMedium_5c17e7', '__NeverMindMedium_Fallback_5c17e7';font-weight: 600;font-style: normal
}.__variable_5c17e7 {--font-nevermind-medium: '__NeverMindMedium_5c17e7', '__NeverMindMedium_Fallback_5c17e7'
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[3].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[3].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/local/target.css?{"path":"src\\app\\layout.js","import":"","arguments":[{"src":"../assets/fonts/NeverMindDisplay-Light-BF6501205364152.ttf","weight":"400","style":"normal","variable":"--font-nevermind-light"}],"variableName":"NeverMindLight"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: '__NeverMindLight_37a64a';
src: url(/_next/static/media/6eac4fdcd2898c1e-s.p.ttf) format('truetype');
font-display: swap;
font-weight: 400;
font-style: normal;
}@font-face {font-family: '__NeverMindLight_Fallback_37a64a';src: local("Arial");ascent-override: 95.12%;descent-override: 19.60%;line-gap-override: 4.11%;size-adjust: 104.61%
}.__className_37a64a {font-family: '__NeverMindLight_37a64a', '__NeverMindLight_Fallback_37a64a';font-weight: 400;font-style: normal
}.__variable_37a64a {--font-nevermind-light: '__NeverMindLight_37a64a', '__NeverMindLight_Fallback_37a64a'
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{"path":"src\\app\\layout.js","import":"Poppins","arguments":[{"weight":["400","500","600","700"],"style":"normal","subsets":["latin"],"variable":"--font-poppins"}],"variableName":"PoppinsFont"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* devanagari */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/034d78ad42e9620c-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/fe0777f1195381cb-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/eafabf029ad39a43-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/db911767852bc875-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/f10b8e9d91f3edcb-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/8888a3826f4a3af4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/29e7bbdce9332268-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/c3bc380753a8436c-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/0484562807a97172-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/29a4aea02fdee119-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/a1386beebedccca4-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_6bee3b';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/b957ea75a84b6ea7-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Poppins_Fallback_6bee3b';src: local("Arial");ascent-override: 93.62%;descent-override: 31.21%;line-gap-override: 8.92%;size-adjust: 112.16%
}.__className_6bee3b {font-family: '__Poppins_6bee3b', '__Poppins_Fallback_6bee3b';font-style: normal
}.__variable_6bee3b {--font-poppins: '__Poppins_6bee3b', '__Poppins_Fallback_6bee3b'
}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[12].use[3]!./src/app/globals.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  --font-nevermind-bold: "NeverMindBold", sans-serif;
  --font-nevermind-light: "NeverMindLight", sans-serif;
  --font-poppins: "PoppinsFont", sans-serif;
  --font-nevermind-medium: "NeverMindMedium", sans-serif;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: var(--font-nevermind-bold);
}
.noLinkStyle {
  text-decoration: none;
  color: inherit;
}

.noScroll {
  overflow: hidden;
}

/* Base font size for rem calculations */
:root {
  font-size: 16px;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 768px) {
  :root {
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  :root {
    font-size: 12px;
  }
}

/* Video player global styles for IRIS and RekindleMinds pages */
:root {
  --plyr-control-icon-size: 24px;
  --plyr-color-main: #f76000;
  --plyr-range-fill-background: #00c6a3;
  --plyr-range-thumb-background: #f76000;
  --plyr-range-thumb-height: 20px;
  --plyr-range-thumb-shadow: 0 1px 1px rgba(215, 26, 18, 0.15),
    0 0 0 1px rgba(215, 26, 18, 0.2);
  --plyr-range-track-height: 08px;
}

/* Hide video player controls */
.plyr__controls .plyr__controls__item:first-child,
.plyr--pip-supported [data-plyr="pip"],
.plyr--fullscreen-enabled [data-plyr="fullscreen"] {
  display: none;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./src/components/Footer/styles.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.styles_footerWrapper____Ach {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}
.styles_pinkFooterWrapper__ARV8K {
  background-image: url("/images/pinkBg.webp");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  padding: 8.3rem 0 1.5rem 0;
}

.styles_lockContentWrapper__clho7 {
  display: flex;
  align-items: center;
  flex-direction: column;
  font-size: 2.5rem;
}

.styles_lockContentWrapper__clho7 p:nth-child(1) {
  margin: 0;
}

.styles_lockContentWrapper__clho7 p:nth-child(2) {
  color: #ffff;
  margin: 1rem 0 2rem 0;
}

.styles_carouselBannerBtn__jr6pg {
  margin-top: 0rem;
  background-color: #9258fe;
  font-size: 1.5rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
  font-family: var(--font-poppins);
  font-weight: 500;
}

.styles_gameCardsWrapper__yhDNE {
  display: flex;
  gap: 10px;
  justify-content: center;
  text-align: center;
  margin: 2rem 2rem 0 2rem;
}
.styles_gameCardsFirstSection__gUtNL {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.styles_gameCardsFirstSection__gUtNL:nth-child(1) {
  justify-content: flex-end;
  gap: 20px;
}
.styles_gameCardsFirstSection__gUtNL:nth-child(2) {
  justify-content: flex-start;
  gap: 20px;
}
.styles_gameCardCol1__hb_5l div {
  background-color: #ffff;
  padding: 5px;
  border-radius: 20px;
}
.styles_gameCardCol1__hb_5l p {
  margin: 0;
  font-size: 0.8rem;
}

.styles_gameCardCol2__S1dVA {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 15px;
}
.styles_gameCardCol2__S1dVA div {
  background-color: #ffff;
  padding: 5px;
  border-radius: 20px;
}
.styles_gameCardCol2__S1dVA p {
  margin: 0;
  font-size: 0.8rem;
}
.styles_gameCardCol1Img__PCg0s {
  object-fit: fill;
}
.styles_footerGreenWrapper__L8eHT {
  position: relative;
  background-image: url("/images/footer.webp");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  width: 100%;
  min-height: 1250px;
  display: flex;
  align-items: flex-end;
}
.styles_footerContentWrapper__1cJVC {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.12);
  position: absolute;
  width: 98%;
  left: 50%;
  transform: translateX(-50%);
  color: #ffff;
  border-radius: 12px;
  flex-direction: column;
  bottom: 10px;
}
.styles_footerContentWrapper__1cJVC div {
  width: 100%;
}
.styles_footerContentRight__gf5WE {
  display: flex;
  flex-direction: column;
  border-left: none;
}
.styles_footerContentRightTop__fLNXO {
  display: flex;
  width: 100% !important;
  text-align: left;
  border-bottom: 0.1px solid rgba(255, 255, 255, 0.3);
  border-top: 0.1px solid rgba(255, 255, 255, 0.3);
  margin-top: 2rem;
  flex-direction: column;
  padding: 1rem;
  box-sizing: border-box;
}
.styles_footerContentRightTop__fLNXO div {
  width: 100%;
  margin-left: 0.3rem;
}
.styles_footerContentRightTop__fLNXO ul {
  list-style: none;
  text-align: left;
  padding: 0;
  margin: 0;
  font-size: 1.2rem;
  font-family: var(--font-poppins);
}
.styles_footerContentRightTop__fLNXO li {
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 0.7);
}
.styles_linkHoverable__qGJeG:hover {
  display: inline;
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 1);
  box-sizing: border-box;
}
.styles_storeIconsWrapper__tslxu {
  display: grid;
  grid-gap: 16px;
  gap: 16px; /* Adjust spacing between items */
  grid-template-columns: repeat(4, 1fr);
  
      align-self: center;
    place-self: center;
    justify-self: center;
}

.styles_footerIconsDiv__PmrZz{
position: absolute;
  bottom: 280px;
  left: 50%;
  transform: translateX(-50%);
}
.styles_footerContentLeft__W74Vc {
  display: flex;
  flex-direction: column;
  gap: 13px;
  padding: 1rem;
  box-sizing: border-box;
  padding: 0;
}
.styles_footerContentLeft__W74Vc div {
  width: 80%;
  margin-left: 0.5rem;
}
.styles_footerContentLeftImg__rO3Mc {
  display: flex;
  gap: 5px;
}
.styles_storeIcons__88Elm {
  cursor: pointer;
}

@media (min-width: 768px) {
  .styles_lockContentWrapper__clho7 {
    font-size: 5rem;
  }
  .styles_carouselBannerBtn__jr6pg {
    font-size: 2rem;
    padding: 0.8rem 3rem;
  }
  .styles_footerGreenWrapper__L8eHT {
    min-height: 1050px;
    /* min-height: clamp(450px, 100vw + 450px, 1050px); */
  }
  .styles_footerContentWrapper__1cJVC {
    flex-direction: row;
  }
  .styles_footerContentRight__gf5WE {
    border-left: 0.1px solid rgba(255, 255, 255, 0.3);
  }
  .styles_footerContentRightTop__fLNXO {
    border-top: none;
    margin-top: 0;
    flex-direction: row;
    padding: 0;
  }
  .styles_footerContentRightTop__fLNXO div {
    width: 33.3%;
    margin-left: 1rem;
  }
  .styles_footerContentLeft__W74Vc {
    gap: 90px;
    padding-left: 2rem;
  }
  .styles_footerContentRightTop__fLNXO div:nth-child(1),
  .styles_footerContentRightTop__fLNXO div:nth-child(2) {
    border-right: 0.1px solid rgba(255, 255, 255, 0.3);
  }
  .styles_footerContentWrapper__1cJVC div:nth-child(1) {
    width: 40%;
  }
  .styles_gameCardCol2__S1dVA div,
  .styles_gameCardCol1__hb_5l div {
    padding: 3px;
  }
  .styles_gameCardsFirstSection__gUtNL p {
    font-size: 1rem;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .styles_storeIcons__88Elm {
    width: 140px;
    height: 50px;
  }
  .styles_footerGreenWrapper__L8eHT {
    min-height: 830px !important;
  }
  .styles_footerContentRightTop__fLNXO div:nth-child(1),
  .styles_footerContentRightTop__fLNXO div:nth-child(2) {
    width: 23%;
  }
}

@media (min-width: 1550px) {
  .styles_footerGreenWrapper__L8eHT {
    min-height: 1200px !important;
  }
}
@media (max-width: 700px) {
  .styles_storeIconsWrapper__tslxu {
    display: grid;
    grid-gap: 16px;
    gap: 16px;
    grid-template-columns: repeat(2, 1fr);
  }
  .styles_storeIcons__88Elm {
    width: 178px;
    height: 50px;
  }
}

@media (max-width: 700px) {
  .styles_footerIconsDiv__PmrZz {
    position: absolute;
    bottom: 740px;
    left: 50%;
    transform: translateX(-50%);
  }
}


@media screen and (max-height: 500px) and (orientation: landscape) {
  .styles_specificPageFooter__tphbG {
    display: none !important;
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./src/components/LanguageSelector/styles.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.styles_languageSelectorWrapper__iO2Ag {
  /* border: 2px solid yellow; */
  cursor: pointer;
  margin-left: 1rem;
  display: flex;
  gap: 5px;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.styles_overlay__tIi_b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Dark background with some transparency */
  z-index: 1; /* Below the selector but above the rest of the content */
}

/* Language options container */
.styles_languageOptionsToggleWrapper__GZ5AG {
  position: fixed;
  top: 90px;
  right: 40px;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 3;
  width: 195px;
}

/* Style the list of languages */
.styles_languageOptionsToggleWrapper__GZ5AG ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.styles_languageOptionsToggleWrapper__GZ5AG li {
  font-family: var(--font-poppins);
  font-weight: 500;
  padding: 15px 0;
  cursor: pointer;
  border-bottom: 1px solid #666666;
  text-align: center;
}

.styles_languageOptionsToggleWrapper__GZ5AG li:last-child {
  border-bottom: none;
}
.styles_selectedLanguage___Q76Y {
  color: #9258fe;
  font-weight: 700 !important;
}
@media (max-width: 820px) {
  .styles_languageOptionsToggleWrapper__GZ5AG {
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50%;
  }
  .styles_languageSelectorWrapper__iO2Ag {
    margin-top: 2rem;
  }
  .styles_langTranslatorIcon__O8Xb8 {
    width: 42px;
    height: 40px;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./src/components/Navbar/styles.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.styles_navbar__r2ciA {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background-color: rgba(255, 255, 255, 1);
  border-bottom: 1px solid #dbdbdb;
  top: 0;
  position: -webkit-sticky;
  position: sticky;
  z-index: 100;
  font-family: var(--font-poppins);
  font-weight: 500;
  width: 100%;
  box-sizing: border-box;
}

.styles_logo__vlu7A a {
  color: black;
  text-decoration: none;
  font-size: 1.5rem;
}

.styles_crossImg__rs7Qg {
  display: none;
}

.styles_menuIcon__NQeoh {
  display: none;
  cursor: pointer;
}

.styles_menuIcon__NQeoh .styles_bar__ytcHm {
  width: 30px;
  height: 3px;
  background-color: black;
  margin: 4px 0;
  transition: 0.4s;
}

.styles_menuIcon__NQeoh.styles_change__xATiX .styles_bar__ytcHm:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.styles_menuIcon__NQeoh.styles_change__xATiX .styles_bar__ytcHm:nth-child(2) {
  opacity: 0;
}

.styles_menuIcon__NQeoh.styles_change__xATiX .styles_bar__ytcHm:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

.styles_navMenu__a_FEe {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
  flex-wrap: nowrap;
}

.styles_navItem__ULId1 {
  margin-left: 1rem;
  white-space: nowrap;
}

.styles_navLink__Hm5Z5 {
  color: #6666;
  text-decoration: none;
  padding: 0.5rem 0.5rem;
  display: block;
  position: relative;
  white-space: nowrap;
}

.styles_navLink__Hm5Z5::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 4px;
  background-color: rgba(146, 88, 254, 1);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.styles_navLink__Hm5Z5:hover,
.styles_activeLink___mTrY {
  color: black !important;
}

.styles_activeLink___mTrY::after {
  width: 100%;
}

.styles_freeTrailBtn__cEcx2 {
  font-weight: 600;
  font-size: 1.5rem;
  background-color: rgba(146, 88, 254, 1);
  color: #fff;
  border-radius: 8px;
  box-shadow: 0px 6px 0px rgba(74, 45, 128, 1);
  padding: 0.5rem 1.5rem;
  white-space: nowrap;
}

.styles_freeTrailBtn__cEcx2:hover {
  color: #fff !important;
}

@media (max-width: 820px) {
  .styles_navbar__r2ciA {
    padding: 0.8rem 0.8rem 0.2rem 0.8rem;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .styles_logoImg__A8DqR {
    width: 140px;
    height: 45px;
  }

  .styles_menuIcon__NQeoh {
    display: block;
  }

  .styles_noScroll__OWjDr {
    overflow: hidden;
    height: 100%;
  }

  .styles_navMenu__a_FEe {
    flex-direction: column;
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    right: -150%;
    background-color: #fff;
    align-items: center;
    transition: 0.3s;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 1rem;
    box-sizing: border-box;
  }

  .styles_navMenu__a_FEe.styles_active__afWsr {
    right: 0;
    z-index: 20;
  }

  .styles_navItem__ULId1 {
    margin: 1rem 0;
    font-size: 1.5rem;
    text-align: center;
  }

  .styles_navLink__Hm5Z5 {
    font-size: 1.25rem;
    padding: 0.5rem;
  }

  .styles_freeTrailBtn__cEcx2 {
    font-size: 1.25rem;
    padding: 0.5rem 1.25rem;
    display: inline-block;
    text-align: center;
  }

  .styles_crossImg__rs7Qg {
    display: block;
  }
}

@media screen and (max-height: 500px) and (orientation: landscape) {
  .styles_specificPageNavbar__30IfH {
    display: none !important;
  }
}

