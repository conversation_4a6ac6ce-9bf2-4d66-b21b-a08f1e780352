"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0";
exports.ids = ["vendor-chunks/@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0/node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0/node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a(...params) {\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/speed-insights/script.js`;\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights(props = {}) {\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(`[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`);\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route ?? void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!setScriptRoute.current) {\n            const script = injectSpeedInsights({\n                framework: props.framework ?? \"react\",\n                basePath: props.basePath ?? getBasePath(),\n                ...props\n            });\n            if (script) {\n                setScriptRoute.current = script.setRoute;\n            }\n        } else if (props.route) {\n            setScriptRoute.current(props.route);\n        }\n    }, [\n        props.route\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)() || new URLSearchParams();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return null;\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return computeRoute(path, finalParams);\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction SpeedInsightsComponent(props) {\n    const route = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsights, {\n        route,\n        ...props,\n        framework: \"next\",\n        basePath: getBasePath2()\n    });\n}\nfunction SpeedInsights2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsightsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0/node_modules/@vercel/speed-insights/dist/next/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0/node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0/node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpeedInsights: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\SKIDOS\Front-end-proj\nextjs-skidos-website\node_modules\.pnpm\@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0\node_modules\@vercel\speed-insights\dist\next\index.mjs`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\SKIDOS\Front-end-proj\nextjs-skidos-website\node_modules\.pnpm\@vercel+speed-insights@1.2._bd2241ac63aab5e5375e1628fa16eff0\node_modules\@vercel\speed-insights\dist\next\index.mjs#SpeedInsights`);


/***/ })

};
;