import axios from "axios";
import { NextResponse } from "next/server";

export async function GET(req) {
  try {
    // Extract email from query parameters
    const { searchParams } = new URL(req.url);
    const email = searchParams.get("email");

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Kickbox API URL
    const apiKey = process.env.NEXT_PUBLIC_KICKBOX_API_KEY;
    const kickboxUrl = `https://api.kickbox.com/v2/verify?email=${email}&apikey=${apiKey}`;

    // Make request to Kickbox API
    const response = await axios.get(kickboxUrl);

    // Return response from Kickbox to frontend
    return NextResponse.json(response.data);
  } catch (error) {
    console.error("Kickbox API Error:", error.message);
    return NextResponse.json(
      { error: "Failed to verify email", details: error.message },
      { status: 500 }
    );
  }
}
