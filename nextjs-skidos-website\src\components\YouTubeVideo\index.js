"use client";
import styles from "./styles.module.css";

const YouTubeVideo = ({
  videoId,
  title = "YouTube Video",
  width = "100%",
  height = "100%",
  className = "",
  autoplay = false,
  controls = true,
  modestbranding = true,
  rel = false,
}) => {
  const embedUrl = `https://www.youtube.com/embed/${videoId}?${new URLSearchParams({
    autoplay: autoplay ? "1" : "0",
    controls: controls ? "1" : "0",
    modestbranding: modestbranding ? "1" : "0",
    rel: rel ? "1" : "0",
    enablejsapi: "1",
    origin: typeof window !== "undefined" ? window.location.origin : "",
  }).toString()}`;

  return (
    <iframe
      style={{ width, height }}
      src={embedUrl}
      title={title}
      width="100%"
      height="100%"
      frameBorder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowFullScreen
      className={styles.iframe}
    />
  );
};

export default YouTubeVideo;
