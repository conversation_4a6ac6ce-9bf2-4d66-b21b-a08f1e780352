import axios from "axios";

const apiClient = axios.create({
  // baseURL: "https://apitest.skidos.com/apis",
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Function to refresh the token
const refreshAuthToken = async () => {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/refresh/token`,
      {
        headers: {
          "Content-Type": "application/json",
          Refresh_token: localStorage.getItem("refresh_token"),
        },
      }
    );

    if (response.status === 200) {
      const { auth_token, refresh_token } = response.headers;
      localStorage.setItem("auth_token", auth_token);
      localStorage.setItem("refresh_token", refresh_token);
      return auth_token;
    }
  } catch (error) {
    console.error("Error refreshing token:", error);
    return null;
  }
};

// Axios request interceptor to add the token to headers
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = token;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Axios response interceptor to handle token expiration
apiClient.interceptors.response.use(
  (response) => {
    const authToken = response.headers["auth_token"];
    const refreshToken = response.headers["refresh_token"];
    if (authToken) {
      localStorage.setItem("auth_token", authToken);
    }
    if (refreshToken) {
      localStorage.setItem("refresh_token", refreshToken);
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true; // Mark the request as retried
      console.warn("Token expired. Refreshing token...");

      const newToken = await refreshAuthToken();
      if (newToken) {
        originalRequest.headers.Authorization = newToken;
        return apiClient(originalRequest); // Retry the original request
      }
    }

    return Promise.reject(error); // Reject the promise if the error persists
  }
);

export default apiClient;
