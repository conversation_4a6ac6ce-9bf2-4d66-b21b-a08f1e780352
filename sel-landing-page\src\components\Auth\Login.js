import React, { useState } from "react";
import AuthContainer from "./AuthContainer";
// images
import logo from "icons/auth/logo.png";
// components
import FormInput from "components/common/FormInput";

export function Login() {
  const [isLoginByOtp, setLoginByOtp] = useState(false);
  const [isOtpSent, setOtpSent] = useState(false);

  const otpLogin = () => {
    setLoginByOtp(true);
  };

  const sendOtp = () => {
    setOtpSent(true);
  };

  return (
    <AuthContainer>
      <div className="container">
        <div className="row">
          <div className="col-sm-12 col-md-12 col-lg-1">&nbsp;</div>
          <div className="col-sm-12 col-md-12 col-lg-5 auth_align_center">
            <div className="auth_left_container">
              <img src={logo} alt="logo" />
              <div className="auth_left_title">Lorem ipsum.</div>
              <div className="auth_left_content">
                Lorem ipsum dolor sit amet, consetetur sadipscing <br /> elitr,
                sed diam nonumy eirmod tempor invidunt ut. <br /> Lorem ipsum
                dolor sit amet.
              </div>
            </div>
          </div>
          <div className="col-sm-12 col-md-12 col-lg-5 auth_align_middle">
            {isLoginByOtp ? (
              <div className="auth_right_container">
                {isOtpSent ? (
                  <>
                    <div className="auth_right_title">OTP Sent</div>
                    <div className="auth_right_content">
                      One-time password sent to <b><EMAIL></b>{" "}
                      <br /> Please check inbox or spam folder. <br /> Code is
                      valid for 15 minutes
                    </div>
                    <div className="topgap40">
                      <FormInput
                        type="text"
                        name="otp"
                        placeholder="Enter OTP"
                        label="OTP"
                      />
                    </div>
                    <button className="skidos-btn-primary topgap40">
                      Continue
                    </button>
                    <div className="auth_right_content topgap40">
                      Resend OTP in 30s
                    </div>
                    <div className="auth_right_content topgap40">
                      <a className="auth_link" onClick={otpLogin}>
                        Sign in with Password
                      </a>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="auth_right_title">
                      A one-time password will be sent to your email
                    </div>
                    <div className="topgap40">
                      <FormInput
                        type="text"
                        name="email"
                        placeholder="Enter email"
                        label="Email"
                      />
                    </div>
                    <button
                      className="skidos-btn-primary topgap40"
                      onClick={sendOtp}
                    >
                      Send OTP
                    </button>
                    <div className="auth_right_content topgap40">
                      <a className="auth_link" onClick={otpLogin}>
                        Sign in with Password
                      </a>
                    </div>
                    <div className="auth_right_content topgap40">
                      New user? <a className="auth_link">Create account</a>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="auth_right_container">
                <div className="auth_right_title">
                  Welcome to <strong>SKIDOS</strong>
                </div>
                <div className="auth_right_content">&nbsp;</div>
                <div className="topgap40">
                  <FormInput
                    type="text"
                    name="email"
                    placeholder="Enter email"
                    label="Email"
                  />
                </div>
                <div className="topgap20">
                  <FormInput
                    type="password"
                    name="password"
                    placeholder="Enter password"
                    label="Password"
                  />
                </div>
                <button className="skidos-btn-primary topgap40">Sign in</button>
                <div className="auth_right_content topgap40">
                  Forget password?{" "}
                  <a className="auth_link" onClick={otpLogin}>
                    Sign in with OTP
                  </a>
                </div>
                <div className="auth_right_content topgap40">
                  New user? <a className="auth_link">Create account</a>
                </div>
              </div>
            )}
          </div>
          <div className="col-sm-12 col-md-12 col-lg-1">&nbsp;</div>
        </div>
      </div>
    </AuthContainer>
  );
}

export default Login;
