.createPlayerHeader {
  padding: 1rem;
  text-align: left;
}

.createPlayerHeader h1 {
  margin: 0;
  font-size: 2rem;
}

.createPlayerHeader p {
  margin: 0;
  font-family: var(--font-poppins);
  color: #6c757d;
  font-family: 1.2rem;
}

.selected {
  border: 3px solid #9258fe;
  box-sizing: border-box;
  border-radius: 50%;
  box-shadow: 0px 0px 4.37px 4.37px #9258fe54;
}

.infoText {
  font-size: 16px;
  margin-top: 1rem !important;
  margin-bottom: 0;
  color: #6c757d;
  font-family: var(--font-poppins);
  cursor: pointer;
}

.personilisationContent {
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  height: clamp(300px, 52vh, 400px);
  max-height: 400px;
  min-height: 300px;

  overflow-y: auto;
  scrollbar-width: none;
}

.personilisationContent::-webkit-scrollbar {
  display: none;
}

.themeItem {
  flex: 0 0 calc(33.33% - 2rem);
  max-width: calc(33.33% - 2rem);
  cursor: pointer;
  padding: 0.5rem;
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.themeItem:hover {
  transform: scale(1.05);
}

.themeItem img {
  width: 100%;
  height: auto;
  max-width: clamp(75px, 15vw, 115px);
  border-radius: 50%;
  object-fit: cover;
}

.selected img {
  border: 3px solid #9258fe;
  box-shadow: 0px 0px 4px 4px rgba(146, 88, 254, 0.33);
  border-radius: 50%;
}

.themeItem p {
  margin-top: clamp(0.5rem, 1vw, 1rem);
  font-size: clamp(0.8rem, 1vw, 1rem);
  color: #333;
}

@media (max-width: 750px) {
  .personilisationContent {
    height: clamp(250px, 40vh, 350px);
  }
  .themeItem {
    /* flex: 0 0 calc(33.33% - 1rem);
    max-width: calc(33.33% - 1rem); */
  }
}

@media (max-width: 480px) {
  .themeItem {
    /* flex: 0 0 calc(50% - 1rem);
    max-width: calc(50% - 1rem); */
  }
}

.errorWrapper {
  width: 75%;
  border-radius: 5px;
  background-color: #f8d7ce;
  font-family: var(--font-nevermind-light);
  color: #dd4a38;
  margin-top: 0.5rem;
  padding: 0.5rem 0;
  margin: 1rem auto 0 auto;
}

.errorWrapper p {
  font-family: var(--font-nevermind-medium);
  margin: 0;
}

.errorWrapper span {
  color: #0169dd;
}
