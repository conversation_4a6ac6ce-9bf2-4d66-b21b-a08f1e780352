.container {
  min-height: 100vh;
  background-color: white;
  margin-top: 2rem;
}
.content {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  margin-bottom: 1.5rem;
  padding: 2rem;
  width: 818px;
}
.daskboxImage {
  padding-right: 3.5rem;
  width: 210px;
  height: 173px;
}
.loginBanner {
  background: radial-gradient(95.78% 61.92% at 50% 35.25%, #cbafff 0%, #9258fe 100%);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  max-width: 765px;
  align-self: center;
  padding: 1rem;
  text-align: center;
  overflow: hidden;
}
.loginBanner .title {
  font-size: 1.5rem;
  color: #e2e2e2;
  line-height: 2rem;
  font-weight: bold;
  font-family: var(--font-nevermind-display);
  word-wrap: break-word;
}
.bannerContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: left;
  gap: 1rem;
  padding-left: 1rem;
}
.button {
  background-color: #4b5563;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin-top: 1rem;
  border: none;
  cursor: pointer;
}
.whiteButton {
  background-color: white;
  color: #8b5cf6;
  box-shadow: 0px 5.35px 0px 0px #4a2d7f;
  width: auto;
  height: auto;
  padding: 6.4px 32.11px;
  font-size: var(--font-poppins);
  border-radius: 10.7px;
  font-weight: 500;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  max-width: 765px;
}
.headerDiv {
  background: #f9f9f9;
  text-align: -webkit-center;
}
.email {
  color: #b9b9b9;
  margin-bottom: 1.5rem;
  max-width: 765px;
  text-align: start;
}
.membershipInfoContent {
  display: flex;
  flex-direction: column;
  font-size: 1.1rem;
  max-width: 765px;
  padding: 1rem;
}
.membershipInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.label {
  color: #b9b9b9;
}
.active {
  background-color: #f5fff5;
  color: #065f46;
  padding: 0.75rem 1.25rem;
  border: 1px solid #027700;
  border-radius: 0.5rem;
}
.inactive {
  border: 1px solid #f2a83b;
  color: #f2a83b;
  background-color: #fffbf5;
}
.subtitle {
  font-size: larger;
  background: #f9f9f9;
  height: auto;
}
.qrCodes {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 765px;
  text-align: -webkit-center;
}
.qrCode {
  width: 200px;
  height: 200px;
  border: 1px solid black;
  border-radius: 0.7rem;
}
.exploreButton {
  box-shadow: 0px 8.7px 0px 0px #4a2d7f;
  background: #9258fe;
  color: white;
  padding: 0.75rem 4rem;
  font-size: 1.125rem;
  text-align: center;
  width: fit-content;
  align-self: center;
  margin-bottom: 2rem;
}
@media (min-width: 768px) {
  .loginBanner {
    flex-direction: row;
    max-width: 880px;
    padding: 2rem;
    text-align: left;
  }
  .membershipInfoContent {
    max-width: 880px;
  }
  .header {
    max-width: 880px;
  }
  .email {
    max-width: 880px;
  }
  .qrCodes {
    flex-direction: row;
    justify-content: space-evenly;
    max-width: 765px;
  }
}
@media (max-width: 768px) {
  .loginBanner {
    flex-direction: column-reverse;
    max-width: 320px;
    align-items: center;
    align-self: center;
    padding: 1rem;
  }
  .membershipInfoContent {
    max-width: 586px;
  }
  .header {
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    font-size: medium;
    max-width: 320px;
  }
  .title {
    text-align: center;
    margin-bottom: 20px;
    padding: 0;
  }
  .email {
    text-align: center;
    margin-bottom: 20px;
    max-width: 320px;
  }
  .subtitle {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.1rem;
    padding: 0;
  }
  .qrCodes {
    max-width: 765px;
  }
  .bannerContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0;
  }
}
@media (max-width: 375px) {
  .loginBanner .title {
    font-size: 1.5rem;
  }
}

.ghostButton {
  background-color: transparent;
  color: #4b5563;
  text-decoration: underline;
  font-family: var(--font-nevermind-medium);
  font-size: 1.2rem;
}
