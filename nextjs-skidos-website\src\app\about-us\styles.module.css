.aboutUsContainer {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}
.OurMissionContainer {
  width: 100%;
  height: auto;
  background-image: url("/images/aboutUs/banner4k.png");
  background-size: cover;
  background-position: center;
  padding: 40px 10px;
  box-sizing: border-box;
  overflow-x: hidden;
}

.MissionFlexCenter {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin: 10px 10px;
  box-sizing: border-box;
}

.MissionTitle {
  margin-top: 10px;
  width: 100%;
  max-width: 1059px;
  opacity: 1;
  font-family: var(--font-nevermind-bold);
  font-size: 3rem;
  font-weight: 500;
  line-height: 1.2;
  text-align: center;
  color: black;
  box-sizing: border-box;
}

.MissionHighlightSubheader {
  font-size: 3rem;
  background-color: #fa7e47;
  border-radius: 100px;
  padding: 0.5rem 1.2rem;
  display: inline-flex;
  align-items: center;
  font-family: var(--font-nevermind-bold);
}

.MissionArrowImg {
  vertical-align: middle;
}

.MissionText {
  width: 100%;
  max-width: 1059px;
  height: auto;
  font-family: var(--font-poppins);
  font-size: 1.8rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  margin-top: 20px;
  box-sizing: border-box;
}

/* Responsive Styles */
@media (min-width: 768px) {
  .OurMissionContainer {
    padding: 60px 40px;
  }

  .MissionHighlightSubheader {
    font-size: 2.5rem;
  }

  .MissionText {
    font-size: 1.4rem;
    margin: 20px 40px;
    font-family: var(--font-poppins);
  }
  .MissionArrowImg {
    vertical-align: middle;
  }
}

@media (min-width: 1024px) {
  .OurMissionContainer {
    padding: 80px 60px;
  }

  .MissionHighlightSubheader {
    font-size: 3rem;
  }

  .MissionText {
    font-size: 1.4rem;
    margin: 20px 60px;
    font-family: var(--font-poppins);
  }
}

@media (max-width: 480px) {
  .MissionHighlightSubheader {
    font-size: 2.7rem;
  }

  .MissionText {
    font-size: 1.2rem;
    margin: 10px 10px;
    font-family: var(--font-poppins);
  }
  .MissionArrowImg {
    width: 35px;
    height: 35px;
  }
}

/* history css */

.historyScroll {
  max-width: 100%;
  overflow: auto;
  scrollbar-width: none;
}

.about {
  color: black;
  text-align: center;
  width: 100%;
  /* background: #f1f1f1; */
  gap: 0px;
  opacity: 1;
  height: 710px;
}

.container {
  max-width: 1120px;
  margin: 0 auto;
  z-index: 2;
  position: sticky;
}

.aboutContent {
  z-index: 2;
  display: inline-block;
  vertical-align: middle;
  font-family: var(--font-nevermind-bold);
  font-size: 2rem;
}

.aboutHighlightSubheader1 {
  display: inline-block;
  vertical-align: middle;
  font-family: var(--font-nevermind-bold);
  font-size: 2rem;
  font-weight: 900;
  line-height: 89.51px;
  text-align: left;
  width: 290px;
  height: 90px;
}

.aboutHighlightImg {
  display: inline-block;
  vertical-align: middle;
  margin-left: 0.2rem;
  /* object-fit: contain; */
  margin-top: -20px;
  height: 588px;
  width: 2000px;
}
.historyCoreValuesWrapper {
  background-image: url("../../../public/images/aboutUs/bg4.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .aboutHighlightSubheader1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 992px) {
  .aboutHighlightSubheader1 {
    font-size: 1.5rem;
  }
  .aboutContent {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .aboutHighlightSubheader1 {
    font-size: 3rem;
  }
  .aboutContent {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .aboutHighlightSubheader1 {
    font-size: 2rem;
  }
  .aboutContent {
    font-size: 1.6rem;
  }
}

/* core value.... */

.coreContainer {
  text-align: center;
  padding: 1.4rem;
}

.coreHeading1 {
  font-size: 3rem;
  margin-bottom: 10px;
  margin-top: -10px;
  color: black;
  font-family: var(--font-nevermind-bold);
  font-weight: 900;
  line-height: 89.51px;
  text-align: center;
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.card {
  width: 100%;
  max-width: 300px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  color: #ffff;
  font-size: 1rem;
  text-align: left;
  padding: 8px;
  box-sizing: border-box;
  margin: 10px 5px 0px 25px;
  /* background: rgba(255, 255, 255, 0.16); */
  background-color: #1dc368;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(9.2px);
  -webkit-backdrop-filter: blur(9.2px);
  width: 350.3px;
  height: 120px;
  top: 184px;
  left: 687.3px;
  border: 0.57px;
}

.card h3 {
  margin: 0;
  font-family: var(--font-NeverMind-Display);

  font-weight: 900;
  line-height: 19.53px;

  font-size: 1.2rem;
}

.card p {
  margin: 8px 0 0;
  font-size: 0.9rem;
  font-family: var(--font-poppins);

  font-weight: 500;
  line-height: 19.21px;
  text-align: left;
}

.cardWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.cardWrapperRow1 {
  grid-column: 1 / span 3;
  display: flex;
  justify-content: center;
}

.cardWrapperRow2 {
  grid-column: 1 / span 3;
  display: flex;
  justify-content: center;
}

.cardWrapperRow3 {
  grid-column: 1 / span 3;
  display: flex;
  justify-content: center;
}

@media (max-width: 767px) {
  .gridContainer {
    grid-template-columns: repeat(2, 1fr);
  }
  .cardWrapperRow1,
  .cardWrapperRow2,
  .cardWrapperRow3 {
    flex-direction: column;
    gap: 2rem;
    align-items: center;
  }
  .card {
    margin: 0;
  }
  .card h3 {
    font-size: 1.3rem;
  }
  .heading1 {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .gridContainer {
    grid-template-columns: 1fr;
  }
}

/* meet gang */

.meetHeading {
  font-family: var(--font-nevermind-bold);
  font-size: 3rem;
  font-weight: 900;
  line-height: 1.5;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 20px;
}

.isFlexCenter1 {
  display: flex;
  justify-content: space-evenly;
  font-family: var(--font-poppins);
  flex-direction: row;
  align-items: center;
}

.Flex1 {
  flex: 1;
  margin: 20px;
  font-size: 1rem;
}
.moyoImage {
  width: 100%;
  height: 100%;
}
.colorineImage {
  width: 100%;
  height: 100%;
}
.netaImage {
  width: 100%;
  height: 100%;
}

.imageContainer {
  flex: 1;
  text-align: center;
}

.paragarp {
  text-align: left;
  color: white;
  padding: 0 5rem 0 5rem;
  flex: 1;
  font-size: 1, 2rem;
  font-weight: 500;
  text-align: left;
}

.paragarp h1 {
  margin-bottom: 10px;
  font-size: 2.5rem;
  font-weight: 500;
  line-height: 36px;
  font-family: var(--font-nevermind-bold);
}

.paragarp p {
  font-size: 1.5rem;
  line-height: 1.5;
  font-weight: 500;
  text-align: left;
  width: 100%;
  font-family: var(--font-poppins);
}

/* Responsive Styles for Tablets and Small Laptops */
@media (max-width: 1024px) {
  .meetImage {
    width: 404px;
    height: 427px;
  }

  .paragarp {
    font-size: 1rem;
    font-weight: 500;
    width: 566px;
    height: 350px;
  }

  .paragarp p {
    font-size: 1.2rem;
    font-weight: 463;
    line-height: 24px;
  }
}

/* Extra Small Mobile Devices */
@media (max-width: 480px) {
  .paragarp h1 {
    display: flex;
    flex-direction: column;
  }

  .isFlexCenter1 {
    flex-direction: column;
    padding: 10px;
  }
  .paragarp {
    width: 330px;
    height: 229px;
    padding: 10px;
  }

  .paragarp p {
    font-size: 1.2rem;
    text-align: left;
    display: flex;
    flex-direction: column;
  }
}
