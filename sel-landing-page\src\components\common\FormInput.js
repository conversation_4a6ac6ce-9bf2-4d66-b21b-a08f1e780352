import React from "react";
import PropTypes from "prop-types";

export function FormInput(props) {
  return (
    <>
      <span className="form_input_label">{props.label}</span>
      <input
        className="form_input_styled"
        type={props.type ? props.type : "text"}
        id={props.name}
        placeholder={props.placeholder}
      />
    </>
  );
}

export default FormInput;

FormInput.propTypes = {
  type: PropTypes.string,
  name: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
};
