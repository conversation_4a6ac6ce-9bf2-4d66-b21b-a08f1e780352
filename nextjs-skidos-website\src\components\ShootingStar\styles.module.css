.shootingStarWrapper {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.shootingStar {
  position: absolute;
  top: -10%; /* Start slightly off-screen */
  left: -10%; /* Start slightly off-screen */
  width: 50px; /* Adjust size */
  animation: shootingStar 3s ease-in-out infinite;
}

@keyframes shootingStar {
  0% {
    transform: translate(-10%, -10%) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translate(100vw, 100vh) rotate(45deg);
    opacity: 1;
  }
  75% {
    transform: translate(50vw, 50vh) rotate(90deg);
    opacity: 0.5;
  }
  100% {
    transform: translate(-10%, -10%) rotate(360deg);
    opacity: 0;
  }
}
