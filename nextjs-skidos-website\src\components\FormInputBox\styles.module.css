.formInputBox {
  width: 100%;
  box-sizing: border-box;
  border: 2.5px solid rgba(235, 235, 235, 1);
  border-radius: 2.2rem;
  padding: 1.5rem 2rem;
  font-size: 1.7rem;
  color: rgba(185, 185, 185, 1);
  margin-bottom: 2rem;
}
.formInputBoxError {
  border: 2.5px solid rgba(255, 0, 0, 1);
  color: rgba(255, 0, 0, 1);
  background-color: rgba(255, 0, 0, 0.1);
}
.formInputBoxError::placeholder {
  color: rgba(255, 0, 0, 1) !important;
}
.formInputBox:focus {
  outline: none;
}
.formInputBox::placeholder {
  font-size: 1.7rem;
  color: rgba(185, 185, 185, 1);
}
@media only screen and (max-width: 480px) {
  .formInputBox {
    padding: 1rem 1.5rem;
    border-radius: 1.2rem;
    border: 1.5px solid rgba(235, 235, 235, 1);
    font-size: 1.2rem;
  }
  .formInputBox::placeholder {
    font-size: 1.2rem;
  }
  .formInputBoxError {
    border: 1.5px solid rgba(255, 0, 0, 1);
  }
}
