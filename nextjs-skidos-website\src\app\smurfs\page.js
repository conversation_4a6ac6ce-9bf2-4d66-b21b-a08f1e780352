"use client";
import Head from "next/head";
import Image from "next/image";
import styles from "./styles.module.css";

const SmurfsComingSoon = () => {
  // Commented out for future use when videos are implemented
  // const mainTrailerVideoId = "dQw4w9WgXcQ"; // Replace with actual trailer video ID
  // const teaserVideos = [
  //   { id: 1, title: "Smurfs Learning Adventure", videoId: "dQw4w9WgXcQ" },
  //   { id: 2, title: "Educational Games Preview", videoId: "dQw4w9WgXcQ" },
  //   { id: 3, title: "Character Introduction", videoId: "dQw4w9WgXcQ" },
  //   { id: 4, title: "Gameplay Highlights", videoId: "dQw4w9WgXcQ" },
  // ];

  // Game cards for display - separate from videos
  const gameCards = [
    { id: 1, text: "THE CHARACTERS YOU LOVE!" },
    { id: 2, text: "WATCH OUT FOR GARGAMEL!" },
    { id: 3, text: "玩迷你游戏" },
    { id: 4, text: "MAGICAL ADVENTURES!" },
    { id: 5, text: "LEARN WITH SMURFS!" },
    { id: 6, text: "SMURF VILLAGE!" },
    { id: 7, text: "SMURF VILLAGE!" },
  ];

  // const t = useTranslations("Footer"); // Commented out for future use

  // Commented out for future use when store icons are implemented
  // const icons = [
  //   {
  //     src: "/images/footer/socialIcons/Amazon.webp",
  //     alt: t("Amazon"),
  //     onClick: () => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank"),
  //   },
  //   {
  //     src: "/images/footer/socialIcons/Appstore.webp",
  //     alt: t("Appstore"),
  //     onClick: () =>
  //       window.open(
  //         "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
  //         "_blank"
  //       ),
  //   },
  //   {
  //     src: "/images/footer/socialIcons/Playstore.webp",
  //     alt: t("Playstore"),
  //     onClick: () =>
  //       window.open(
  //         "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
  //         "_blank"
  //       ),
  //   },
  // ];

  return (
    <>
      <Head>
        <title>SKIDOS x Smurfs</title>
        <meta
          name="description"
          content="Smurfs Are Here to Learn & Play! SKIDOS and the Smurfs have teamed up to bring exciting educational games."
        />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <>
        {/* First Frame - Hero Banner */}
        <div className={styles.firstFrame}>
          {/* Desktop Banner */}
          <div className={styles.desktopBanner}>
            <Image
              src="/images/skidos-smurfs/banner-smurf-skidos.webp"
              alt="Smurf Characters"
              width={1920}
              height={800}
              priority
              sizes="100vw"
              className={styles.bannerImage}
            />
          </div>

          {/* Mobile Banner */}
          <div className={styles.mobileBanner}>
            <Image
              src="/images/skidos-smurfs/banner-smurf-skidos-mobile.webp"
              alt="Smurf Characters"
              width={768}
              height={1024}
              priority
              sizes="100vw"
              className={styles.bannerImage}
            />
          </div>
        </div>

        {/* Second Frame - White background with text */}
        <div className={styles.secondFrame}>
          <h1 className={styles.mainHeading}>
            Welcome to the Smurfs Playhouse — Coming Soon to SKIDOS!
          </h1>
          <p className={styles.subHeading}>(A Smurftastic Blend of Imagination, Learning & Play)</p>
          {/* <p className={styles.subHeading}>Download now and play!</p> */}
          {/* <StoreIcons icons={icons} styleClass={styles.storeIconsWrapper} /> */}
          <p className={styles.description}>
            Get ready for a smurfy new adventure! SKIDOS brings your little ones an all-new
            interactive pretend play experience featuring their favorite blue friends—perfectly
            designed for kids aged 3 to 7 years. From the Playroom to the Playground, the Kitchen,
            Bathroom, and beyond—each area is filled with smurfalicious surprises and interactive
            fun. Kids can tap, drag, and create endless stories with beloved characters like
            Smurfette, Papa Smurf, Clumsy Smurf, and more. Whether it’s dress-up time, cooking, or a
            cozy bedtime routine—every scene is a chance for storytelling and imaginative discovery.
            Let your child’s imagination run smurfwild!
          </p>
          {/* <h1 className={styles.teaserTrailerHeading}>Watch the trailer now</h1>
          <div className={styles.trailerVideoContainer}>
            <YouTubeVideo
              videoId={mainTrailerVideoId}
              title="SKIDOS x Smurfs Official Trailer"
              className={styles.trailerVideo}
            />
          </div>

          <h1 className={styles.teaserTrailerHeading}>Teaser</h1>
          <div className={styles.teaserVideosContainer}>
            {teaserVideos.map((video) => (
              <div key={video.id} className={styles.teaserVideoCard}>
                <YouTubeVideo
                  videoId={video.videoId}
                  title={video.title}
                  className={styles.teaserVideo}
                />
              </div>
            ))}
          </div> */}

          {/* <p className={styles.description}>
            Get ready for a magical adventure in learning! SKIDOS and the Smurfs have teamed up to
            bring your kids exciting educational games with their favorite blue buddies. Fun meets
            learning like never before!
          </p> */}
          {/* <FormCtaButton text="Notify Me" /> */}
          {/* <div style={{ width: "100%" }}>
            <FormCtaButton text="Download" />
          </div> */}
        </div>

        {/* Third Frame - Game screenshots */}
        <div className={styles.thirdFrame}>
          <div className={styles.gamesContainer}>
            {gameCards.map((card) => (
              <div key={card.id} className={styles.gameCard}>
                <Image
                  src={`/images/skidos-smurfs/smurf${card.id}1.webp`}
                  width={560}
                  height={420}
                  alt="Smurf Game Screenshot"
                  sizes="(max-width: 480px) 250px, (max-width: 768px) 320px, (max-width: 1024px) 400px, (max-width: 1440px) 480px, 560px"
                />
              </div>
            ))}
          </div>

          {/* <div style={{ paddingLeft: "1.5rem" }}>
            <h1 className={styles.teaserTrailerHeading}>header 1</h1>
            <p className={styles.description}>
              Get ready for a magical adventure in learning! SKIDOS and the Smurfs have teamed up to
              bring your kids exciting educational games with their favorite blue buddies. Fun meets
              learning like never before!
            </p>

            <h1 className={styles.teaserTrailerHeading}>header 2</h1>
            <p className={styles.description}>
              <ul>
                <li>Get ready for a magical adventure in learning! SKIDOS and</li>
                <li>Get ready for a magical adventure in learning! SKIDOS and</li>
                <li>Get ready for a magical adventure in learning! SKIDOS and</li>
              </ul>
            </p>
          </div> */}
        </div>
      </>
    </>
  );
};

export default SmurfsComingSoon;
