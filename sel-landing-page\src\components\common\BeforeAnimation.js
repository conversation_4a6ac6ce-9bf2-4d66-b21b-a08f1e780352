import React, { useLayoutEffect, useRef } from "react";
// icons
import TopTree from "icons/tree_top.png";
import BottomTree from "icons/tree_bottom.png";
import LeftTree from "icons/tree_left.png";
import RightTree from "icons/tree_right.png";
// gsap
import { gsap } from "gsap";

export function BeforeAnimation() {
  const container = useRef();
  const tl = useRef();

  useLayoutEffect(() => {
    const ctx = gsap.context((self) => {
      tl.current = gsap.to(self.selector(".ba_top_tree"), {
        duration: 1,
        top: 0,
      });
      tl.current = gsap.to(self.selector(".ba_bottom_tree"), {
        duration: 1,
        bottom: 0,
      });
      tl.current = gsap.to(self.selector(".ba_left_tree"), {
        duration: 1,
        left: 0,
      });
      tl.current = gsap.to(self.selector(".ba_right_tree"), {
        duration: 1,
        right: 0,
      });
      tl.current = gsap.to(self.selector(".ba_top_tree"), {
        duration: 1.5,
        top: "-100%",
        delay: 1,
      });
      tl.current = gsap.to(self.selector(".ba_bottom_tree"), {
        duration: 1.5,
        bottom: "-100%",
        delay: 1,
      });
      tl.current = gsap.to(self.selector(".ba_left_tree"), {
        duration: 1,
        left: "-50%",
        delay: 1,
      });
      tl.current = gsap.to(self.selector(".ba_right_tree"), {
        duration: 1,
        right: "-50%",
        delay: 1,
      });
    }, container); // <- Scope!
    return () => ctx.revert(); // <- Cleanup!
  }, []);

  return (
    <div className="before_anim_container" ref={container}>
      <div className="tree_box">
        <img src={TopTree} className="ba_top_tree" alt="top tree" />
        <img src={BottomTree} className="ba_bottom_tree" alt="bottom tree" />
        <img src={LeftTree} className="ba_left_tree" alt="left tree" />
        <img src={RightTree} className="ba_right_tree" alt="right tree" />
      </div>
    </div>
  );
}

export default BeforeAnimation;
