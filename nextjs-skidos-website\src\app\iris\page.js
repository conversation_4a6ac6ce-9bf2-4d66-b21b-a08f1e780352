"use client";

import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import dynamic from "next/dynamic";
import Image from "next/image";
import styles from "./styles.module.css";

// Dynamically import Plyr to avoid SSR issues
const Plyr = dynamic(() => import("plyr-react"), {
  ssr: false,
  loading: () => <div>Loading video player...</div>
});

// Import Plyr CSS
import "plyr-react/plyr.css";

const IRISPage = () => {
  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen"
  ];

  const eventTrackerGa4 = (category, action, label, nonInteraction) => {
    // GA4 tracking can be implemented here if needed
    console.log("GA4 Event:", { category, action, label, nonInteraction });
  };

  return (
    <div className={styles.irisMinds}>
      {/* Hero Section */}
      <div className={styles.hero}>
        <div className="container">
          <div className="row d-flex justify-content-center">
            <div className="col-12 col-md-5 text-center">
              <h1 className={styles.heroTitle}>
                An exciting journey to develop early reading skills
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content Box */}
      <div className={styles.irisContentBox}>
        <div className="container" id="player-container">
          {/* Video Player */}
          <div className="row d-flex justify-content-center">
            <div className="col-12 col-md-8">
              <div className={styles.playerContainer}>
                <Plyr
                  id="plyr"
                  controls
                  options={{ volume: 0.1, controls }}
                  source={{
                    type: "video",
                    sources: [
                      {
                        src: "https://youtu.be/HAJHytYL1xA",
                        provider: "youtube",
                      },
                    ],
                  }}
                />
              </div>
            </div>
          </div>

          {/* Feature Sections */}
          <div className={styles.irisFiksContainer}>
            <div className={styles.irisFiksContainerImg}>
              <Image
                src="/images/iris/join-reading-adventure.png"
                alt="Join Reading Adventure"
                width={330}
                height={250}
              />
            </div>
            <div>
              <h2>Join the reading adventure</h2>
              <p>
                Learn letters, phonics, and first words on a delightful treasure hunt!
              </p>
            </div>
          </div>

          <div className={styles.irisFiksContainer}>
            <div className={styles.irisFiksContainerImg}>
              <Image
                src="/images/iris/science-of-reading.png"
                alt="Science Of Reading"
                width={330}
                height={250}
              />
            </div>
            <div>
              <h2>Aligned with Science of Reading (SoR)</h2>
              <p>
                Develop reading fluency and confidence with 20+ expert-approved learning activities
              </p>
            </div>
          </div>
        </div>

        {/* Ribbon Section */}
        <div className={styles.irisRibbonBlock}>
          <div className={styles.irisRibbon}></div>
        </div>

        {/* Testimonials Carousel */}
        <div className={`container ${styles.irisMinds} ${styles.mt7}`}>
          <div className="row mt-3">
            <div className="col-12">
              <div className={`${styles.irisCarousel} ${styles.mt7}`}>
                <Carousel
                  infiniteLoop={true}
                  autoPlay={true}
                  showStatus={false}
                  showIndicators={false}
                  swipeable={false}
                  renderArrowPrev={(clickHandler, hasPrev) => {
                    return (
                      <div className={`${styles.arrow} ${styles.leftArrow}`} onClick={clickHandler}>
                        <button className={`btn btn-primary ${styles.btnPrimary}`}>
                          <Image src="/images/rekindle-minds/left-arrow.png" alt="Left" width={20} height={20} />
                        </button>
                      </div>
                    );
                  }}
                  renderArrowNext={(clickHandler, hasNext) => {
                    return (
                      <div className={`${styles.arrow} ${styles.rightArrow}`} onClick={clickHandler}>
                        <button className={`btn btn-primary ${styles.btnPrimary}`}>
                          <Image src="/images/rekindle-minds/right-arrow.png" alt="Right" width={20} height={20} />
                        </button>
                      </div>
                    );
                  }}
                >
                  {/* Testimonial 1 */}
                  <div className="row carousel">
                    <div className="col-12 col-md-5">
                      <div className={styles.reviewImage}>
                        <Image src="/images/iris/iris-review-one-new.png" alt="Review 1" width={400} height={300} />
                      </div>
                    </div>
                    <div className={`col-12 col-md-7 ${styles.rightBlock}`}>
                      <h2>Hear from Educators and Parents</h2>
                      <p>
                        "IRIS has exceeded my expectations. The app's curriculum covers all aspects of early language learning, and its vibrant visuals and catchy tunes keep children engaged and enthusiastic."
                        <br /> <br />Jaya Sharma, Child Learning Expert
                      </p>
                    </div>
                  </div>

                  {/* Testimonial 2 */}
                  <div className="row carousel">
                    <div className="col-12 col-md-5">
                      <div className={styles.reviewImage}>
                        <Image src="/images/iris/iris-review-two-new.png" alt="Review 2" width={400} height={300} />
                      </div>
                    </div>
                    <div className={`col-12 col-md-7 ${styles.rightBlock}`}>
                      <h2>Hear from Educators and Parents</h2>
                      <p>
                        "IRIS is an educational masterpiece. It has given my child a solid understanding of English letters, sounds, and word formation. The progress they've made is truly remarkable! I highly recommend"
                        <br /> <br />- Alice Williams, School Counselor & Mom
                      </p>
                    </div>
                  </div>

                  {/* Testimonial 3 */}
                  <div className="row carousel">
                    <div className="col-12 col-md-5">
                      <div className={styles.reviewImage}>
                        <Image src="/images/iris/iris-review-three-new.png" alt="Review 3" width={400} height={300} />
                      </div>
                    </div>
                    <div className={`col-12 col-md-7 ${styles.rightBlock}`}>
                      <h2>Hear from Educators and Parents</h2>
                      <p>
                        "As a parent, I'm thrilled with IRIS! It's the perfect blend of education and entertainment. My child looks forward to their English lessons every day, thanks to the app's inviting games."
                        <br /> <br />- Emma Daniels, Mom to 3 y/o
                      </p>
                    </div>
                  </div>
                </Carousel>
              </div>
            </div>
          </div>

          {/* Games Section */}
          <div className={`row d-flex justify-content-center ${styles.mt7}`}>
            <span className={styles.gamesHeading}>Explore Now In Top SKIDOS Games</span>
            <div className={styles.gamesBlock}>
              <div className={styles.games}>
                <Image
                  width={200}
                  height={160}
                  src="/images/rekindle-minds/bike-racing.png"
                  alt="Skidos Games"
                />
                <span>Bike Racing</span>
                <a href="https://apps.apple.com/us/app/cool-math-games-kids-racing/id1319262120">
                  <button
                    onClick={() =>
                      eventTrackerGa4(
                        "button",
                        "iris_bike_racing_download_clicked",
                        "Bike Racing Download Button",
                        false
                      )
                    }
                  >
                    Download
                  </button>
                </a>
              </div>
              <div className={styles.games}>
                <Image width={200} height={160} src="/images/rekindle-minds/Doctor.png" alt="Skidos Games" />
                <span>Doctor</span>
                <a href="https://apps.apple.com/us/app/doctor-games-for-kids/id1506886061">
                  <button
                    onClick={() =>
                      eventTrackerGa4(
                        "button",
                        "iris_doctor_download_clicked",
                        "Doctor Download Button",
                        false
                      )
                    }
                  >
                    Download
                  </button>
                </a>
              </div>
              <div className={styles.games}>
                <Image width={200} height={160} src="/images/rekindle-minds/Bath.png" alt="Skidos Games" />
                <span>Bath</span>
                <a href="https://apps.apple.com/us/app/learning-games-for-kids/id1483744837">
                  <button
                    onClick={() =>
                      eventTrackerGa4(
                        "button",
                        "iris_bath_download_clicked",
                        "Bath Download Button",
                        false
                      )
                    }
                  >
                    Download
                  </button>
                </a>
              </div>
              <div className={styles.games}>
                <Image
                  width={200}
                  height={160}
                  src="/images/rekindle-minds/Supertstore.png"
                  alt="Skidos Games"
                />
                <span>Superstore</span>
                <a href="https://apps.apple.com/us/app/fun-games-kids-preschool-math/id1497549298">
                  <button
                    onClick={() =>
                      eventTrackerGa4(
                        "button",
                        "iris_superstore_download_clicked",
                        "Superstore Download Button",
                        false
                      )
                    }
                  >
                    Download
                  </button>
                </a>
              </div>
            </div>
          </div>

          {/* Certification Section */}
          <div className={`row container-block d-flex justify-content-center ${styles.mt7}`}>
            <span className={styles.gamesHeading}>
              Safe Play- Learning For Your Kid
            </span>
            <div className={styles.certificationBlock}>
              <div className={styles.certification}>
                <Image src="/images/iris/iris-coppa.png" alt="Skidos Certification" width={150} height={150} />
                <p className="mt-2">Strict privacy compliance</p>
              </div>
              <div className={styles.certification}>
                <Image src="/images/iris/iris-certification-two.png" alt="Skidos Certification" width={150} height={150} />
                <p className="mt-2">Designed by educators & experts</p>
              </div>
              <div className={styles.certification}>
                <Image src="/images/iris/iris-certification-three.png" alt="Skidos Certification" width={150} height={150} />
                <p className="mt-2">Guided by Science</p>
              </div>
              <div className={styles.certification}>
                <Image src="/images/iris/iris-certification-one.png" alt="Skidos Certification" width={150} height={150} />
                <p className="mt-2">Secure learning environment</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IRISPage;
