{"node": {"8cc183ddabd4789d521472e73c5c5970162b00d1": {"workers": {"app/rekindle/page": "(action-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CSKIDOS%5C%5CFront-end-proj%5C%5Cnextjs-skidos-website%5C%5Csrc%5C%5Cactions%5C%5Cset-language.js%22%2C%5B%22default%22%5D%5D%5D&__client_imported__=true!", "app/iris/page": "(action-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CSKIDOS%5C%5CFront-end-proj%5C%5Cnextjs-skidos-website%5C%5Csrc%5C%5Cactions%5C%5Cset-language.js%22%2C%5B%22default%22%5D%5D%5D&__client_imported__=true!"}, "layer": {"app/rekindle/page": "action-browser", "app/iris/page": "action-browser"}}}, "edge": {}, "encryptionKey": "TfFhEMsCLmHR2PNpQeVUZUFybzq8imiSPT4VnaT498w="}