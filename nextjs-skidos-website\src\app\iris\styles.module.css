/* IRIS Page Styles */
.irisMinds .containerBlock .blockImg img {
  width: 300px;
}

.irisMinds .playerContainer {
  margin-top: -180px;
  padding: 25px;
}

.irisMinds .playerContainer .plyr {
  border-radius: 15px;
  border: 3px solid #fff;
}

.irisMinds .containerBlock {
  align-items: center;
  justify-content: center;
}

.irisMinds .hero iframe {
  position: absolute;
}

.irisMinds h2,
.irisMinds .heroTitle {
  font-weight: 700;
  font-size: 2.1rem;
  margin-top: 23px;
}

.irisFiksContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 5rem;
  margin-bottom: 4rem;
}

.irisFiksContainer > div {
  padding: 0 3rem;
  width: 40%;
  align-items: center;
}

.irisFiksContainerImg img {
  width: 330px;
}

.containerBlock p {
  font-weight: 500;
  font-size: 1.3rem;
}

.irisMinds .irisRibbonBlock {
  position: relative;
  margin-bottom: 350px;
  margin-top: 30px;
}

.irisMinds .irisRibbon {
  position: absolute;
  background-image: url('/images/iris/iris-flyer.png');
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 225px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.irisMinds .mt7 {
  margin-top: 5rem;
}

.irisMinds .hero {
  height: 680px;
  width: 100%;
  background-image: url('/images/iris/iris-landing-header.png');
  background-size: cover;
  background-position: center center;
}

.irisContentBox {
  background-image: url('/images/iris/iris-content-background.png');
  background-size: cover;
}

.irisMinds .hero .header {
  padding-top: 97px;
  position: relative;
}

.irisMinds .btnPrimary,
.btnGhost {
  position: relative;
  background-color: #f66007;
  outline: none;
  padding: 15px 25px;
  border: 1px solid #fff;
  font-weight: 700;
  border-radius: 20px;
}

.irisMinds .btnPrimary:hover,
.btnGhost:hover {
  border: 1px solid rgba(153, 153, 153, 0.753);
}

.btnGhost {
  background-color: transparent;
  border: 1px solid #333;
  color: #333;
  margin-right: 15px;
  padding-right: 35px;
}

.btnGhost img {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 7px;
}

.irisMinds .certificationBlock {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.irisMinds .certification {
  width: 242px;
  margin: 0 12px;
  text-align: center;
  margin-top: 20px;
}

.certification img {
  width: 150px;
}

.gamesHeading {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
}

.irisMinds .gamesBlock {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.irisMinds .games {
  margin: 0 12px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #f2f2f2;
  border-radius: 4px;
}

.irisMinds .games img {
  display: block;
}

.irisMinds .games a {
  text-decoration: none;
}

.irisMinds .games:hover {
  box-shadow: 0 4px 8px rgba(93, 93, 93, 0.3);
  transition: box-shadow 400ms ease-in-out;
}

.irisMinds .games span {
  font-weight: 700;
  display: inline-block;
  font-size: 20px;
  margin: 0.5rem 0;
  text-align: left !important;
}

.irisMinds .games button {
  background-color: #f66007;
  display: block;
  width: 100%;
  border-radius: 4px;
  height: 48px;
  font-size: 1.3rem;
  font-weight: 700;
  outline: none;
  border: none;
  color: #ffffff;
}

.irisMinds .irisCarousel {
  background-color: #224154;
  border-radius: 20px;
}

.irisMinds .carouselRoot {
  padding-top: 30px;
}

.irisMinds .irisCarousel .carousel .reviewImage {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.irisMinds .irisCarousel .carousel .reviewImage img {
  width: 400px;
}

.irisMinds .irisMinds .rightBlock {
  text-align: left;
  padding: 0 25px;
}

.irisMinds .irisCarousel .carousel h2 {
  color: #fff;
}

.irisMinds .irisCarousel .carousel p {
  color: #fff;
  font-weight: 500;
  padding-right: 50px;
  line-height: 1.75rem;
}

.irisMinds .irisCarousel .arrow {
  display: inline-block;
  position: absolute;
  bottom: 0;
  right: 50%;
  cursor: pointer;
  z-index: 9;
}

.irisMinds .mr50 {
  margin: 50px;
}

.irisMinds .irisCarousel .arrow.rightArrow {
  margin-right: -100px;
}

.irisMinds .irisCarousel .btnPrimary {
  border-radius: 28px;
  border-color: #ffffff40;
  padding: 20px 25px;
}

.irisMinds .irisCarousel .btnPrimary:hover {
  border-color: #ffffffa4;
}

.irisFiksContainer p {
  font-size: 1.2rem;
  font-weight: 600;
}

/* Video player styles - moved to global styles */

.vjsThemeSkidos {
  --vjs-theme-skidos--primary: #00c6a3;
  --vjs-theme-skidos--secondary: #fff;
}

/* hide control - moved to global styles */

/* Mobile Responsive Styles */
@media only screen and (max-width: 450px) {
  .irisMinds .irisCarousel .carousel .reviewImage img {
    width: 340px;
  }

  .irisMinds {
    margin-top: 45px;
  }

  .irisMinds .heroTitle {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 10px;
  }

  .irisFiksContainer h2 {
    font-size: 2rem;
    font-weight: 700;
  }

  .irisMinds .mt7 {
    margin-top: 2rem;
  }

  .irisMinds .irisRibbonBlock {
    margin-bottom: 265px;
    margin-top: 20px;
  }

  .irisMinds .irisRibbon {
    background-image: url('/images/iris/iris-flyer-mb.png');
  }

  .irisMinds .irisCarousel .carousel .reviewImage {
    transform: scale(0.9);
    margin-top: -15px;
  }

  .irisMinds .carouselRoot {
    padding-top: 25px;
  }

  .irisMinds .irisMinds .rightBlock {
    margin-top: -10px;
    padding-bottom: 70px;
  }

  .irisMinds .playerContainer {
    padding: 5px;
    margin-top: -90px;
  }

  .irisContentBox {
    background-image: url('/images/iris/iris-content-background-mb.png');
    background-size: auto;
    background-repeat: no-repeat;
  }

  .irisMinds .hero {
    background-image: url('/images/iris/iris-landing-header-mb.png');
    background-position: center center;
    background-size: cover;
    height: 468px;
  }

  .irisFiksContainer {
    flex-direction: column;
    margin-bottom: 0rem;
  }

  .irisFiksContainer > div {
    padding: 0;
    width: 100%;
  }

  .irisFiksContainerImg {
    display: flex;
    justify-content: center;
  }
}

/* iPad */
@media all and (device-width: 768px) and (device-height: 1024px) and (orientation: portrait) {
  .irisFiksContainer > div {
    width: 70%;
  }

  .irisMinds .carouselRoot {
    padding-top: 05px;
  }

  .irisMinds .irisCarousel .carousel .reviewImage {
    transform: scale(0.7);
  }

  .irisMinds .irisCarousel .carousel p {
    padding-right: 0;
  }

  .irisMinds .irisMinds .rightBlock {
    padding-bottom: 70px;
  }
}

@media all and (device-width: 820px) and (device-height: 1180px) and (orientation: portrait) {
  .irisMinds .carouselRoot {
    padding-top: 05px;
  }

  .irisMinds .irisCarousel .carousel .reviewImage {
    transform: scale(0.7);
  }

  .irisMinds .irisCarousel .carousel p {
    padding-right: 0;
  }

  .irisMinds .irisMinds .rightBlock {
    padding-bottom: 70px;
  }
}

@media all and (min-width: 768px) and (orientation: portrait) {
  .irisFiksContainer > div {
    width: 60%;
    padding: 0 1.5rem;
  }
}

@media all and (min-width: 1180px) and (min-height: 820px) {
  .irisMinds .irisMinds .rightBlock {
    margin-top: -64px;
  }

  .irisMinds .irisCarousel .carousel .reviewImage img {
    width: 350px;
  }

  .irisMinds .irisCarousel .btnPrimary {
    padding: 15px 15px;
  }
}

@media all and (device-width: 1180px) and (device-height: 820px) and (orientation: landscape) {
  .irisContentBox {
    background-position: center;
  }

  .irisMinds h2,
  .irisMinds .heroTitle {
    font-size: 1.7rem;
    margin-top: 59px;
  }
}