import React, { useState } from "react";
import MascotGroup from "icons/rekindle-minds/mascot-group.png";
import {
  appPath,
  getPlayers,
  avatarBaseUrl,
  homePage,
} from "services/Constants";

export function Footer() {
  // onClick={() => (show ? setShow(false) : setShow(true))}
  return (
    <div className="footer">
      <div className="container">
        <div className="mascot-group">
          <img src={MascotGroup} />
        </div>
        <div className="row footer-nav">
          <div className="col-sm-3 text-center">
            <h4>About us</h4>
            <p>
              Skidos Labs ApS,
              <br />
              Titangade 11
              <br />
              2200 København N<br />
              CVR: 37212962
            </p>
          </div>
          <div className="col-sm-3 text-center">
            <h4>Support</h4>
            <ul>
              <li>
                <a href="https://support.skidos.com">FAQ</a>
              </li>
              <li>
                <a href={homePage + "privacy-policy/"}>Privacy Policy</a>
              </li>
              <li>
                <a href={homePage + "terms/"}>Terms of Service</a>
              </li>
            </ul>
          </div>
          <div className="col-sm-3 text-center">
            <h4>Resources</h4>
            <ul>
              <li>
                <a href={homePage + "blog/"}>Blog</a>
              </li>
              <li>
                <a href={homePage + "parenting-guide/"}>Parenting guide</a>
              </li>
              <li>
                <a href={homePage + "press/"}>Press</a>
              </li>
              <li>
                <a href={homePage + "career"}>Career</a>
              </li>
              <li>
                <a href={homePage + "mission-vison"}>Mission & Vision</a>
              </li>
            </ul>
          </div>
          <div className="col-sm-3 text-center">
            <h4>Follow us on</h4>
            <a
              href="https://www.facebook.com/skidosgames/"
              className="social-icon facebook"
            ></a>
            <a
              href="https://www.linkedin.com/company/skidos-learning/"
              className="social-icon linkedin"
            ></a>
            <a
              href="https://www.youtube.com/user/playatskidos"
              className="social-icon youtube"
            ></a>
            <a
              href="https://www.instagram.com/skidos_games/"
              className="social-icon instagram"
            ></a>
          </div>
        </div>
      </div>
      <div className="footer-second">
        <div className="container">
          <div className="row">
            <div className="col-sm-12">
              <p>© 2023 SKIDOS.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Footer;
