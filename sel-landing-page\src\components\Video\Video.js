import React, { useState, useLayoutEffect, useRef, useEffect } from "react";
// components
import Header from "components/Header";
import Footer from "components/Footer";
import VideoContainer from "./components/VideoContainer";
// import DetailLeftNav from "components/common/DetailLeftNav";
import GameTileBox from "components/common/GameTileBox";
import BeforeAnimation from "../common/BeforeAnimation";
import GameNavMobile from "../common/GameNavMobile";
// external css
import "./Video.css";
// images
import leftTree from "icons/detail_left_tree.png";
import rightTree from "icons/detail_right_tree.png";
import freePoly from "icons/free_game_polygon.png";
import premiumPoly from "icons/premium_game_polygon.png";
import videosPoly from "icons/videos_polygon.png";
import PolygonBg from "icons/polygon_bg.png";
import PolygonYellow from "icons/polygon_yellow.png";
// contants
import { appPath } from "services/Constants";
// gsap
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useLocation, useNavigate, Link } from "react-router-dom";
import classNames from "classnames";

/* value proposition modal*/
import logo from "icons/auth/logo.png";
import moyoWellDone from "icons/Owl_Well_Done.png";
import premiumBadge from "icons/premium-badge.png";

import Modal from "components/common/Modal";

gsap.registerPlugin(ScrollTrigger);

export function Video() {
  const location = useLocation();

  const [showLoader, setShowLoader] = useState(true);
  const [initialize, setInitialize] = useState(false);
  let navigate = useNavigate();

  const container = useRef();
  const tl = useRef();

  /* show valueproposition modal*/
  const [showModal, setShowModal] = useState(false);

  const [gameType, setGameType] = useState("Free Games");

  const valuePropositionModal = (setStatus) => {
    setShowModal(setStatus);
  };

  useEffect(() => {
    setShowLoader(true);
  }, []);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useLayoutEffect(() => {
    setTimeout(() => {
      setShowLoader(false);
    }, 2000);

    setTimeout(() => {
      setInitialize(true);
    }, 1000);

    const ctx = gsap.context((self) => {
      tl.current = gsap.from(self.selector(".game_box_outer"), {
        duration: 1,
        marginLeft: "-25%",
        delay: 1,
      });
    }, container); // <- Scope!
    return () => ctx.revert(); // <- Cleanup!
  }, []);

  const navMobileCatHandle = (selectedType) => {
    setGameType(selectedType);
  };

  if (location?.state?.videoTitle == undefined) navigate("/");

  return (
    <div ref={container}>
      {showLoader && <BeforeAnimation />}
      <div style={{ visibility: initialize ? "visible" : "hidden" }}>
        <div className="screen1_bg">
          <div className="bg_clouds">
            <Header
              isSubscribed={location?.state?.isSubscribed}
              userEmail={location?.state?.userEmail}
            />
            <img src={leftTree} className="left_tree" alt="left tree" />
            <img src={rightTree} className="right_tree" alt="right tree" />
            <div className="container">
              <div className="row">
                <div className="col-sm-1 col-md-1 col-lg-2 relative">
                  <Link component="span" to="/" state={{ from: "detail" }}>
                    <button className="btn-white go_back_btn">
                      &lt; Go back to home
                    </button>
                  </Link>
                </div>
                <div className="col-sm-10 col-md-10 col-lg-8 no-padding">
                  <VideoContainer
                    videoTitle={location?.state?.videoTitle}
                    videoUrl={location?.state?.videoUrl}
                  />
                </div>
                <div className="col-sm-1 col-md-1 col-lg-2 hidden-xs">
                  &nbsp;
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="detail_purple_layer">
          <div className="detail_purple_bg" data-speed=".75">
            &nbsp;
          </div>
        </div>
        <div className="detail_screen2">
          <div className="detail_green_bg" data-speed=".45">
            <div className="container">
              <div className="row">
                <div className="col-md-1"></div>
                <div className="col-md-10 detail_content_aligner">
                  <div className="video_type_container">
                    Tired of downloading a new game each time? Now Play Online
                    without any hassle with SKIDOS! Action,
                    <br /> Adventure, Racing, Drama- scroll down to choose a
                    game and just start playing
                  </div>
                </div>
                <div className="col-md-1"></div>
              </div>
              <div className="row">
                <div className="col-md-1"></div>
                <div className="col-md-10">
                  <div className="visible-xs visible-sm detail_content_aligner mobile_nav">
                    <GameNavMobile
                      type="Free Games"
                      navMobileCatHandle={navMobileCatHandle}
                      typeList={["Free Games", "Premium Games", "Videos"]}
                    />
                  </div>
                </div>
                <div className="col-md-1"></div>
              </div>
              <div className="row">
                <div className="col-md-1"></div>
                <div className="col-md-10 detail_content_aligner overlap_zindex hidden-xs hidden-sm">
                  <div className="game_type_box">
                    <div
                      className={classNames("game_type_tile", {
                        ["list_item_active"]: gameType === "Free Games",
                      })}
                      onClick={() => setGameType("Free Games")}
                    >
                      <span className="gtlc_nav_icon rightgap12">
                        <img
                          src={PolygonBg}
                          alt="nav icon background"
                          className="nav_brown_bg"
                        />
                        <img
                          src={PolygonYellow}
                          alt="nav background"
                          className="nav_yellow_bg"
                        />
                        <img
                          src={freePoly}
                          alt="free games"
                          className="nav_img"
                        />
                      </span>
                      Free Games
                    </div>
                    <div
                      className={classNames("game_type_tile", {
                        ["list_item_active"]: gameType === "Premium Games",
                      })}
                      onClick={() => setGameType("Premium Games")}
                    >
                      <span className="gtlc_nav_icon rightgap12">
                        <img
                          src={PolygonBg}
                          alt="nav icon background"
                          className="nav_brown_bg"
                        />
                        <img
                          src={PolygonYellow}
                          alt="nav background"
                          className="nav_yellow_bg"
                        />
                        <img
                          src={premiumPoly}
                          alt="premium games"
                          className="nav_img"
                        />
                      </span>
                      Premium Games
                    </div>
                    <div
                      className={classNames("game_type_tile", {
                        ["list_item_active"]: gameType === "Videos",
                      })}
                      onClick={() => setGameType("Videos")}
                    >
                      <span className="gtlc_nav_icon rightgap12">
                        <img
                          src={PolygonBg}
                          alt="nav icon background"
                          className="nav_brown_bg"
                        />
                        <img
                          src={PolygonYellow}
                          alt="nav background"
                          className="nav_yellow_bg"
                        />
                        <img
                          src={videosPoly}
                          alt="videos"
                          className="nav_img"
                        />
                      </span>
                      Videos
                    </div>
                  </div>
                </div>
                <div className="col-md-1"></div>
              </div>
              <div className="row">
                <div className="col-md-12 detail_content_aligner topgap50 topgap-xs-0 topgap-sm-0">
                  <div className="row">
                    <div className="col-sm-2"></div>
                    {/* <div className="col-sm-12 col-md-12 col-lg-3 hidden-xs hidden-sm">
                      <DetailLeftNav list={navList} />
                    </div> */}
                    <div className="col-sm-12 col-md-12 col-lg-8">
                      <GameTileBox
                        listType={gameType}
                        isSubscribed={location?.state?.isSubscribed}
                        valuePropositionModal={valuePropositionModal}
                        userEmail={location?.state?.userEmail}
                      />
                    </div>
                    <div className="col-sm-1"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
      <Modal show={showModal} className="value-proposition">
        <span className="close-btn" onClick={() => setShowModal(false)}></span>
        <div className="container">
          <div className="row">
            <div className="col-sm-7">
              <ul>
                <img src={logo} className="skidos-logo" />
                <li>
                  <span></span>Unlimited play, no downloads.
                </li>
                <li>
                  <span></span> Child-friendly and safe games.
                </li>
                <li>
                  <span></span> Play on Mobile, Tablet or Desktop.
                </li>
                <li>
                  <span></span> Games for all ages and interests.
                </li>
                <li>
                  <span></span> Trusted by 4 million parents
                </li>
              </ul>
              <img src={moyoWellDone} className="moyo-well-done" />
            </div>
            <div className="col-sm-5 premium-section">
              <div className="premium-pass mt-4">
                <img src={premiumBadge} />
                <span>Premium</span> Pass
              </div>
              <h3 className="mt-3">Unlock this and more games & videos</h3>
              <button
                className="btn btn-warning premium-btn mt-4"
                onClick={() =>
                  window.location.replace(appPath + "acquisition_instant_game")
                }
              >
                Activate Premium
              </button>
              {!location?.state?.userEmail && (
                <>
                  <p className="mt-4 login-action">
                    Already a member?{" "}
                    <span
                      onClick={() =>
                        window.location.replace(appPath + "ig_login")
                      }
                    >
                      Log in to play
                    </span>
                  </p>
                </>
              )}
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default Video;
