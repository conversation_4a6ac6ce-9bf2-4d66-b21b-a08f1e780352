/* RekindleMinds Page Styles */
.rekindleMinds .containerBlock .blockImg img {
  max-width: 100%;
}

.rekindleMinds .playerContainer {
  margin-top: -150px;
  padding: 25px;
}

.rekindleMinds .playerContainer .plyr {
  border-radius: 15px;
  border: 1px solid #fff;
}

.rekindleMinds .containerBlock {
  align-items: center;
  justify-content: center;
}

.rekindleMinds .hero iframe {
  position: absolute;
}

.rekindleMinds h2,
.rekindleMinds .heroTitle {
  font-weight: 700;
  font-size: 1.75rem;
  margin-top: 15px;
}

.containerBlock p {
  font-weight: 500;
}

.rekindleMinds .ribbonBlock {
  position: relative;
  margin-bottom: 350px;
  margin-top: 30px;
}

.rekindleMinds .ribbon {
  position: absolute;
  background-image: url('/images/rekindle-minds/ribbon.png');
  width: 100%;
  height: 225px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.rekindleMinds .mt7 {
  margin-top: 5rem;
}

.rekindleMinds .hero {
  background-color: rgb(75 232 193);
  height: 680px;
  width: 100%;
}

.rekindleMinds .hero .header {
  padding-top: 170px;
  position: relative;
}

.rekindleMinds .btnPrimary,
.btnGhost {
  position: relative;
  background-color: #f66007;
  outline: none;
  padding: 15px 25px;
  border: 1px solid #fff;
  font-weight: 700;
  border-radius: 20px;
}

.rekindleMinds .btnPrimary:hover,
.btnGhost:hover {
  border: 1px solid rgba(153, 153, 153, 0.753);
}

.btnGhost {
  background-color: transparent;
  border: 1px solid #333;
  color: #333;
  margin-right: 15px;
  padding-right: 35px;
}

.btnGhost img {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 7px;
}

.rekindleMinds .certificationBlock {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.rekindleMinds .certification {
  width: 242px;
  margin: 0 12px;
  text-align: center;
  margin-top: 20px;
}

.gamesHeading {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
}

.rekindleMinds .gamesBlock {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.rekindleMinds .games {
  margin: 0 12px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #f2f2f2;
  border-radius: 4px;
}

.rekindleMinds .games img {
  display: block;
}

.rekindleMinds .games a {
  text-decoration: none;
}

.rekindleMinds .games:hover {
  box-shadow: 0 4px 8px rgba(93, 93, 93, 0.3);
  transition: box-shadow 400ms ease-in-out;
}

.rekindleMinds .games span {
  font-weight: 700;
  display: inline-block;
  font-size: 20px;
  margin: 0.5rem 0;
  text-align: left !important;
}

.rekindleMinds .games button {
  background-color: #f66007;
  display: block;
  width: 100%;
  border-radius: 4px;
  height: 48px;
  font-size: 1.3rem;
  font-weight: 700;
  outline: none;
  border: none;
  color: #ffffff;
}

.rekindleMinds .rekindleCarousel {
  background-color: #b877f7;
  border-radius: 20px;
}

.rekindleMinds .carouselRoot {
  padding-top: 50px;
}

.rekindleMinds .rekindleCarousel .carousel .reviewImage {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rekindleMinds .rekindleCarousel .carousel .reviewImage .left {
  position: absolute;
  width: 230px;
  margin-left: -125px;
}

.rekindleMinds .rekindleCarousel .carousel .reviewImage .center {
  width: 280px;
  z-index: 2;
}

.rekindleMinds .rekindleCarousel .carousel .reviewImage .right {
  position: absolute;
  width: 230px;
  right: 60px;
}

.rekindleMinds .rekindleMinds .rightBlock {
  text-align: left;
  padding: 0 25px;
  margin-top: 25px;
}

.rekindleMinds .rekindleCarousel .carousel h2 {
  color: #fff;
}

.rekindleMinds .rekindleCarousel .carousel p {
  color: #fff;
  font-weight: 500;
  padding-right: 50px;
  line-height: 1.75rem;
}

.rekindleMinds .rekindleCarousel .arrow {
  display: inline-block;
  position: absolute;
  bottom: 0;
  right: 50%;
  cursor: pointer;
  z-index: 9;
}

.rekindleMinds .mr50 {
  margin: 50px;
}

.rekindleMinds .rekindleCarousel .arrow.rightArrow {
  margin-right: -100px;
}

.rekindleMinds .rekindleCarousel .btnPrimary {
  border-radius: 28px;
  border-color: #ffffff40;
  padding: 20px 25px;
}

.rekindleMinds .rekindleCarousel .btnPrimary:hover {
  border-color: #ffffffa4;
}

.rekindleMinds .rekindleCarousel .dots {
  position: absolute;
  z-index: 9;
  bottom: 20px;
  display: flex;
  align-items: center;
}

.rekindleMinds .rekindleCarousel .dots span {
  background-color: #e9d2ff;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 5px;
}

.rekindleMinds .rekindleCarousel .dots span.active {
  background-color: rgb(140 73 193);
  width: 15px;
  height: 15px;
}

/* Video player styles - moved to global styles */

.vjsThemeSkidos {
  --vjs-theme-skidos--primary: #00c6a3;
  --vjs-theme-skidos--secondary: #fff;
}

/* hide control - moved to global styles */

/* Mobile Responsive Styles */
@media only screen and (max-width: 450px) {
  .rekindleMinds h2,
  .rekindleMinds .heroTitle {
    margin-top: 25px;
  }

  .rekindleMinds .mt7 {
    margin-top: 2rem;
  }

  .rekindleMinds .ribbonBlock {
    margin-bottom: 265px;
    margin-top: 20px;
  }

  .rekindleMinds .rekindleCarousel .carousel .reviewImage .right {
    right: 5px;
  }

  .rekindleMinds .rekindleCarousel .carousel .reviewImage {
    transform: scale(0.9);
    margin-top: -15px;
  }

  .rekindleMinds .carouselRoot {
    padding-top: 25px;
  }

  .rekindleMinds .rekindleMinds .rightBlock {
    margin-top: -10px;
    padding-bottom: 70px;
  }

  .rekindleMinds .playerContainer {
    margin-top: -250px;
  }
}

/* iPad */
@media all and (device-width: 768px) and (device-height: 1024px) and (orientation: portrait) {
  .rekindleMinds .carouselRoot {
    padding-top: 05px;
  }

  .rekindleMinds .rekindleCarousel .carousel .reviewImage {
    transform: scale(0.7);
  }

  .rekindleMinds .rekindleCarousel .carousel .reviewImage .right {
    right: -40px;
  }

  .rekindleMinds .rekindleCarousel .carousel p {
    padding-right: 0;
  }

  .rekindleMinds .rekindleMinds .rightBlock {
    padding-bottom: 70px;
  }
}

@media all and (device-width: 820px) and (device-height: 1180px) and (orientation: portrait) {
  .rekindleMinds .carouselRoot {
    padding-top: 05px;
  }

  .rekindleMinds .rekindleCarousel .carousel .reviewImage {
    transform: scale(0.7);
  }

  .rekindleMinds .rekindleCarousel .carousel .reviewImage .right {
    right: -40px;
  }

  .rekindleMinds .rekindleCarousel .carousel p {
    padding-right: 0;
  }

  .rekindleMinds .rekindleMinds .rightBlock {
    padding-bottom: 70px;
  }
}