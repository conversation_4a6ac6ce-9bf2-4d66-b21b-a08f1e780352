"use client";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import { faArrowRight, faBriefcase, faLocationDot } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Carousel from "../../components/CarouselCareer";
import styles from "./styles.module.css";

function Page() {
  const t = useTranslations("careers");

  const jobs = [
    {
      title: "Senior Software Engineer (Golang)",
      exp: "2+ years of experience",
      location: "Delhi, Noida",
      applyURL: "https://skidos.bamboohr.com/careers/115",
    },
  ];

  const JobCard = ({ job }) => {
    return (
      <div className={styles.jobCard}>
        <h2 className={styles.jobTitle}>{job.title}</h2>
        <div className={styles.jobDetail}>
          <span className={styles.icon}>
            <FontAwesomeIcon icon={faBriefcase} />
          </span>
          {job.exp}
        </div>
        <div className={styles.jobDetail}>
          <span className={styles.icon}>
            <FontAwesomeIcon icon={faLocationDot} />
          </span>
          {job.location}
        </div>
        <a href={job.applyURL} target="_blank" rel="noreferrer">
          <button className={styles.applyButton}>
            {t("apply_now")}
            <span className={styles.arrow}>
              <FontAwesomeIcon icon={faArrowRight} />
            </span>
          </button>
        </a>
      </div>
    );
  };

  useTogglePinkFooter(true);

  return (
    <>
      <title>{t("title")}</title>
      <meta name="description" content={t("meta_description")} />
      <div className={styles.careerWrapper}>
        <Carousel />
        {jobs.length > 0 && (
          <div className={styles.jobsContainer}>
            <h1>{t("job_available")}</h1>
            <div className={styles.jobGrid}>
              {jobs.map((job, index) => (
                <JobCard key={index} job={job} />
              ))}
            </div>
          </div>
        )}
        <div>
          <div className={styles.noJobContainer}>
            <div className={styles.comingSoonContainer}>
              <Image src="/images/career/suitcase.webp" height={40} width={72} />
              {jobs.length > 0 ? <h2>{t("join_us")}</h2> : <h2>{t("jobs_coming_soon")}</h2>}
              <p>{t("company_mission")}</p>
              <button
                className={styles.linkedinCta}
                onClick={() =>
                  (window.location.href =
                    "https://www.linkedin.com/company/9246535/admin/dashboard/")
                }
              >
                Linkedin <Image src="/images/career/linkedinLogo.webp" height={36} width={36} />
              </button>
            </div>
          </div>
        </div>
        <div className={styles.eduInnoCont}>
          <h1>{t("explore_perks")}</h1>
          <div className={styles.gridContainer}>
            {[
              { img: "suitcase.webp", title: "health_coverage", desc: "health_description" },
              { img: "parent.webp", title: "parental_leave", desc: "parental_description" },
              { img: "wellness.webp", title: "wellness", desc: "wellness_description" },
              { img: "growth.webp", title: "growth_opportunities", desc: "growth_description" },
              { img: "leave.webp", title: "leave_policy", desc: "leave_description" },
              { img: "engagement.webp", title: "team_engagement", desc: "engagement_description" },
              {
                img: "collaborate.webp",
                title: "collaborative_culture",
                desc: "collaborative_description",
              },
              { img: "cash.webp", title: "tax_assistance", desc: "tax_description" },
            ].map((item, index) => (
              <div key={index} className={styles.box}>
                <Image src={`/images/career/${item.img}`} width={72} height={40} />
                <h2>{t(item.title)}</h2>
                <p>{t(item.desc)}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}

export default Page;
