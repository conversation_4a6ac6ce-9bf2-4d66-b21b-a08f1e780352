.stepper-back-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
}
.stepper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0.2rem 1rem;
}
.step {
  display: flex;
  align-items: center;
}
.back-button {
  cursor: pointer;
}

.circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #d3d3d3;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
}

.circle.active {
  background-color: #4caf50;
}

.line {
  height: 4px;
  width: 30px;
  background-color: #d3d3d3;
  transition: background-color 0.3s ease;
}

.line.completed {
  background: linear-gradient(to right, #4caf50, rgba(76, 175, 80, 0.2));
}
