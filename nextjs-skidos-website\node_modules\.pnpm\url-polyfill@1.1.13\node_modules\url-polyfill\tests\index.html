<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>Tests</title>

  <script type="text/javascript">
    ['url-polyfill'].forEach(function(fileName) {
      var script = document.createElement('script');
      script.src = '../' + fileName + '.js?timeout=' + Math.random().toString();
      document.head.appendChild(script);
    });
  </script>

</head>
<body>
  Test executing...
</body>
</html>