.articleWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}

.contactUsHeader {
  text-align: center;
  margin-bottom: 3rem;
}
.contactUsHeader h1 {
  font-size: 2rem;
}
.contactUsHeader p {
  font-size: 1.5rem;
  font-family: var(--font-poppins);
  padding: 0 1.5rem;
  text-align: center;
}

.videoContentWrapper {
  max-width: 959px;
  display: flex;
  justify-content: center;
  gap: 0px;
  margin: 4rem auto;
  flex-direction: column;
}
.videoContentWrapper > div {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
}
.videoContentWrapper > div:nth-child(1) {
  align-items: center;
}
.videoContentWrapper > div:nth-child(2) {
  padding: 0 1rem;
  box-sizing: border-box;
}
.videoPlyer {
  padding: 1rem 1rem;
  box-sizing: border-box;
  width: 100%;
  height: 346px;
  border-radius: 10px;
  border: none;
}

.videoContentWrapper > div h1 {
  font-size: 2rem;
  margin: 0rem;
}
.videoContentWrapper > div p {
  font-family: var(--font-poppins);
  font-size: 1.3rem;
}

.getPremiumBtn {
  display: inline;
  width: auto;
  align-self: flex-start;
  background-color: #9258fe;
  font-size: 1rem;
  color: #ffff;
  border-radius: 0.5rem;
  padding: 0.8rem 2rem;
  text-decoration: none;
  border: none;
  box-shadow:
    inset 0px 5px 5px rgba(203, 177, 252, 0.9),
    0px 5px 6px rgba(0, 0, 0, 0.978);
  cursor: pointer;
  margin-right: 5rem;
}

.articleSection h2 {
  font-size: 2rem;
  padding-left: 1rem;
  margin-bottom: 0;
  text-align: center;
}

.mediaMentionWrapper {
  display: flex;
  gap: 20px;
  margin: 0rem 0;
  overflow-x: auto;
  scrollbar-width: none;
  padding: 2rem 0 3rem 1rem;
  -webkit-scrollbar-width: none;
}
.mediaMentionWrapper > div {
  width: 300px;
  background-color: #f9f9f9;
  font-family: var(--font-poppins);
  cursor: pointer;
  flex-shrink: 0;
  border: 1px solid #c8c8c8;
  border-radius: 16px;
  transition:
    background-color 0.3s,
    filter 0.3s;
  position: relative;
}

.mediaMentionWrapper p {
  padding: 0 0.5rem;
  box-sizing: border-box;
}
.mediaMentionCard {
  width: 100%;
  background: url("/images/article/article1.png");
  height: 315px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.readPopover {
  display: inline;
  padding: 0.8rem;
  background-color: #f9f9f9;
  border-radius: 16px;
  position: relative;
  z-index: 2;
  top: 200px;
  transition: top 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.mediaMentionWrapper > div:hover .mediaMentionCard::before {
  background-color: rgba(0, 0, 0, 0.7);
}

.mediaMentionWrapper > div:hover .readPopover {
  top: 0;
}

.mediaMentionCard::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  transition: background-color 0.3s;
  z-index: 1;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.newsWrapper {
  padding: 0rem 1rem 1rem 1rem;
  box-sizing: border-box;
}
.articleCard {
  display: flex;
  border-bottom: 1px solid #898989;
  padding: 1rem 0;
  gap: 10px;
  font-family: var(--font-poppins);
  cursor: pointer;
  flex-direction: column;
}
.articleCard > div:first-child {
  display: flex;
  align-items: center;
}
.articleCard > div:last-child {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}
.articleImg {
  width: 100%;
  height: 100%;
}

.newsDate {
  color: #b7b7b7;
  font-size: 0.8rem;
  margin: 0 0 28px 0;
}
.articleCard h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  line-height: 2rem;
}
.newsSubheading {
  margin: 0;
  color: #747474;
  line-height: 1.2rem;
  font-size: 0.9rem;
  padding-bottom: 2.5rem;
}
.readIcon {
  color: #0169dd;
  font-size: 0.9rem;
  position: absolute;
  margin: 0;
  bottom: 0;
  right: 0;
}
.buttonContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2rem 0;
}

.loadMoreBtn {
  padding: 12px 24px 12px 24px;
  margin: 0 auto;
  border-radius: 8px;
  border: 1px solid #000000;
  font-size: 1.2rem;
  cursor: pointer;
  background-color: transparent;
}

.noLinkStyle {
  text-decoration: none;
  color: inherit;
}

@media (min-width: 768px) {
  .contactUsHeader h1 {
    font-size: 2.5rem;
  }
  .videoContentWrapper {
    flex-direction: row;
  }
  .videoContentWrapper > div {
    width: 50%;
  }
  .videoContentWrapper > div:nth-child(1) {
    align-items: end;
  }
  .videoPlyer {
    width: 457px;
    height: 446px;
  }
  .mediaMentionWrapper {
    padding: 2rem 0 3rem 3rem;
  }
  .newsWrapper {
    padding: 0rem 3rem 3rem 3rem;
  }
  .articleCard {
    flex-direction: row;
    gap: 30px;
    padding: 1.5rem 0;
  }
  .articleSection h2 {
    padding-left: 3rem;
    text-align: left;
  }
  .articleCard h3 {
    font-size: 2rem;
    line-height: 2.5rem;
  }
  .articleCard > div:first-child {
    width: 35%;
  }
  .articleCard > div:last-child {
    width: 65%;
  }
  .newsSubheading {
    line-height: 1.5rem;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .videoContentWrapper {
    flex-direction: column;
  }
  .videoContentWrapper > div {
    width: 65%;
  }
  .videoContentWrapper > div:nth-child(1) {
    align-items: center;
  }
  .videoPlyer {
    width: 557px;
    height: 446px;
  }
  .videoContentWrapper > div:nth-child(2) {
    margin: 4rem 0;
    padding: 0 8rem;
    box-sizing: border-box;
  }
  .articleCard {
    align-items: center;
  }
}
