import React, { useRef, useEffect } from "react";
import { ShareFill, Fullscreen } from "react-bootstrap-icons";

import Plyr from "plyr-react";
import "plyr-react/plyr.css";
import Hls from "hls.js";
import "../Theme.css";

export function VideoContainer({ videoTitle, videoUrl }) {
  const ref = useRef(null);
  useEffect(() => {
    const loadVideo = async () => {
      const video = document.getElementById("plyr");
      var hls = new Hls();
      hls.loadSource(videoUrl);
      hls.attachMedia(video);
      // @ts-ignore
      ref.current.plyr.media = video;

      hls.on(Hls.Events.MANIFEST_PARSED, function () {
        ref.current.plyr.play();
      });
    };
    const videoWrapper = document.getElementsByClassName(
      "plyr__video-wrapper"
    )[0];
    videoWrapper.addEventListener("click", (event) => {
      ref.current.plyr.togglePlay();
      event.stopPropagation(); // Necessary or the video will toggle twice => no playback
    });
    loadVideo();
  });
  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "volume",
    "captions",
    "settings",
    "pip",
    "airplay",
    "fullscreen",
  ];
  const goFullScreen = () => {
    ref.current.plyr.fullscreen.enter();
  };

  return (
    <div className="relative">
      <div className="game_frame_top">&nbsp;</div>
      <div className="game_frame_bottom">&nbsp;</div>
      <div className="game_box_outer">
        <span className="game_box_name">{videoTitle}</span>
        <div className="row">
          <div className="col video_box_inner">
            <Plyr
              id="plyr"
              controls
              options={{ volume: 0.1, controls }}
              source={{}}
              ref={ref}
            />
          </div>
        </div>
        <div className="row">
          <div className="col text-right">
            {/* <button className="btn-white topgap20">
              <ShareFill className="rightgap12" /> Share
            </button> */}
            <button
              className="btn-white leftgap20 topgap20"
              onClick={goFullScreen}
            >
              <Fullscreen className="rightgap12" />
              Full Screen
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default VideoContainer;
