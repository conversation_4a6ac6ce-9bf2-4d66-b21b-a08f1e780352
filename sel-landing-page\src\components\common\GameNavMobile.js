import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

export function GameNavMobile(props) {
  const [selectedSection, setSelectedSection] = useState("Free Games");
  const onValueChange = (e) => {
    setSelectedSection(e.target.value);
    props.navMobileCatHandle(e.target.value);
    setTimeout(() => {
      var objDiv = document.querySelectorAll(".scroll_tile_box");
      for (let i = 0; i < objDiv.length; i++) {
        objDiv[i].scrollTop = 0;
      }
    }, 550);
  };
  useEffect(() => {
    setSelectedSection(props.type);
  }, [props.type]);
  return (
    <div className="game_mobile_nav row">
      <div className="col-12 right_block">
        <select
          className="form-select"
          onChange={onValueChange}
          value={selectedSection}
        >
          {props.typeList.map((item, index) => (
            <option key={item + "n_" + index} value={item}>
              {item}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}

export default GameNavMobile;

GameNavMobile.propTypes = {
  type: PropTypes.string.isRequired,
  typeList: PropTypes.array.isRequired,
};
