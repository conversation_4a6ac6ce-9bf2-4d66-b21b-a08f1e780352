import React from "react";
import PropTypes from "prop-types";
// external css
import "./Auth.css";
// images
import leftTree from "icons/auth/tree_left.png";
import rightTree from "icons/auth/tree_right.png";
import purpleBg from "icons/auth/purple_bg.png";

export function AuthContainer(props) {
  const { children, isPriceBlockSection } = props;

  return (
    <div className="auth_container">
      <div
        className="auth_light_purple"
        style={{
          minHeight:
            window.innerWidth <= 600
              ? isPriceBlockSection
                ? `${window.innerWidth / 0.46 + 200}px`
                : `${window.innerWidth / 0.46 + 100}px`
              : "100vh",
        }}
      >
        <img src={leftTree} className="auth_left_tree" alt="left tree" />
        <img src={rightTree} className="auth_right_tree" alt="right tree" />
        <img src={purpleBg} className="auth_purple_bush" alt="purple bush" />
        <div className="auth_content">{children}</div>
      </div>
    </div>
  );
}

export default AuthContainer;

AuthContainer.propTypes = {
  children: PropTypes.node.isRequired,
  isPriceBlockSection: PropTypes.bool,
};
