<!doctype html>
<html>
  <head>
    <script src="assets/log.js"></script>
    <script src="assets/loadjs/loadjs.js"></script>
    <script>
      loadjs('assets/file1.js', function() {
        log('file1.js loaded');
      });      
    </script>
  </head>
  <body>
    <h1>Example: Trigger script load before window onload event fires</h1>
    <p>Note: If the network fetch returns before the DOMContentLoaded event or the window onload event fires then the browser will delay event execution until the script has finished executing.</p>
  </body>
</html>
