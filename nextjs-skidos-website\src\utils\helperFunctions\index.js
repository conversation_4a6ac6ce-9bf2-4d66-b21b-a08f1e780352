export const toSentenceCase = (str) => {
  return str
    .toLowerCase() // Convert everything to lowercase
    .replace(/(^\w{1}|\.\s*\w{1})/g, (match) => match.toUpperCase());
};

export const getLocale = (locale) => {
  switch (locale) {
    case "gb":
      return "en_gb";
    case "en":
      return "en_us";
    case "ENGB":
      return "en_gb";
    case "uk":
      return "en_gb";
    case "EN-GB":
      return "en_gb";
    case "da":
      return "da";
    case "sv":
      return "sv";
    case "nb":
      return "nb";
    case "br":
      return "pt_br";
    default:
      return "en_us";
  }
};

export const sortPlansByFrequency = (plans) => {
  const frequencyOrder = { annual: 0, quarterly: 1, monthly: 2 };

  return plans.sort((a, b) => {
    return frequencyOrder[a.PlanFreq] - frequencyOrder[b.PlanFreq];
  });
};

export const transformTextWithBreak = (text, dynamicValue, keyword) => {
  const replacedText = text.replace("(price)", dynamicValue);

  const [firstPart, secondPart] = replacedText.split(keyword);

  return <>{replacedText}</>;
};

export const extractGameData = (link) => {
  // Decode the URL
  const decodedUrl = decodeURIComponent(link);

  // Extract the base URL up to "index.html"
  const gameUrl = decodedUrl.split("index.html")[0] + "index.html";

  // Extract the query parameters after "index.html"
  const queryString = decodedUrl.split("index.html")[1];
  const params = new URLSearchParams(queryString);
  const gameOrientation = params.get("gameOrientation");

  // Return an object with the extracted data
  return {
    gameUrl,
    gameOrientation,
  };
};
