import React, { useEffect, useRef, useState } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { stripeKey, stripeBaseUrl, registerUser } from "services/Constants";
import Header from "components/Header";
import Footer from "../Footer/Footer";

/*images*/
import frame71 from "icons/subscription/frame-71.png";
import infoCircle from "icons/subscription/info-circle.png";
import logo from "icons/subscription/sel-logo.png";

// external css
import "./Email.css";
import axios from "axios";

const stripePromise = loadStripe(stripeKey);

function Email() {
  const token = window.localStorage.getItem("token");
  const [isEmail, setIsEmail] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [email, setEmail] = useState("");
  const [isDisabled, setIsDisabled] = useState(false);
  const selectedPlan = localStorage.getItem("selectedPlan");
  const validatEmail = (e) => {
    let temp = e.target.value;
    var regex = /\S+@\S+\.\S+/;
    let validate = regex.test(temp);
    if (validate) {
      setIsEmail(true);
      setEmail(temp);
    } else {
      setIsEmail(false);
    }
  };
  const handleClic = async (event) => {
    // Get Stripe.js instance
    const stripe = await stripePromise;
    const response = await fetch(
      `${stripeBaseUrl}/subscription/session?l=en_us`,
      {
        method: "POST",
        headers: {
          Authorization: window.localStorage.getItem("token"),
        },

        body: JSON.stringify({
          PlanID: window.localStorage.getItem("selectedPlan"),
          Source: "rekindle",
        }),
      }
    );
    const session = await response.json();
    const result = await stripe.redirectToCheckout({
      sessionId: session.session_id,
    });
  };
  const subscribe = () => {
    setIsDisabled(true);
    const utmSource = window.localStorage.getItem("utm_source");
    const utmMedium = window.localStorage.getItem("utm_medium");
    const utmCampaign = window.localStorage.getItem("utm_campaign");
    const installSource = window.localStorage.getItem("install_source");

    if (email && selectedPlan !== null) {
      let data = {
        email: email,
        language: "en_us",
        locale: "en_us",
        terms_confirmed: 1,
        sdk_version: "web",
        deep_link:
          utmCampaign !== "null" && utmCampaign !== null ? utmCampaign : "",
        utm_medium: utmMedium !== "null" && utmMedium !== null ? utmMedium : "",
        utm_source: utmSource !== "null" && utmSource !== null ? utmSource : "",
        game: "rekindle",
        gender: "female",
        nickname: "Emma",
        os_version: "mac",
        time_zone: "6",
        gender: "female",
        nickname: "Player",
        grade: 1,
        subject: "math",
        interests: [1, 2, 3, 4],
      };
      setIsLoading(true);
      axios
        .post(`${registerUser}&l=en_us}`, data)
        .then((response) => {
          console.log("res", response.data);
          if (response?.headers) {
            localStorage.setItem("token", response.headers.auth_token);
            localStorage.setItem(
              "refresh_token",
              response.headers.refresh_token
            );
          }
          // window.localStorage.setItem("was_onboarding", "yes");
          handleClic();
        })
        .catch((e) => {
          if (e.response?.data) {
            setIsDisabled(false);
            setIsLoading(false);
            // this.setState({ error: e.response.data.Message });
          }
        });
    }
  };
  useEffect(() => {
    if (token) {
      handleClic();
    } else {
      setIsLoading(false);
    }
  });
  return (
    <div className="email-screen">
      {isLoading && (
        <div className="loader-container">
          <span className="loader"></span>
        </div>
      )}
      <Header isSubscribed={false} userEmail={""} />
      <div className="container">
        <div className="row email-row">
          <div className="col-12 col-md-1"></div>
          <div className="col-12 col-md-4">
            <div className="hero-left">
              <div className="sel-logo">
                <img src={logo} />
              </div>
              <div className="hero-second">
                <img src={frame71} />
              </div>
              <h1>2 million+</h1>
              <h4>parents around the world trust SKIDOS</h4>
              <div className="highlight mt-4">
                <p>
                  Sign up today to help your child succeed in school and beyond
                </p>
              </div>
            </div>
          </div>
          <div className="col-12 col-md-5 mt-3">
            <div className="hero-right">
              <h2>Create Account</h2>
              <h4>
                Already have an account? <a href="#">Login</a>
              </h4>
              <input
                className={`email-input ${
                  isEmail === null || isEmail ? "" : "invalid"
                }`}
                placeholder="Enter your email address"
                onChange={(e) => validatEmail(e)}
              />
              <button
                className={`skidos-btn-primary ${
                  !isEmail || isDisabled ? "inactive" : ""
                } mt-4`}
                onClick={subscribe}
              >
                Activate
              </button>
              <h5 className="tag mt-2">
                <img src={infoCircle} /> A unique password will be sent to your
                email
              </h5>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
export default Email;
