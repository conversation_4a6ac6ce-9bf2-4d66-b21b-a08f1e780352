import React, { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import logo from "icons/questionnaire/logo.png";
import { getNonAuthData, postData } from "services/ApiHandler";
import {
  getQuestionnaire,
  sendQuestionnaireResponse,
} from "services/Constants";
import "./Questionnaire.css";

const Questionnaire = () => {
  const [questions, setQuestions] = useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [isSending, setIsSending] = useState(false);
  const [responses, setResponses] = useState([]);
  const getQuestions = async () => {
    const response = await getNonAuthData(getQuestionnaire);
    if (response?.data) setQuestions(response?.data);
  };
  const setResponse = (questionId, response) => {
    const updateResponse = [
      ...responses,
      ...[{ QuestionID: questionId, Response: response }],
    ];
    setResponses(updateResponse);
  };
  const sendUserResponse = async () => {
    setIsSending(true);
    const response = await postData(sendQuestionnaireResponse, {
      PlayerID: 22,
      Responses: responses,
    });
    console.log(response);
  };
  const optionList = questions.map((el, i) => {
    return (
      <tr key={`option_${el.QuestionID}`}>
        <td className="content">{el.Question}</td>
        <td>
          <input
            type="radio"
            name={`option_${i}`}
            value="Very Rarely"
            onChange={() => setResponse(el.QuestionID, 0)}
          />
        </td>
        <td>
          <input
            type="radio"
            name={`option_${i}`}
            value="Rarely"
            onChange={() => setResponse(el.QuestionID, 1)}
          />
        </td>
        <td>
          <input
            type="radio"
            name={`option_${i}`}
            value="Frequently"
            onChange={() => setResponse(el.QuestionID, 2)}
          />
        </td>
        <td>
          <input
            type="radio"
            name={`option_${i}`}
            value="Very Frequently"
            onChange={() => setResponse(el.QuestionID, 3)}
          />
        </td>
        <td>
          <input
            type="radio"
            name={`option_${i}`}
            value="Sometimes"
            onChange={() => setResponse(el.QuestionID, 4)}
          />
        </td>
      </tr>
    );
  });
  useEffect(() => {
    getQuestions();
  }, []);
  return (
    <div className="questionnaire">
      <div className="container">
        <div className="row header">
          <div className="col-12 col-md-6">
            <img src={logo} alt="Questionnaire" />
          </div>
          <div className="col-12 col-md-6">
            <h3>Prescriptive Report: Parents</h3>
          </div>
        </div>
        <div className="row highlight">
          <div className="col-12 col-md-6 box">
            <p>
              How: We know that you understand your child the best. Tell us more
              about their day-to-day behaviour over the past two weeks here.
            </p>
          </div>
          <div className="col-12 col-md-6 box">
            <p>
              Why: This helps us give you a comprehensive report about how they
              are doing and suggest top 3 milestone for strength based
              development.
            </p>
          </div>
        </div>
        <div className="row divider mt-3"></div>
        <div className="row">
          <div className="col-12 col-md-12">
            <div className="table-conatiner">
              <table className="table table-borderless">
                <thead>
                  <tr>
                    <th scope="col"></th>
                    <th scope="col">Very Rarely</th>
                    <th scope="col">Rarely</th>
                    <th scope="col">Frequently</th>
                    <th scope="col">Very Frequently</th>
                    <th scope="col">Sometimes</th>
                  </tr>
                </thead>
                <tbody>{optionList}</tbody>
              </table>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-12 col-md-12">
            <button
              className="btn mb-5"
              onClick={sendUserResponse}
              disabled={
                searchParams.get("pid") && responses.length > 0
                  ? ""
                  : "disabled"
              }
            >
              {isSending ? (
                <>
                  <span
                    className="spinner-border spinner-border-sm"
                    role="status"
                    aria-hidden="true"
                  ></span>
                  Saving...
                </>
              ) : (
                "Done"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Questionnaire;
