"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/rekindle/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js ***!
  \******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuM19yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjNfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcz83YTEzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuLi9zaGFyZWQvbGliL2FwcC1keW5hbWljXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLWR5bmFtaWMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \*************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuM19yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hcHAtZHluYW1pYy5qcyIsIm1hcHBpbmdzIjoiOzs7OzJDQWlDQTs7O2VBQXdCQTs7Ozs7NEVBakNOOytFQUNHO0FBZ0NOLFNBQVNBLFFBQ3RCQyxjQUE2QyxFQUM3Q0MsT0FBMkI7UUFtQ2hCQztJQWpDWCxJQUFJQyxrQkFBc0M7UUFDeEMsd0RBQXdEO1FBQ3hEQyxTQUFTLENBQUFDO2dCQUFDLEVBQUVDLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUUsR0FBQUg7WUFDdkMsSUFBSSxDQUFDRyxXQUFXLE9BQU87WUFDdkIsSUFBSUMsSUFBeUIsRUFBYztnQkFDekMsSUFBSUYsV0FBVztvQkFDYixPQUFPO2dCQUNUO2dCQUNBLElBQUlELE9BQU87b0JBQ1QsT0FDRSxXQURGLEdBQ0UsSUFBQUksWUFBQUMsSUFBQSxFQUFDQyxLQUFBQTs7NEJBQ0VOLE1BQU1PLE9BQU87MENBQ2QsSUFBQUgsWUFBQUksR0FBQSxFQUFDQyxNQUFBQSxDQUFBQTs0QkFDQVQsTUFBTVUsS0FBSzs7O2dCQUdsQjtZQUNGO1lBQ0EsT0FBTztRQUNUO0lBQ0Y7SUFFQSxJQUFJLE9BQU9oQixtQkFBbUIsWUFBWTtRQUN4Q0csZ0JBQWdCYyxNQUFNLEdBQUdqQjtJQUMzQjtJQUVBLE1BQU1FLGdCQUFnQjtRQUNwQixHQUFHQyxlQUFlO1FBQ2xCLEdBQUdGLE9BQU87SUFDWjtJQUVBLE9BQU9pQixDQUFBQSxHQUFBQSxVQUFBQSxPQUFRLEVBQUM7UUFDZCxHQUFHaEIsYUFBYTtRQUNoQmlCLFNBQU8sQ0FBRWpCLG1DQUFBQSxjQUFja0IsaUJBQWlCLHFCQUEvQmxCLGlDQUFpQ2lCLE9BQU87SUFDbkQ7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvYXBwLWR5bmFtaWMudHN4P2M1NjUiXSwibmFtZXMiOlsiZHluYW1pYyIsImR5bmFtaWNPcHRpb25zIiwib3B0aW9ucyIsIm1lcmdlZE9wdGlvbnMiLCJsb2FkYWJsZU9wdGlvbnMiLCJsb2FkaW5nIiwicGFyYW0iLCJlcnJvciIsImlzTG9hZGluZyIsInBhc3REZWxheSIsInByb2Nlc3MiLCJfanN4cnVudGltZSIsImpzeHMiLCJwIiwibWVzc2FnZSIsImpzeCIsImJyIiwic3RhY2siLCJsb2FkZXIiLCJMb2FkYWJsZSIsIm1vZHVsZXMiLCJsb2FkYWJsZUdlbmVyYXRlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \*************************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuM19yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBY08sTUFBQUEsZ0JBQXNCQyxtQkFBQUEsQ0FBdUM7U0FBdkNDLGFBQVVDLEtBQVE7SUFDN0MsSUFBSSxFQUFBQyxNQUFPQyxFQUFBQSxRQUFXLEtBQUFDO1FBQ3BCLE9BQU1ELFdBQUlFLGFBQUFBO1FBQ1osVUFBQVAsY0FBQU8saUJBQUEsQ0FBQUg7SUFFQTtJQUNGLE9BQUFEOztLQU42QkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9zaGFyZWQvbGliL2xhenktZHluYW1pYy9keW5hbWljLWJhaWxvdXQtdG8tY3NyLnRzeD9lMzQwIl0sIm5hbWVzIjpbIl9iYWlsb3V0dG9jc3IiLCJyZXF1aXJlIiwiQmFpbG91dFRvQ1NSIiwiY2hpbGRyZW4iLCJyZWFzb24iLCJ3aW5kb3ciLCJwYXJhbSIsIkJhaWxvdXRUb0NTUkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \***********************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadcss = __webpack_require__(/*! ./preload-css */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { $$typeof, default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && \"default\" in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                typeof window === \"undefined\" ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadcss.PreloadCss, {\n                    moduleIds: opts.modules\n                }) : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js ***!
  \**************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadCss\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadCss;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _requestasyncstorageexternal = __webpack_require__(/*! ../../../client/components/request-async-storage.external */ \"(shared)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/request-async-storage.external.js\");\nfunction PreloadCss(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (typeof window !== \"undefined\") {\n        return null;\n    }\n    const requestStore = (0, _requestasyncstorageexternal.getExpectedRequestStore)(\"next/dynamic css\");\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (requestStore.reactLoadableManifest && moduleIds) {\n        const manifest = requestStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const cssFiles = manifest[key].files.filter((file)=>file.endsWith(\".css\"));\n            allFiles.push(...cssFiles);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((file)=>{\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                // @ts-ignore\n                precedence: \"dynamic\",\n                rel: \"stylesheet\",\n                href: requestStore.assetPrefix + \"/_next/\" + encodeURI(file),\n                as: \"style\"\n            }, file);\n        })\n    });\n} //# sourceMappingURL=preload-css.js.map\n_c = PreloadCss;\nvar _c;\n$RefreshReg$(_c, \"PreloadCss\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE0LjIuM19yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvcHJlbG9hZC1jc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTyxNQUFBQSwrQkFBc0VDLG1CQUFBQSxDQUFBO1NBQWxEQyxXQUFXQyxLQUFYO0lBQ3pCLE1BQUFDLFNBQUEsS0FBQUQ7SUFDQSwrRUFBbUM7UUFDakMsT0FBT0UsV0FBQTtRQUNUO0lBRUE7SUFDQSxNQUFNQyxlQUFhLElBQUFOLDZCQUFBTyx1QkFBQTtJQUVuQixNQUFBRCxXQUFBO0lBQ0EsNEVBQWtEO0lBQ2xELGtEQUEwQ0Y7UUFDeENJLGFBQU1DLHFCQUF3QkMsSUFBQUEsV0FBQUE7UUFDOUIsTUFBS0QsV0FBTUUsYUFBa0JELHFCQUFBO2FBQzNCLE1BQUtELE9BQVNFLFVBQU07WUFDcEIsS0FBQUYsUUFBTUcsQ0FBQUEsSUFBV0gsRUFBQUE7WUFHakJILE1BQUFBLFdBQWlCTSxRQUFBQSxDQUFBQSxJQUFBQSxDQUFBQSxLQUFBQSxDQUFBQSxNQUFBQSxDQUFBQSxDQUFBQSxPQUFBQSxLQUFBQSxRQUFBQSxDQUFBQTtZQUNuQk4sU0FBQU8sSUFBQSxJQUFBRDtRQUNGO0lBRUE7UUFDRU4sU0FBT1EsTUFBQTtRQUNUO0lBRUE7V0FFS1IsV0FBQUEsR0FBQUEsQ0FBQUEsR0FBU1MsWUFBS0MsR0FBQUEsRUFBQUEsWUFBQUEsUUFBQUEsRUFBQUE7a0JBQ2JWLFNBQUFTLEdBQUEsRUFBQUM7bUJBR2lCLGtCQUFBQyxZQUFBQyxHQUFBO2dCQUNiQyxhQUFZO2dCQUNaQyxZQUFJO2dCQUNKQyxLQUFBQTtnQkFDQUMsTUFBR2QsYUFBQWUsV0FBQSxlQUFBQyxVQUFBUjtnQkFMRUEsSUFBQUE7WUFRWCxHQUFBQTs7SUFHTjs7S0ExQzJCZCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvbGF6eS1keW5hbWljL3ByZWxvYWQtY3NzLnRzeD9iYWQ1Il0sIm5hbWVzIjpbIl9yZXF1ZXN0YXN5bmNzdG9yYWdlZXh0ZXJuYWwiLCJyZXF1aXJlIiwiUHJlbG9hZENzcyIsInBhcmFtIiwibW9kdWxlSWRzIiwid2luZG93IiwiYWxsRmlsZXMiLCJnZXRFeHBlY3RlZFJlcXVlc3RTdG9yZSIsInJlcXVlc3RTdG9yZSIsIm1hbmlmZXN0IiwicmVhY3RMb2FkYWJsZU1hbmlmZXN0Iiwia2V5IiwiY3NzRmlsZXMiLCJwdXNoIiwibGVuZ3RoIiwibWFwIiwiZmlsZSIsIl9qc3hydW50aW1lIiwianN4IiwicHJlY2VkZW5jZSIsInJlbCIsImhyZWYiLCJhcyIsImFzc2V0UHJlZml4IiwiZW5jb2RlVVJJIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/rekindle/page.js":
/*!**********************************!*\
  !*** ./src/app/rekindle/page.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_responsive_carousel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-responsive-carousel */ \"(app-pages-browser)/./node_modules/.pnpm/react-responsive-carousel@3.2.23/node_modules/react-responsive-carousel/lib/js/index.js\");\n/* harmony import */ var react_responsive_carousel_lib_styles_carousel_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-responsive-carousel/lib/styles/carousel.min.css */ \"(app-pages-browser)/./node_modules/.pnpm/react-responsive-carousel@3.2.23/node_modules/react-responsive-carousel/lib/styles/carousel.min.css\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _styles_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./styles.module.css */ \"(app-pages-browser)/./src/app/rekindle/styles.module.css\");\n/* harmony import */ var _styles_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var plyr_react_plyr_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! plyr-react/plyr.css */ \"(app-pages-browser)/./node_modules/.pnpm/plyr-react@5.3.0_plyr@3.7.8_react@18.3.1/node_modules/plyr-react/plyr.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Dynamically import Plyr to avoid SSR issues\nconst Plyr = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_pnpm_plyr-react_5_3_0_plyr_3_7_8_react_18_3_1_node_modules_pl-e47da1\").then(__webpack_require__.bind(__webpack_require__, /*! plyr-react */ \"(app-pages-browser)/./node_modules/.pnpm/plyr-react@5.3.0_plyr@3.7.8_react@18.3.1/node_modules/plyr-react/esm/index.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\rekindle\\\\page.js -> \" + \"plyr-react\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading video player...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n            lineNumber: 12,\n            columnNumber: 18\n        }, undefined)\n});\n_c = Plyr;\n// Import Plyr CSS\n\nconst RekindlePage = ()=>{\n    const controls = [\n        \"play-large\",\n        \"play\",\n        \"progress\",\n        \"current-time\",\n        \"mute\",\n        \"captions\",\n        \"pip\",\n        \"airplay\",\n        \"fullscreen\"\n    ];\n    const eventTrackerGa4 = (category, action, label, nonInteraction)=>{\n        // GA4 tracking can be implemented here if needed\n        console.log(\"GA4 Event:\", {\n            category,\n            action,\n            label,\n            nonInteraction\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().rekindleMinds),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().hero),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                        src: \"https://images.skidos.com/rekindle/landing-page.html\",\n                        width: \"100%\",\n                        height: \"680px\",\n                        frameBorder: \"0\",\n                        title: \"Rekindle Minds\",\n                        children: \"Browser not compatible.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row d-flex justify-content-center header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-12 col-md-7 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().heroTitle),\n                                    children: \"A mindful game designed by teachers to boost your child's emotional growth and learning\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                id: \"player-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row d-flex justify-content-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-12 col-md-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().playerContainer),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Plyr, {\n                                    id: \"plyr\",\n                                    controls: true,\n                                    options: {\n                                        volume: 0.1,\n                                        controls\n                                    },\n                                    source: {\n                                        type: \"video\",\n                                        sources: [\n                                            {\n                                                src: \"https://www.youtube.com/watch?v=yrgI2U9hAc8\",\n                                                provider: \"youtube\"\n                                            }\n                                        ]\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().containerBlock), \" \").concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().mt7)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-12 col-md-6 block-img d-flex justify-content-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: \"/images/rekindle-minds/why-play.png\",\n                                    alt: \"Why Play Rekindle Minds\",\n                                    width: 400,\n                                    height: 300\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-12 col-md-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Why play Rekindle Minds?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Rekindle Minds takes children on a playful deep-dive into their emotions. This delightful interactive game helps them build strong relationships, manage stress and develop a positive, confident personality.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().containerBlock), \" \").concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().mt7)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-12 col-md-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"How does it work?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Explore, learn and grow with 150+ immersive stories, quizzes, puzzles, and games - designed by trusted educators to develop your child's EQ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-12 col-md-6 block-img d-flex justify-content-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: \"/images/rekindle-minds/how-it-works.png\",\n                                    alt: \"How it Works\",\n                                    width: 400,\n                                    height: 300\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().ribbonBlock),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().ribbon)\n                }, void 0, false, {\n                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().rekindleMinds), \" \").concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().mt7)),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().containerBlock)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-12 col-md-6 block-img d-flex justify-content-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: \"/images/rekindle-minds/progress-report.png\",\n                                    alt: \"Progress Report\",\n                                    width: 400,\n                                    height: 300\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-12 col-md-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: \"Expert-approved guide to SEL\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Lay a strong foundation for social-emotional learning with 5 playful islands designed as per the CASEL framework\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row mt-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().rekindleCarousel), \" \").concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().mt7)),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_responsive_carousel__WEBPACK_IMPORTED_MODULE_1__.Carousel, {\n                                    infiniteLoop: true,\n                                    autoPlay: true,\n                                    showStatus: false,\n                                    showIndicators: false,\n                                    swipeable: false,\n                                    renderArrowPrev: (clickHandler, hasPrev)=>{\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().arrow), \" \").concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().leftArrow)),\n                                            onClick: clickHandler,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn btn-primary \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().btnPrimary)),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    src: \"/images/rekindle-minds/left-arrow.png\",\n                                                    alt: \"Left\",\n                                                    width: 20,\n                                                    height: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 21\n                                        }, void 0);\n                                    },\n                                    renderArrowNext: (clickHandler, hasNext)=>{\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().arrow), \" \").concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().rightArrow)),\n                                            onClick: clickHandler,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn btn-primary \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().btnPrimary)),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    src: \"/images/rekindle-minds/right-arrow.png\",\n                                                    alt: \"Right\",\n                                                    width: 20,\n                                                    height: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 158,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                            lineNumber: 157,\n                                            columnNumber: 21\n                                        }, void 0);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"row carousel\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-12 col-md-5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().reviewImage),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-two.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().left),\n                                                                alt: \"Left\",\n                                                                width: 230,\n                                                                height: 200\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-one.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().center),\n                                                                alt: \"Center\",\n                                                                width: 280,\n                                                                height: 250\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-three.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().right),\n                                                                alt: \"Right\",\n                                                                width: 230,\n                                                                height: 200\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().dots),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().active)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 174,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-12 col-md-7 \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().rightBlock)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            children: \"Hear from Educators and Parents\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                '\"Love how this app helps the young ones understand their feelings! My daughter Zoey is very sensitive so she likes this game a lot! She loves playing with the character Moyo\"',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 30\n                                                                }, undefined),\n                                                                \"- Sarah Honart, Mom to 7 y/o\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"row carousel\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-12 col-md-5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().reviewImage),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-one.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().left),\n                                                                alt: \"Left\",\n                                                                width: 230,\n                                                                height: 200\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-two.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().center),\n                                                                alt: \"Center\",\n                                                                width: 280,\n                                                                height: 250\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-three.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().right),\n                                                                alt: \"Right\",\n                                                                width: 230,\n                                                                height: 200\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().dots),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().active)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 200,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-12 col-md-7 \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().rightBlock)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            children: \"Hear from Educators and Parents\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                '\"Game-changer! This app teaches children to understand and express their emotions in a healthy way.\"',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 30\n                                                                }, undefined),\n                                                                \"- Maria Adamms, School Counselor\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"row carousel\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-12 col-md-5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().reviewImage),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-two.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().left),\n                                                                alt: \"Left\",\n                                                                width: 230,\n                                                                height: 200\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-three.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().center),\n                                                                alt: \"Center\",\n                                                                width: 280,\n                                                                height: 250\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                src: \"/images/rekindle-minds/review-two.png\",\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().right),\n                                                                alt: \"Right\",\n                                                                width: 230,\n                                                                height: 200\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().dots),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().active)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-12 col-md-7 \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().rightBlock)),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            children: \"Hear from Educators and Parents\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                '\"The interactive videos are a wonderful way to help guide kids on what is right or wrong.\"',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 30\n                                                                }, undefined),\n                                                                \"- Dr. Thor Mitch, Child Psychologist\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row d-flex justify-content-center \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().mt7)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().gamesHeading),\n                                children: \"Explore Now In Top SKIDOS Games\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().gamesBlock),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().games),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                width: 200,\n                                                height: 160,\n                                                src: \"/images/rekindle-minds/bike-racing.png\",\n                                                alt: \"Skidos Games\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Bike Racing\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://apps.apple.com/us/app/cool-math-games-kids-racing/id1319262120\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>eventTrackerGa4(\"button\", \"rekindle_bike_racing_download_clicked\", \"Bike Racing Download Button\", false),\n                                                    children: \"Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().games),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                width: 200,\n                                                height: 160,\n                                                src: \"/images/rekindle-minds/Doctor.png\",\n                                                alt: \"Skidos Games\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Doctor\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 272,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://apps.apple.com/us/app/doctor-games-for-kids/id1506886061\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>eventTrackerGa4(\"button\", \"rekindle_doctor_download_clicked\", \"Doctor Download Button\", false),\n                                                    children: \"Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().games),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                width: 200,\n                                                height: 160,\n                                                src: \"/images/rekindle-minds/Bath.png\",\n                                                alt: \"Skidos Games\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Bath\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://apps.apple.com/us/app/learning-games-for-kids/id1483744837\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>eventTrackerGa4(\"button\", \"rekindle_bath_download_clicked\", \"Bath Download Button\", false),\n                                                    children: \"Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().games),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                width: 200,\n                                                height: 160,\n                                                src: \"/images/rekindle-minds/Supertstore.png\",\n                                                alt: \"Skidos Games\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Superstore\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://apps.apple.com/us/app/fun-games-kids-preschool-math/id1497549298\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>eventTrackerGa4(\"button\", \"rekindle_superstore_download_clicked\", \"Superstore Download Button\", false),\n                                                    children: \"Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row \".concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().containerBlock), \" d-flex justify-content-center \").concat((_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().mt7)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().gamesHeading),\n                                children: \"Safe Play- Learning For Your Kid\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().certificationBlock),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().certification),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                src: \"/images/rekindle-minds/certification-one.png\",\n                                                alt: \"Skidos Certification\",\n                                                width: 150,\n                                                height: 150\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2\",\n                                                children: \"Strict privacy compliance\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().certification),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                src: \"/images/rekindle-minds/certification-two.png\",\n                                                alt: \"Skidos Certification\",\n                                                width: 150,\n                                                height: 150\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2\",\n                                                children: \"Designed by educators & experts\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().certification),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                src: \"/images/rekindle-minds/certification-three.png\",\n                                                alt: \"Skidos Certification\",\n                                                width: 150,\n                                                height: 150\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2\",\n                                                children: \"Guided by CASEL framework\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_module_css__WEBPACK_IMPORTED_MODULE_5___default().certification),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                src: \"/images/rekindle-minds/certification-four.png\",\n                                                alt: \"Skidos Certification\",\n                                                width: 150,\n                                                height: 150\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-2\",\n                                                children: \"Secure learning environment\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\SKIDOS\\\\Front-end-proj\\\\nextjs-skidos-website\\\\src\\\\app\\\\rekindle\\\\page.js\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = RekindlePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RekindlePage);\nvar _c, _c1;\n$RefreshReg$(_c, \"Plyr\");\n$RefreshReg$(_c1, \"RekindlePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/rekindle/page.js\n"));

/***/ })

});