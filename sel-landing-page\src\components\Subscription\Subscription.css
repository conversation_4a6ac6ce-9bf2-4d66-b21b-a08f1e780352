.subscription {
  font-weight: 600;
}
.subscription h1,
.subscription h2,
.subscription h3 {
  font-weight: 700;
}
.subscription h1 {
  font-size: 3.5rem;
  margin-top: 100px;
  margin-bottom: 25px;
}
.subscription h2 {
  font-size: 2.5rem;
}
.subscription .price-block {
  margin-top: 20px;
}
.subscription .pricing {
  display: flex;
  border: 1px solid #000;
  border-radius: 15px;
  background-color: #f8f8f8;
  position: relative;
  cursor: pointer;
}
.subscription .pricing .left,
.subscription .pricing .right {
  flex-grow: 1;
  flex-basis: 0;
  padding: 20px 25px 05px 25px;
}
.subscription .pricing .right {
  text-align: right;
}
.subscription .pricing h3 {
  font-size: 1.25rem;
  font-weight: 700;
}
.subscription .pricing p {
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 15px;
}
.subscription .pricing .base-price {
  text-decoration: line-through;
  font-size: 1rem;
  font-weight: 600;
  margin-right: 10px;
  display: inline-block;
  margin-top: 05px;
}
.subscription .pricing .offer-badge {
  background: #3ca247;
  font-size: 0.8rem;
  font-weight: 700;
  padding: 5px 10px;
  color: #fff;
  border-radius: 5px;
}
.subscription .price-block.selected .pricing,
.subscription .price-block:hover .pricing {
  border: 2px solid;
  background-color: #eeedff;
  transition: all 0.2s ease-in-out;
}
.subscription .best-value .pricing {
  padding-bottom: 08px;
}
.subscription .best-value .pricing h3 {
  font-size: 1.75rem;
  margin-top: 40px;
}
.subscription .best-value .pricing .best-value-badge {
  background-color: #8d83ff;
  color: #fff;
  font-weight: 700;
  position: absolute;
  left: 0;
  top: 0;
  padding: 2px 15px 5px 15px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 12px;
}
.subscription .best-value .pricing .best-value-badge img {
  max-width: 22px;
}
.subscription .best-value .pricing .base-price {
  font-size: 1.1rem;
}
.subscription .best-value .pricing .offer-badge {
  font-size: 0.9rem;
}
.subscription .skidos-btn-primary {
  border-bottom: 5px solid #b54608;
}
.subscription .skidos-btn-primary.arrow {
  position: relative;
}
.subscription .skidos-btn-primary.arrow::before {
  content: "";
  background: url(../../icons/subscription/arrow-2.png);
  background-size: cover;
  background-repeat: no-repeat;
  display: inline-block;
  width: 110px;
  height: 75px;
  position: absolute;
  left: -125px;
  top: -25px;
}
.subscription .hero-second {
  text-align: center;
}
.subscription .hero-second img {
  max-width: 375px;
}
.subscription .feature-box {
  background-color: #61a2ff;
  padding: 15px 20px 15px;
  border-radius: 5px;
  text-align: center;
  flex-grow: 1;
  flex-basis: 0;
  margin: 10px;
  margin-top: 100px;
}
.subscription .feature-box h3 {
  color: #fff;
  font-size: 1.35rem;
  font-weight: 700;
}
.subscription .feature-box p {
  font-size: 0.9rem;
}
.subscription .feature-box img {
  max-width: 90px;
  margin-top: -75px;
}
.subscription .bg-blue {
  background-color: #a3e2f7;
  padding: 25px 50px;
  border-radius: 5px;
  display: flex;
  align-items: center;
}
.subscription .bg-blue h3 {
  font-size: 1.5rem;
}
.subscription .bg-blue img {
  max-width: 100%;
}
.subscription .award img {
  max-width: 100%;
}
.subscription .reviews .review {
  padding: 25px;
}
.subscription .reviews .review .qt {
  width: 25px;
}
.subscription .reviews .review h4 {
  color: #696969;
  font-size: 1.1rem;
  font-weight: 700;
  margin-top: 25px;
}
.subscription .reviews .review h5 {
  color: #696969;
  font-size: 0.9rem;
  font-weight: 600;
}
.subscription .reviews .review .rating {
  max-width: 115px;
}
.subscription .review-navigator,
.subscription .view-more-faq {
  justify-content: center;
}
.subscription .review-navigator img {
  width: 75px;
  cursor: pointer;
}
.subscription .view-more-faq img {
  width: 125px;
  cursor: pointer;
}
.subscription .faqs {
  border-top: 1px solid;
}
.subscription .faqs .faq {
  border-bottom: 1px solid #333;
  padding: 25px;
  position: relative;
}
.subscription .faqs .faq .content {
  opacity: 0;
  margin-left: 50px;
  margin-top: 15px;
  height: 0;
  transition: all 0.3s ease-in-out;
}
.subscription .faqs .faq .content.active {
  opacity: 1;
  height: auto;
  transition: all 0.3s ease-in-out;
}
.subscription .faqs .faq h3 {
  color: #989898;
  font-size: 1.5rem;
  font-weight: 600;
  margin-left: 50px;
  cursor: pointer;
}
.subscription .faqs .faq h3::before {
  content: "";
  background: url(../../icons/subscription/arrow-down.png);
  width: 25px;
  height: 25px;
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  position: absolute;
  left: 0;
  top: 30px;
  transition: all 0.3s ease-in-out;
}
.subscription .faqs .faq.active h3::before {
  transform: rotate(-180deg);
  transition: all 0.3s ease-in-out;
}
