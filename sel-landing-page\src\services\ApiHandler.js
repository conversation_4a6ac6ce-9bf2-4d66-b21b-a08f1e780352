import axios from "axios";
import { refreshTokenUrl } from "./Constants";
export const getData = async (baseURL, auth_token) => {
  if (auth_token) {
    let result = await axios
      .get(baseURL, {
        headers: {
          Authorization: auth_token,
        },
      })
      .catch(async (error) => {
        if (error.response) {
          if (error.response.status === 401) {
            let refreshToken = localStorage.getItem("refresh_token");
            if (refreshToken) {
              await axios
                .get(refreshTokenUrl, {
                  headers: {
                    refresh_token: refreshToken,
                  },
                })
                .then((response) => {
                  let authToken = response?.headers?.auth_token;
                  if (authToken) {
                    localStorage.setItem("token", authToken);
                    window.location.reload();
                  }
                })
                .catch((error) => {
                  //console.log(error);
                });
            }
          }
        }
      })
      .then((response) => {
        return response?.data;
      });
    return result;
  } else {
    let result = await axios.get(baseURL).then((response) => {
      return response?.data;
    });
    return result;
  }
};
export const getNonAuthData = async (baseURL, queryParams) => {
  const result = await axios
    .get(`${baseURL}${queryParams ? queryParams : ""}`)
    .then((response) => {
      return response;
    });
  return result;
};
export const postData = async (baseURL, payload) => {
  const result = axios.post(baseURL, payload).then((response) => {
    return response;
  });
  return result;
};
export const updateData = async (baseURL, payload) => {
  axios.put(baseURL, payload).then((response) => {
    console.log(response.data);
  });
};
export const deleteData = async (baseURL) => {
  axios.delete(baseURL).then(() => {
    console.log("Deleted!");
  });
};
