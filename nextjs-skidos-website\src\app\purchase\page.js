"use client";
import FormCtaButton from "@/components/FormCtaButton";
import useStripeCheckout from "@/hooks/useStripeCheckout";
import apiClient from "@/utils/axiosUtil";
import { getLocale, sortPlansByFrequency, transformTextWithBreak } from "@/utils/helperFunctions";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useState } from "react";
import styles from "./page.module.css";

export default function Component() {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const { handleCheckout } = useStripeCheckout();
  const locale = useLocale();
  const lang = getLocale(locale);
  const t = useTranslations("Purchase");

  useEffect(() => {
    trackWebEngageEvent("WebSubscriptionScrReached");
  }, []);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        const url = `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/plan?version=5.0&l=${lang}&planType=acquisition`;
        const response = await apiClient.get(url);
        const data = response.data;

        // Automatically select the top card by default (highest ID)
        const sortedPlans = sortPlansByFrequency([...data]);
        const updatedPlans = sortedPlans.map((plan, index) => ({
          ...plan,
          isSelected: index === 0, // Select the first plan in the sorted array
        }));

        setPlans(updatedPlans);
        setLoading(false);
      } catch (error) {
        console.error("Failed to fetch plans:", error);
        setLoading(false);
      }
    };
    fetchPlans();
  }, [locale]);

  const handleChangePlan = (plan) => {
    setPlans((prevPlans) =>
      prevPlans.map((p) => ({
        ...p,
        isSelected: p.ID === plan.ID,
      }))
    );
  };

  const handleActivatePlan = () => {
    const selectedPlan = plans.find((plan) => plan.isSelected);
    const planID = selectedPlan.PlanID;
    const authToken = localStorage.getItem("auth_token");
    trackWebEngageEvent("WebSubscriptionNextBtnClk", {
      plan_name: selectedPlan?.PlanName,
      product_id: selectedPlan?.PlanID,
      session_id: authToken,
    });
    handleCheckout({ lang, planID, authToken });
  };

  return (
    <div className={styles.container}>
      <div className={styles.banner}>
        <img className={styles.bannerImage} src="/images/saas/banner.svg" alt="Banner" />
        <div className={styles.bannerContent}>
          <h1 className={styles.title}>{t("Heading")}</h1>
          <div className={styles.features} style={{ placeItems: "flex-start" }}>
            <div className={styles.feature}>
              <Image src="/images/saas/crown-icon.svg" alt="Crown" width={24} height={24} />
              <span>{t("SubHeading1")}</span>
            </div>
            <div className={styles.feature}>
              <Image src="/images/saas/crown-icon.svg" alt="Crown" width={24} height={24} />
              <span>{t("SubHeading2")}</span>
            </div>
          </div>
        </div>
      </div>

      <main className={styles.main}>
        <div className={styles.pricingSection}>
          {loading ? (
            <p>Loading plans...</p>
          ) : (
            plans.map((plan) => (
              <div
                onClick={() => handleChangePlan(plan)}
                key={plan.ID}
                className={`${
                  plan.BestValue
                    ? `${styles.annualPlan} ${styles.bestValuePlan}`
                    : styles.quarterlyPlan
                } ${plan.isSelected ? styles.selectedPlan : ""}`}
              >
                {plan.BestValue !== 0 && (
                  <div className={styles.bestValueBadge}>
                    <Image
                      src="/images/saas/best-value.svg"
                      alt="Best Value"
                      width={119}
                      height={38}
                    />
                    <span className={styles.badgeText}>Best Value</span>
                  </div>
                )}
                <div className={styles.planHeader}>
                  <span>{plan.PlanTextTransformed.plan}</span>
                  {!plan.BestValue && plan?.PlanTextTransformed?.cpmText && (
                    <span className={styles.discount}>{plan.PlanTextTransformed.cpmText}</span>
                  )}
                </div>
                <div className={styles.pricing}>
                  {plan.PlanTextTransformed.nonDiscountedPrice && (
                    <span
                      className={styles.originalPrice}
                      style={{ textDecoration: "line-through" }}
                    >
                      {plan.PlanTextTransformed.nonDiscountedPrice}
                    </span>
                  )}
                  <span className={styles.price}>{plan.PlanTextTransformed.price}</span>
                  {plan.Price && plan?.Price?.trim()?.length > 0 && (
                    <div className={styles.monthly}>{plan.Price}</div>
                  )}
                </div>
                <p
                  className={styles.billingInfo}
                  style={{
                    color: plan.BestValue ? "#000" : "",
                  }}
                >
                  {/* {plan.PlanTextTransformed.annualText} {plan.PlanTextTransformed.price} */}
                  {transformTextWithBreak(
                    plan.PlanTextTransformed.annualText,
                    plan.PlanTextTransformed.price,
                    "free"
                  )}
                </p>
              </div>
            ))
          )}

          <FormCtaButton
            onClick={handleActivatePlan}
            text={t("CtaBtn")}
            customStyles={styles.ctaButton}
          />

          {/* <div className={styles.footer}>
                        <Link href="/terms">Terms</Link>
                        <Link href="/privacy-policy">Privacy Policy</Link>
                    </div> */}
        </div>
      </main>
    </div>
  );
}
