"use client";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import styles from "./styles.module.css";

const CarouselEnterEmail = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [touchStartY, setTouchStartY] = useState(0);
  const [touchEndY, setTouchEndY] = useState(0);
  const t = useTranslations("EnterEmailSection");

  const carouselData = [
    {
      bannerImage: "/images/webGl/emailBanner/2.webp",
      bannerHeading: t("Heading"),
      bannerSubheading: t("SubHeading2"),
    },
    {
      bannerImage: "/images/webGl/emailBanner/1.webp",
      bannerHeading: t("Heading"),
      bannerSubheading: t("SubHeading1"),
    },
    {
      bannerImage: "/images/webGl/emailBanner/3.webp",
      bannerHeading: t("Heading"),
      bannerSubheading: t("SubHeading3"),
    },
  ];

  const handleTouchStart = (e) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    setTouchEndX(e.touches[0].clientX);
    setTouchEndY(e.touches[0].clientY);
  };

  const handleTouchEnd = () => {
    const horizontalSwipeDistance = touchStartX - touchEndX;
    const verticalSwipeDistance = touchStartY - touchEndY;

    if (Math.abs(verticalSwipeDistance) < 50) {
      if (horizontalSwipeDistance > 75) {
        setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
      } else if (horizontalSwipeDistance < -75) {
        setCurrentSlide((prev) => (prev === 0 ? carouselData.length - 1 : prev - 1));
      }
    }

    setTouchStartX(0);
    setTouchEndX(0);
    setTouchStartY(0);
    setTouchEndY(0);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === carouselData.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className={styles.carouselWrapper}>
      <div className={styles.backgroundContainer}>
        {carouselData.map((item, index) => (
          <div
            key={index}
            className={`${styles.backgroundSlide} ${currentSlide === index ? styles.activeBackground : ""}`}
            // style={{
            //   backgroundImage: `url(${item.bannerImage})`,
            // }}
          />
        ))}
      </div>
      <div
        className={styles.carousel}
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {carouselData.map((item, index) => (
          <div
            key={index}
            className={`${styles.slide} ${currentSlide === index ? styles.active : ""}`}
          >
            <div className={styles.imageContainer}>
              <img src={item.bannerImage} alt={item.bannerHeading} />
            </div>
          </div>
        ))}
      </div>
      <div className={styles.carouselContentWrapper}>
        <h1>{carouselData[currentSlide].bannerHeading}</h1>
        <p>{carouselData[currentSlide].bannerSubheading}</p>
      </div>
      <div className={styles.indicators}>
        {carouselData.map((_, index) => (
          <span
            key={index}
            className={`${styles.indicator} ${currentSlide === index ? styles.active : ""}`}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default CarouselEnterEmail;
