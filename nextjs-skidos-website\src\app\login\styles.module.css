.loginContainer {
  background: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0 2vh 0;
}

.container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 0 2vw 6vh 2vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: var(--font-nevermind-medium);
  background: white;
  box-shadow: 0 0.5vmin 2vmin rgba(0, 0, 0, 0.1);
}

.title {
  font-family: var(--font-nevermind-bold);
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: bold;
  margin-bottom: 4vh;
  text-align: center;
}

.formContainer {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 3vh;
}

.form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 3vh;
  position: relative;
  z-index: 1;
}

.BackgroundImg {
  position: absolute;
  left: 50%;
  bottom: -12%;
  transform: translateX(-50%);
  z-index: -1;
  pointer-events: none;
  width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: contain;
  object-position: bottom;
}

.inputWrapper {
  position: relative;
  width: 100%;
}

.input {
  width: 100%;
  padding: 2vh 3vw;
  border: 0.2vmin solid #e0e0e0;
  border-radius: 2vmin;
  font-size: clamp(0.875rem, 2vw, 1rem);
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.input::placeholder {
  color: #6c757d;
}

.formInputError {
  border-color: #dd4a38;
  color: #dd4a38;
}

.errorMessage {
  background: #f8d7ce;
  color: #dd4a38;
  margin: 1vh 0;
  border-radius: 1vmin;
  padding: 1vh 2vw;
  font-size: clamp(0.75rem, 1.5vw, 0.875rem);
  display: flex;
  align-items: center;
}

.errorMessage img {
  margin-right: 1vw;
  width: clamp(16px, 3vw, 20px);
  height: auto;
}

.eyeIcon {
  position: absolute;
  right: 3vw;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 1vh;
  font-size: clamp(1rem, 2.5vw, 1.25rem);
}

.forgotPassword {
  width: 100%;
  margin-top: 26vh;
  padding: 2vh 3vw;
  border-radius: 1vmin;
  background: white;
  font-size: clamp(0.875rem, 2vw, 1rem);
  cursor: pointer;
  box-shadow: 0 0.5vmin 1vmin rgba(0, 0, 0, 0.2);
  transition: transform 0.2s;
  color: #000;
  border: 0.1vmin solid rgba(0, 0, 0, 0.4);
  font-family: var(--font-nevermind-bold);
}

.forgotPassword:hover {
  transform: translateY(-0.2vmin);
}

.backButton {
  position: absolute;
  top: 2vh;
  left: 2vw;
  cursor: pointer;
  padding: 1vh;
  transition: transform 0.2s;
  width: clamp(20px, 4vw, 30px) !important;
  height: auto !important;
}

.backButton:hover {
  transform: scale(1.1);
}

@media (max-height: 600px) {
  .container {
    padding: 4vh 4vw;
  }

  .title {
    margin-bottom: 4vh;
  }

  .form {
    gap: 2vh;
  }

  .input {
    padding: 1.5vh 3vw;
  }

  .forgotPassword {
    margin-top: 14vh;
    padding: 1.5vh 3vw;
  }
}

@media (max-width: 480px) {
  .loginContainer {
    padding: 0;
  }

  .container {
    border-radius: 0;
    height: 100%;
  }
}
