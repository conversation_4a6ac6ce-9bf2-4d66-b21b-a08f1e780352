import { useEffect } from "react";

const useTogglePinkFooter = (isHidden) => {
  useEffect(() => {
    const pinkFooter = document.getElementById("pinkFooterContainer");
    if (pinkFooter) {
      pinkFooter.style.display = isHidden ? "none" : "block";
    }

    return () => {
      if (pinkFooter) {
        pinkFooter.style.display = "block";
      }
    };
  }, [isHidden]);
};

export default useTogglePinkFooter;
