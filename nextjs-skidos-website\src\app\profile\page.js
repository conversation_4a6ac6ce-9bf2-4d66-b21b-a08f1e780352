"use client";
import { useAuth } from "@/context/AuthContext";
import apiClient from "@/utils/axiosUtil";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import vector from "../../../public/images/login-daskbox/LogoutLogo.png";
import appstoreqr from "../../../public/images/login-daskbox/appstoreqr.png";
import daskbox from "../../../public/images/login-daskbox/daskboxImage.webp";
import playstoreqr from "../../../public/images/login-daskbox/playstoreqr.png";
import vectord from "../../../public/images/login-daskbox/supportLogo.png";
import styles from "./styles.module.css";

const ProfilePage = () => {
  const [userDetails, setUserDetails] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [cancelStatus, setCancelStatus] = useState({ success: false, error: null });

  useEffect(() => {
    const storedUserDetails = JSON.parse(localStorage.getItem("UserDetails")) || {};
    setUserDetails(storedUserDetails);
  }, []);
  const { email, expiryDate, StripeId } = userDetails;
  const date = new Date(expiryDate);
  const router = useRouter();
  const t = useTranslations("Dashboard");
  const { logout, isSubscribed } = useAuth();

  const logoutHandler = () => {
    router.replace("/login");
    logout();
  };

  const handleCancelSubscription = async () => {
    if (!confirm("Are you sure you want to cancel your subscription?")) {
      return;
    }

    setIsLoading(true);
    setCancelStatus({ success: false, error: null });

    try {
      const response = await apiClient.get(
        `${process.env.NEXT_PUBLIC_SUBSSERVICE_BASE_URL}/subscription/cancel`
      );

      if (response.status === 200) {
        setCancelStatus({ success: true, error: null });

        // Update user details in localStorage
        const updatedUserDetails = { ...userDetails, isSubscribed: false };
        localStorage.setItem("UserDetails", JSON.stringify(updatedUserDetails));
        setUserDetails(updatedUserDetails);

        // Update auth context
        logout();
        setTimeout(() => {
          router.replace("/login");
        }, 2000);
      }
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      setCancelStatus({
        success: false,
        error:
          error.response?.data?.message || "Failed to cancel subscription. Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={`${styles.card} ${styles.loginBanner}`}>
          <div className={styles.bannerContent}>
            {isSubscribed && (
              <p className={styles.title}>
                {t("DashboardPlanLine1")} <br />
                {t("DashboardPlanLine2")}
                <br />
                {t("DashboardPlanLine3")}
              </p>
            )}
            {!isSubscribed && (
              <p className={styles.title}>
                {t("DashboardPlanLine4")} <br />
                {t("DashboardPlanLine5")}
                <br />
                <button
                  className={`${styles.button} ${styles.whiteButton}`}
                  onClick={() => {
                    router.push("/acquisition");
                  }}
                >
                  {t("DashboardAnnual")}
                </button>
              </p>
            )}
          </div>
          <Image
            src={daskbox}
            alt="Subscription illustration"
            className={styles.daskboxImage}
            width={210}
            height={173}
          />
        </div>

        <div style={{ textAlign: "-webkit-center" }}>
          <div className={styles.headerDiv}>
            <div className={styles.header}>
              <h1 className={styles.title}> {t("DashboardInformation")}</h1>
              <div>
                <button
                  className={`${styles.button} ${styles.ghostButton}`}
                  onClick={() => (window.location.href = "https://support.skidos.com/")}
                >
                  {t("DashboardSupport")}{" "}
                  <Image
                    style={{ marginLeft: "0.325rem" }}
                    src={vectord}
                    alt="Support"
                    width={13}
                    height={13}
                  />
                </button>
                <button
                  className={`${styles.button} ${styles.ghostButton}`}
                  onClick={logoutHandler}
                >
                  {t("DashboardLogout")}{" "}
                  <Image
                    style={{ marginLeft: "0.325rem" }}
                    src={vector}
                    alt="Logout"
                    width={13}
                    height={13}
                  />
                </button>
              </div>
            </div>
          </div>

          <p className={styles.email}>{email}</p>
          <div className={styles.headerDiv}>
            <div className={styles.header}>
              <h1 className={styles.subtitle}> {t("DashboardMembership")}</h1>
            </div>
          </div>
          <div className={styles.membershipInfoContent}>
            <div className={styles.membershipInfo}>
              <span className={styles.label}> {t("DashboardSubscript")}</span>
              <span className={`${styles.active} ${!isSubscribed ? styles.inactive : ""}`}>
                {isSubscribed ? t("DashboardActive") : t("DashboardInactive")}
              </span>
            </div>
            <div style={{ borderTop: "1px solid #B9B9B9" }}></div>
            {isSubscribed && (
              <div className={styles.membershipInfo}>
                <span className={styles.label}>{t("DashboardExpire")}</span>
                <span style={{ paddingRight: "1rem" }}>
                  {date.toLocaleDateString("en-GB", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  })}
                </span>
              </div>
            )}

            {isSubscribed && StripeId && (
              <div style={{ textAlign: "left" }}>
                <button
                  className={`${styles.button} ${styles.ghostButton}`}
                  onClick={handleCancelSubscription}
                  disabled={isLoading}
                  style={{ paddingLeft: 0, paddingTop: 0 }}
                >
                  {isLoading ? "Processing..." : t("DashboardCancelSubscription")}
                  <FontAwesomeIcon style={{ marginLeft: "0.325rem" }} icon={faArrowRight} />
                </button>

                {cancelStatus.success && (
                  <p style={{ color: "green", marginTop: "0.5rem" }}>
                    Your subscription has been successfully cancelled. You will be redirected
                    shortly.
                  </p>
                )}

                {cancelStatus.error && (
                  <p style={{ color: "red", marginTop: "0.5rem" }}>{cancelStatus.error}</p>
                )}
              </div>
            )}
          </div>
        </div>

        <div style={{ marginBottom: "1.6rem", textAlign: "-webkit-center" }}>
          <div className={styles.headerDiv}>
            <div className={styles.header} style={{ marginBottom: "1.6rem" }}>
              <h1 className={styles.subtitle}>{t("DashboardDownload")}</h1>
            </div>
          </div>
          <div className={styles.qrCodes}>
            <div>
              <Image
                src={appstoreqr}
                alt="App Store QR Code"
                className={styles.qrCode}
                width={200}
                height={200}
              />
              <p style={{ textAlign: "center" }}>{t("DashboardApp")}</p>
            </div>
            <div>
              <Image
                src={playstoreqr}
                alt="Play Store QR Code"
                className={styles.qrCode}
                width={200}
                height={200}
              />
              <p style={{ textAlign: "center" }}>{t("DashboardPlay")}</p>
            </div>
          </div>
        </div>

        <button
          className={`${styles.button} ${styles.exploreButton}`}
          onClick={() => {
            router.push(isSubscribed ? "/user-home-screen" : "/products");
          }}
        >
          {t("DashboardExplore")}
        </button>
      </div>
    </div>
  );
};
export default ProfilePage;
