"use client";
import { useEffect, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const useScrollAnimation = (animationOptions, dependencies) => {
  const elementsRef = useRef([]);

  useEffect(() => {
    elementsRef.current.forEach((element) => {
      if (element) {
        gsap.fromTo(
          element,
          { opacity: 0, y: 50 },
          {
            opacity: 1,
            y: 0,
            duration: animationOptions.duration || 1,
            ease: animationOptions.ease || "power3.out",
            scrollTrigger: {
              trigger: element,
              start: animationOptions.start || "top bottom",
              once: true,
              toggleActions: "play none none none",
              ...animationOptions.scrollTrigger,
            },
          }
        );
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, dependencies);

  return (element) => {
    if (element && !elementsRef.current.includes(element)) {
      elementsRef.current.push(element);
    }
  };
};

export default useScrollAnimation;
