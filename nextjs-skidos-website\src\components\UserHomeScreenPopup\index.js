import { useEffect } from "react";

import { extractGameData } from "@/utils/helperFunctions";
import { trackWebEngageEvent } from "@/utils/webengage";
import Image from "next/image";
import { useRouter } from "next/navigation";
import styles from "./styles.module.css";

const UserHomeScreenPopup = ({ isOpen, onClose, isDark, cardData }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add(styles.noScroll);
    } else {
      document.body.classList.remove(styles.noScroll);
    }
    return () => {
      document.body.classList.remove(styles.noScroll);
    };
  }, [isOpen]);
  if (!isOpen) return null;

  const popupBottomImg = isDark
    ? "/images/webGl/userHomeScreen/crater.webp"
    : "/images/webGl/userHomeScreen/grass.webp";
  const router = useRouter();

  return (
    <div className={styles.overlay}>
      <div className={styles.overlayContent}>
        <div className={styles.btnHeadingWrapper}>
          <div onClick={onClose} className={styles.backBtn}>
            <Image
              src="/images/webGl/userHomeScreen/bckBtn.webp"
              height={50}
              width={50}
              className={styles.backBtnImg}
              alt="Back button"
            />
          </div>
          <p className={styles.title}>{cardData.TileName}</p>
          <div></div>
        </div>
        <div className={styles.contentWrapper}>
          <div className={styles.content}>
            {cardData.GameCollection &&
              cardData.GameCollection.map((item, index) => {
                const { gameUrl, gameOrientation } = extractGameData(item.WebGameUrl);
                return (
                  <div
                    key={index}
                    role="button"
                    tabIndex={0}
                    aria-label={`Play ${item.DisplayName}`}
                    onClick={() => {
                      trackWebEngageEvent("WebGlGameClk", {
                        current_game: item?.GameID,
                      });
                      router.push(
                        `/game-player?gameUrl=${gameUrl}&gameName=Track-Rotate&gameOrientation=${gameOrientation}`
                      );
                    }}
                    style={{ cursor: "pointer" }}
                    onKeyDown={() => {}}
                    className={`${styles.addresebleGamesCard} noLinkStyle`}
                  >
                    <Image
                      src={item.IconURL}
                      width={220}
                      height={220}
                      className={styles.cardImgs}
                      alt={item.DisplayName}
                    />
                    <p>{item.DisplayName}</p>
                    <Image
                      width={65}
                      height={65}
                      src="/images/webGl/userHomeScreen/playBtnPopup.png"
                      className={styles.playBtn}
                      alt="Play Button"
                    />
                  </div>
                );
              })}
          </div>
        </div>

        <div className={styles.bottomImageWrapper}>
          <Image src={popupBottomImg} width={180} height={80} className={styles.popupBottomImgs} />
        </div>
        <div className={styles.bottomImageWrapperRight}>
          <Image src={popupBottomImg} width={180} height={80} className={styles.popupBottomImgs} />
        </div>
      </div>
    </div>
  );
};

export default UserHomeScreenPopup;
