
/* FIRST FRAME - Blue background with logos and characters */
/* .firstFrame {
  background: linear-gradient(180deg, #2850FF 0%, #FFF 93.45%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 40px 20px;
} */

.firstFrame {
  width: 100%;
  padding: 0;
  margin: 0;
  position: relative;
  overflow: hidden;
}

/* Desktop Banner - Hidden on mobile */
.desktopBanner {
  display: block;
  width: 100%;
  position: relative;
}

/* Mobile Banner - Hidden on desktop */
.mobileBanner {
  display: none;
  width: 100%;
  position: relative;
}

/* Common banner image styles */
.bannerImage {
  width: 100%;
  height: auto;
  object-fit: contain;
  object-position: center;
  max-height: 80vh;
  min-height: 300px;
  display: block;
}


.logoX{
  color: white;
  font-weight: bolder;
  font-size: clamp(2rem, 4vw, 3rem);
  margin: 0 0.5rem;
}

.headerLogo {
  display: flex;
  gap: clamp(0.5rem, 2vw, 1rem);
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.headerLogo img {
  width: auto;
  height: clamp(60px, 8vw, 100px);
  max-width: clamp(200px, 25vw, 300px);
}

.charactersSection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
}

.charactersSection img {
  width: 100%;
  height: auto;
  /* max-width: 1000px; */
  object-fit: contain;
}

/* SECOND FRAME - White background with text */
.secondFrame {
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  /* align-items: center; */
  padding: clamp(2rem, 8vw, 5rem) clamp(1rem, 4vw, 2rem);
  text-align: left;
 /* justify-content: center; */
  /* max-width: 1200px; */
  /* margin: 0 auto; */
  width: 100%;
  box-sizing: border-box;
}

.storeIconsWrapper{
  grid-template-columns: repeat(3, 1fr);
}

.mainHeading {
  font-size: clamp(2rem, 6vw, 4rem);
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #EA73C0;
  margin: 0 0 1rem 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  text-align: left;
  line-height: 1.2;
}

.subHeading {
  font-size: clamp(1rem, 3vw, 1.8rem);
  font-family: var(--font-poppins);
  margin: 0 0 1.5rem 0;
  color: #949494;
  text-align: left;
  font-weight: 500;
}

.description {
  font-size: clamp(1rem, 2.5vw, 1.4rem);
  color: #86868B;
  font-family: var(--font-poppins);
  line-height: 1.8;
  margin: 0 0 2rem 0;
  width: 100%;
  text-align: left;
  font-weight: 600;
}

/* Teaser Videos Section */
.teaserTrailerHeading {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 700;
  font-family: var(--font-poppins);
  margin: clamp(2rem, 6vw, 2.5rem) 0 clamp(1rem, 3vw, 1.25rem) 0;
  text-align: left;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.teaserVideosContainer {
  display: flex;
  gap: clamp(0.5rem, 2vw, 1rem);
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  justify-content: flex-start;
  box-sizing: border-box;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.teaserVideosContainer::-webkit-scrollbar {
  display: none;
}

.trailerVideoContainer {
  width: 100%;
  height: clamp(300px, 50vw, 746px);
  max-height: 746px;
  border: none;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  flex-shrink: 0;
  margin-bottom: 2rem;
}

.teaserVideoCard {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  width: clamp(280px, 60vw, 36rem);
  height: clamp(160px, 35vw, 20rem);
  flex-shrink: 0;
  min-width: 280px;
}


/* THIRD FRAME - Game screenshots */
.thirdFrame {
  padding: 0 0 clamp(2rem, 8vw, 5rem) 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.gamesContainer {
  display: flex;
  gap: clamp(0.75rem, 3vw, 1.25rem);
  padding: 0 clamp(1rem, 4vw, 1.25rem);
  width: max-content;
  min-width: 100%;
}

.gameCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  width: clamp(280px, 50vw, 560px);
  height: clamp(210px, 37.5vw, 420px);
  transition: transform 0.3s ease;
  flex-shrink: 0;
  min-width: 280px;
}

.gameCard:hover {
  transform: translateY(-5px);
}

.gameCard img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
/* MINIMAL RESPONSIVE ADJUSTMENTS */

/* Small screens: Center text and adjust layout */
@media only screen and (max-width: 768px) {
  /* Hide desktop banner and show mobile banner */
  .desktopBanner {
    display: none;
  }

  .mobileBanner {
    display: block;
  }

  /* Adjust banner image for mobile */
  .bannerImage {
    max-height: 65vh;
    min-height: 300px;
    object-position: center top;
    object-fit: contain;
  }

  .secondFrame {
    align-items: center;
    text-align: center;
  }

  .mainHeading,
  .subHeading,
  .description {
    text-align: center;
  }
}

/* iPad Mini and Surface Duo specific adjustments */
@media only screen and (min-width: 769px) and (max-width: 820px) and (min-height: 1000px) {
  .bannerImage {
    max-height: 50vh;
    min-height: 350px;
    object-position: center top;
    object-fit: contain;
  }
}

/* Surface Duo and similar dual-screen devices */
@media only screen and (min-width: 540px) and (max-width: 720px) and (min-height: 720px) {
  .desktopBanner {
    display: block;
  }

  .mobileBanner {
    display: none;
  }

  .bannerImage {
    max-height: 55vh;
    min-height: 300px;
    object-position: center top;
    object-fit: contain;
  }
}

/* Tablet screens: Use desktop banner but with adjusted sizing */
@media only screen and (min-width: 821px) and (max-width: 1024px) {
  .desktopBanner {
    display: block;
  }

  .mobileBanner {
    display: none;
  }

  .bannerImage {
    max-height: 60vh;
    min-height: 350px;
    object-position: center top;
    object-fit: contain;
  }
}

/* Desktop screens: Prevent cropping while maintaining full width */
@media only screen and (min-width: 1025px) and (max-height: 900px) {
  .desktopBanner {
    display: block;
  }

  .mobileBanner {
    display: none;
  }

  .bannerImage {
    width: 100%;
    height: auto;
    object-fit: contain;
    object-position: center;
    max-height: 60vh;
    min-height: auto;
  }
}

/* Large screens: Ensure desktop banner is optimized */
@media only screen and (min-width: 1025px) and (min-height: 901px) {
  .desktopBanner {
    display: block;
  }

  .mobileBanner {
    display: none;
  }

  /* Optimize banner for larger screens */
  .bannerImage {
    width: 100%;
    height: auto;
    object-fit: contain;
    object-position: center;
    max-height: none;
    min-height: auto;
  }
}

/* Extra large screens: Full width coverage */
@media only screen and (min-width: 1440px) {
  .bannerImage {
    width: 100%;
    height: auto;
    object-fit: contain;
    object-position: center;
    max-height: none;
    min-height: auto;
  }
}

/* Ultra-wide screens: Prevent excessive height */
@media only screen and (min-width: 1920px) {
  .bannerImage {
    width: 100%;
    height: auto;
    object-fit: contain;
    object-position: center;
    max-height: 80vh;
    min-height: auto;
  }
}

/* 4K and larger screens: Maintain proportions */
@media only screen and (min-width: 2560px) {
  .bannerImage {
    width: 100%;
    height: auto;
    object-fit: contain;
    object-position: center;
    max-height: 70vh;
    min-height: auto;
  }
}
