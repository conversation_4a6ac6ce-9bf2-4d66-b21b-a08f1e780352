.partnershipWrapper {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}

.contactUsHeader {
  max-width: 100%;
  background-image: url("/images/contactUs/contactUsBanner.webp");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  object-fit: cover;
  flex-direction: column;
}
.contactUsHeader > div {
  display: flex;
  width: 95%;
  justify-content: center;
  flex-direction: column;
}
.contactUsHeader > div:nth-child(1) {
  margin: 1.5rem auto;
  text-align: center;
}
.contactUsHeader > div:nth-child(2) {
  align-items: center;
}
.contactUsHeader h2 {
  font-size: 2rem;
  margin: 0;
}
.contactUsHeader p {
  font-size: 1.2rem;
  font-family: var(--font-poppins);
}
.contactUsMascotsImg {
  max-width: 90%;
  height: auto;
  object-fit: contain;
}

.carouselBannerBtn {
  font-family: var(--font-poppins);
  font-weight: 600;
  margin: 1.5rem;
  background-color: #9258fe;
  font-size: 2rem;
  color: #ffff;
  border-radius: 1rem;
  padding: 0.8rem 1rem;
  text-decoration: none;
  border: none;
  box-shadow: 0px 10px 0px rgba(74, 45, 128, 1);
  cursor: pointer;
}

.ourPartnersWrapper {
  padding: 1rem;
}
.ourPartnersWrapper h1 {
  font-size: 2.5rem;
}
.ourPartnersWrapper > div {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-direction: column;
  gap: 40px;
}

.waysCollaborateWrapper h1 {
  font-size: 2.5rem;
  text-align: center;
  background-color: #f7f7f7;
  padding: 1rem 0;
}
.typeOfWays {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}
.blueBg {
  background-color: #e0edff;
  padding: 3.5rem;
}
.typeOfWays p {
  text-align: center;
}

@media (min-width: 768px) {
  .contactUsHeader {
    flex-direction: row;
  }
  .contactUsHeader > div:nth-child(2) {
    width: 70%;
    text-align: left;
  }
  .contactUsHeader > div:nth-child(1) {
    padding: 0 3rem;
    margin: 2rem auto;
    display: block;
    margin: auto 0;
    text-align: left;
  }
  .contactUsHeader h2 {
    font-size: 3rem;
    margin: 0;
  }
  .contactUsHeader p {
    font-size: 1.5rem;
  }
  .carouselBannerBtn {
    margin: 0;
    padding: 0.8rem 2rem;
  }
  .ourPartnersWrapper {
    padding: 3rem;
  }
  .ourPartnersWrapper > div {
    flex-direction: row;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .contactUsHeader > div:nth-child(1) {
    padding: 2rem 1rem;
    margin: 0 auto;
  }
  .contactUsHeader h2 {
    font-size: 2.5rem;
  }
  .contactUsHeader p {
    font-size: 1.2rem;
  }
  .ourPartnersWrapper > div {
    flex-direction: column;
  }
}
