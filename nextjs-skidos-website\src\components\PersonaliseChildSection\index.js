"use client";
import FormCtaButton from "@/components/FormCtaButton";
import { inforPopupData } from "@/constants";
import { usePlayerContext } from "@/context/CreatePlayerContext";
import apiClient from "@/utils/axiosUtil";
import { getLocale } from "@/utils/helperFunctions";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useState } from "react";
import InfoPopup from "../WebGlInfoPopup";
import styles from "./styles.module.css";

const cache = {
  themesData: null,
};

const PersonaliseChildSection = ({ onSubmit }) => {
  const { playerData, updatePlayerData } = usePlayerContext();
  const [themesData, setThemeData] = useState([]);
  const [selectedThemes, setSelectedThemes] = useState(playerData.selectedThemes || []);
  const [showPopup, setShowPopup] = useState(false);
  const [error, setError] = useState("");
  const locale = useLocale();
  const lang = getLocale(locale);
  const t = useTranslations("PersonalisePlayer");

  const fetchInterests = async () => {
    try {
      const url = `${process.env.NEXT_PUBLIC_PRODUCTSERVICE_BASE_URL}/interest?l=${lang}&gameid=superstore&version=8.0&platform=ios`;
      const response = await apiClient.get(url);
      setThemeData(response.data);

      // Cache the data and locale
      cache.themesData = response.data;
    } catch (error) {
      console.error("Error fetching interests:", error);
    }
  };
  useEffect(() => {
    if (cache.themesData) {
      setThemeData(cache.themesData);
    } else {
      fetchInterests();
    }
  }, [locale]);

  const handleThemeToggle = (interestId) => {
    setSelectedThemes((prevSelectedThemes) => {
      const updatedThemes = prevSelectedThemes.includes(interestId)
        ? prevSelectedThemes.filter((id) => id !== interestId)
        : [...prevSelectedThemes, interestId];

      updatePlayerData({ selectedThemes: updatedThemes });
      return updatedThemes;
    });
    setError("");
  };

  const handleSubmit = () => {
    if (selectedThemes.length === 0) {
      setError("Please select at least one theme to proceed.");
      return;
    }
    onSubmit();
  };

  return (
    <div className={styles.personalisePlayerWrapper}>
      <header className={styles.createPlayerHeader}>
        <h1>{t("Heading")}</h1>
        <p>{t("SubHeading")}</p>
      </header>
      {themesData && (
        <main className={styles.personilisationContent}>
          {themesData.map((theme, index) => (
            <div
              key={index}
              className={styles.themeItem}
              onClick={() => handleThemeToggle(theme.InterestID)}
            >
              <Image
                src={theme.PictureUrl}
                className={`${styles.personaliseItem} ${
                  selectedThemes.includes(theme.InterestID) ||
                  playerData.selectedThemes.includes(theme.InterestID)
                    ? styles.selected
                    : ""
                }`}
                width={115}
                height={115}
              />
              <p>{theme.DisplayName}</p>
            </div>
          ))}
        </main>
      )}
      {error && (
        <div className={styles.errorWrapper}>
          <p>
            <Image src="/images/webGl/warning.png" height={15} width={15} /> {t("ErrorMsgAll")}
          </p>
        </div>
      )}
      <FormCtaButton text={t("CtaBtn")} onClick={handleSubmit} />
      <p className={styles.infoText} onClick={() => setShowPopup((prev) => !prev)}>
        <Image src="/images/webGl/normalInfo.png" height={15} width={15} alt="Info" />{" "}
        {t("InfoText")}
      </p>
      {showPopup && (
        <InfoPopup
          isOpen={showPopup}
          onClose={() => setShowPopup((prev) => !prev)}
          data={inforPopupData[1]}
          localeData="PersonalisePlayer"
        />
      )}
    </div>
  );
};
export default PersonaliseChildSection;
