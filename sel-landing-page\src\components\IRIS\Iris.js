import Header from "components/Header";
import Footer from "../Footer/Footer";
import "react-responsive-carousel/lib/styles/carousel.min.css"; // requires a loader
import { Carousel } from "react-responsive-carousel";
import ReactGA from "react-ga4";


/*images*/
import certificationOne from "icons/iris/iris-certification-one.png";
import certificationTwo from "icons/iris/iris-certification-two.png";
import guidedByScience from "icons/iris/iris-certification-three.png";
import certificationFour from "icons/iris/iris-coppa.png";
import leftArrow from "icons/rekindle-minds/left-arrow.png";
import rightArrow from "icons/rekindle-minds/right-arrow.png";
import bikeRacing from "icons/rekindle-minds/bike-racing.png";
import doctor from "icons/rekindle-minds/Doctor.png";
import bath from "icons/rekindle-minds/Bath.png";
import superstore from "icons/rekindle-minds/Supertstore.png";
import irisReviewOne from "icons/iris/iris-review-one-new.png"
import irisReviewTwo from "icons/iris/iris-review-two-new.png"
import irisReviewThree from "icons/iris/iris-review-three-new.png"

import joinReadingAdventure from "icons/iris/join-reading-adventure.png"
import scienceOfReading from "icons/iris/science-of-reading.png"

/*player */
import Plyr from "plyr-react";
import "plyr-react/plyr.css";


import "./Iris.css";
function Iris() {

  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen"
  ];

  const eventTrackerGa4 = (category, action, label, nonInteraction) => {
    ReactGA.event({ category, action, label, nonInteraction });
  };

  return (
    <>
     {/* <div className="iris-minds"> */}
      <Header isSubscribed={false} />
      <div className="iris-minds">
      <div className="hero">
        <div className="container">
          <div className="row d-flex justify-content-center header">
            <div className="col-12 col-md-5 text-center">
              <h1 className="h2">
              An exciting journey to develop early reading skills
              </h1>
            </div>
          </div>
        </div>
      </div>
    <div className="iris-content-box">
      <div className="container" id="player-container">
        <div className="row d-flex justify-content-center">
          <div className="col-12 col-md-8">
            <div className="player-container">
            <Plyr
                id="plyr"
                controls
                options={{ volume: 0.1, controls }}
                source={{
                  type: "video",
                  sources: [
                    {
                      src: "https://youtu.be/HAJHytYL1xA",
                      provider: "youtube",
                    },
                  ],
                }}
                // ref={ref}
              />
            </div>
          </div>
        </div>
        <div className="iris-fiks-container">
          <div className="iris-fiks-container-img">
            <img src={joinReadingAdventure} alt="Join Reading Adventure" />
          </div>
          <div>
            <h2>Join the reading adventure</h2>
            <p>
            Learn letters, phonics, and first words  on a delightful treasure hunt!
            </p>
          </div>
        </div>

        <div className="iris-fiks-container">
          <div className="iris-fiks-container-img">
            <img src={scienceOfReading} alt="Science Of Reading" />
          </div>
          <div>
            <h2>Aligned with Science of Reading (SoR)</h2>
            <p>
            Develop reading fluency and confidence with 20+ expert-approved learning activities
            </p>
          </div>
        </div>
      </div>
      <div className="iris-ribbon-block">
        <div className="iris-ribbon"></div>
      </div>
      <div className="container iris-minds mt-7">
        {/* <div className="row container-block">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <img src={progressReportImg} alt="Progress Report" />
          </div>
          <div className="col-12 col-md-6">
            <h2>Expert-approved guide to SEL</h2>
            <p>
              Lay a strong foundation for social-emotional learning with 5
              playful islands designed as per the CASEL framework
            </p>
          </div>
        </div> */}
        <div className="row mt-3">
          <div className="col-12">
            <div className="iris-carousel mt-7">
              <Carousel
                infiniteLoop={true}
                autoPlay={true}
                showStatus={false}
                showIndicators={false}
                swipeable={false}
                renderArrowPrev={(clickHandler, hasPrev) => {
                  return (
                    <div className={`arrow left-arrow`} onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <img src={leftArrow} alt="Left" />
                      </button>
                    </div>
                  );
                }}
                renderArrowNext={(clickHandler, hasNext) => {
                  return (
                    <div className={`arrow right-arrow`} onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <img src={rightArrow} alt="Right" />
                      </button>
                    </div>
                  );
                }}
              >
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      {/* <img src={reviewTwo} className="left" alt="Left" />
                      <img src={reviewOne} className="center" alt="Center" />
                      <img src={reviewThree} className="right" alt="Right" /> */}
                      <img src={irisReviewOne}/>
                      {/* <div className="dots">
                        <span className="active"></span>
                        <span></span>
                        <span></span>
                      </div> */}
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "IRIS has exceeded my expectations. The app's curriculum covers all aspects of early language learning, and its vibrant visuals and catchy tunes keep children engaged and enthusiastic.“
                      <br /> <br />Jaya Sharma, Child Learning Expert
                    </p>
                  </div>
                </div>
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      {/* <img src={reviewOne} className="left" alt="Left" />
                      <img src={reviewTwo} className="center" alt="Center" />
                      <img src={reviewThree} className="right" alt="Right" />
                      <div className="dots">
                        <span></span>
                        <span className="active"></span>
                        <span></span>
                      </div> */}
                       <img src={irisReviewTwo}/>
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                    "IRIS is an educational masterpiece. It has given my child a solid understanding of English letters, sounds, and word formation. The progress they've made is truly remarkable! I highly recommend“
                      <br /> <br />- Alice Williams, School Counselor & Mom
                    </p>
                  </div>
                </div>
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      {/* <img src={reviewTwo} className="left" alt="Left" />
                      <img src={reviewThree} className="center" alt="Center" />
                      <img src={reviewTwo} className="right" alt="Right" />
                      <div className="dots">
                        <span></span>
                        <span></span>
                        <span className="active"></span>
                      </div> */}
                       <img src={irisReviewThree}/>
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                    "As a parent, I'm thrilled with IRIS! It's the perfect blend of education and entertainment. My child looks forward to their English lessons every day, thanks to the app’s inviting games."
                      <br /> <br />- Emma Daniels,  Mom to 3 y/o
                    </p>
                  </div>
                </div>
              </Carousel>
            </div>
          </div>
        </div>

        <div className="row d-flex justify-content-center mt-7">
          <span className="games-heading">Explore Now In Top SKIDOS Games</span>
          <div className="games-block">
            <div className="games">
              <img
                width="200"
                height="160"
                src={bikeRacing}
                alt="Skidos Games"
              />
              <span>Bike Racing</span>
              <a href="https://apps.apple.com/us/app/cool-math-games-kids-racing/id1319262120">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "iris_bike_racing_download_clicked",
                      "Bike Racing Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <img width="200" height="160" src={doctor} alt="Skidos Games" />
              <span>Doctor</span>
              <a href="https://apps.apple.com/us/app/doctor-games-for-kids/id1506886061">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "iris_doctor_download_clicked",
                      "Doctor Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <img width="200" height="160" src={bath} alt="Skidos Games" />
              <span>Bath</span>
              <a href="https://apps.apple.com/us/app/learning-games-for-kids/id1483744837">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "iris_bath_download_clicked",
                      "Bath Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <img
                width="200"
                height="160"
                src={superstore}
                alt="Skidos Games"
              />
              <span>Superstore</span>
              <a href="https://apps.apple.com/us/app/fun-games-kids-preschool-math/id1497549298">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "iris_superstore_download_clicked",
                      "Superstore Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
          </div>
        </div>
        <div className="row container-block d-flex justify-content-center mt-7">
          <span className="games-heading">
            Safe Play- Learning For Your Kid
          </span>
          <div className="certification-block">
            <div className="certification">
              <img src={certificationFour} alt="Skidos Certification" />
              <p className="mt-2">Strict privacy compliance</p>
            </div>
            <div className="certification">
              <img src={certificationTwo} alt="Skidos Certification" />
              <p className="mt-2">Designed by educators & experts</p>
            </div>
            <div className="certification">
              <img src={guidedByScience} alt="Skidos Certification" />
              <p className="mt-2">Guided by Science</p>
            </div>
            <div className="certification">
              <img src={certificationOne} alt="Skidos Certification" />
              <p className="mt-2">Secure learning environment</p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
      </div>
    </div>
    </>
  );
}
export default Iris;
