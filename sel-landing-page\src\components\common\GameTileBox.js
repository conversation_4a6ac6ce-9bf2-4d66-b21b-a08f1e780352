import React, {
  useLayoutEffect,
  useRef,
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import PropTypes from "prop-types";
import { useNavigate, useSearchParams } from "react-router-dom";

// gsap
import { gsap } from "gsap";

/* handling game and videos inputs */
import { getData } from "services/ApiHandler";
import {
  endPoint,
  getGames,
  getVideos,
  getUser,
  getPlayers,
} from "services/Constants";

// eslint-disable-next-line react/display-name
const GameTileBox = forwardRef(
  (
    { listType, isShowMore, isSubscribed, valuePropositionModal, userEmail },
    ref
  ) => {
    const [nextGamesUrl, setNextGamesUrl] = useState("");
    const [nextVideosUrl, setNextVideosUrl] = useState("");
    const [isShowMoreGames, setIsShowMoreGames] = useState(false);
    const [isShowMoreVideos, setIsShowMoreVideos] = useState(false);

    const [searchParams, setSearchParams] = useSearchParams();

    /*games api data*/
    const getAPiData = getData;
    const [freeGames, setFreeGames] = useState({});
    const [premiumGames, setPremiumGames] = useState({});
    const [videos, setVideos] = useState({});

    const container = useRef();
    const tl = useRef();
    const scrollableDiv = useRef();
    let navigate = useNavigate();

    const loadGame = (gameName, gameUrl) => {
      if (listType === "Premium Games" && !isSubscribed) {
        valuePropositionModal(true);
      } else {
        navigate("/game", {
          state: {
            gameName: gameName,
            gameUrl: gameUrl,
            isSubscribed: isSubscribed,
            userEmail: userEmail,
          },
        });
        window.scrollTo(100, 100);
      }
    };

    const loadVideo = (videoTitle, videoUrl) => {
      if (listType === "Videos" && !isSubscribed) {
        valuePropositionModal(true);
      } else {
        navigate("/video", {
          state: {
            videoTitle: videoTitle,
            videoUrl: videoUrl,
            isSubscribed: isSubscribed,
            userEmail: userEmail,
          },
        });
        window.scrollTo(100, 100);
      }
    };

    /* handling game and videos tilebox */

    const getFreeGames = async () => {
      let response = await getAPiData(
        endPoint + getGames + "0&count=8&page=1&category=all"
      );
      setFreeGames(response?.instantgame);
      let parseGameByUrl = searchParams.get("gameId");
      if (parseGameByUrl) {
        let temp = response?.instantgame[parseGameByUrl];
        let auth_token = localStorage.getItem("token");
        let email = "";
        let isSubscribed = false;
        if (auth_token) {
          let response = await getAPiData(getUser, auth_token);
          email = response?.Email;
          isSubscribed = response?.isSubscribed;
        }
        if (temp) {
          navigate("/game", {
            state: {
              gameName: temp.GameName,
              gameUrl: temp.GameURL,
              isSubscribed: isSubscribed,
              userEmail: email,
            },
          });
        } else {
          navigate("/");
        }
      }
    };

    const getPremiumGames = async () => {
      let response = await getAPiData(
        endPoint + getGames + "1&count=8&page=1&category=all"
      );
      setPremiumGames(response?.instantgame);
      setNextGamesUrl(response?.page?.nextUrl);
      setIsShowMoreGames(response?.page?.nextUrl ? true : false);
    };

    const getVideosAll = async () => {
      let response = await getAPiData(
        endPoint + getVideos + "7.0&count=8&page=1"
      );
      setVideos(response?.video);
      setNextVideosUrl(response?.page?.nextUrl);
      setIsShowMoreVideos(response?.page?.nextUrl ? true : false);
    };
    const loadMoreData = async (type) => {
      if (type === "Premium Games" && nextGamesUrl) {
        if (nextGamesUrl === "done") {
          return false;
        }
        let response = await getAPiData(nextGamesUrl + "&category=all");
        if (response?.instantgame) {
          let updatedGames = [...premiumGames, ...response?.instantgame];
          setPremiumGames(updatedGames);
          setTimeout(() => {
            var objDiv = document.querySelector(".scroll_tile_box");
            objDiv.scrollTop = objDiv.scrollHeight;
          }, 550);
        }
        if (response?.page?.nextUrl) {
          setNextGamesUrl(response?.page?.nextUrl);
        } else {
          setNextGamesUrl("done");
          setIsShowMoreGames(false);
        }
      }
      if (type === "Videos" && nextVideosUrl) {
        if (nextVideosUrl === "done") {
          return false;
        }
        let response = await getAPiData(nextVideosUrl);
        if (response?.video) {
          let updateVideos = [...videos, ...response?.video];
          setVideos(updateVideos);
          setTimeout(() => {
            var objDiv = document.querySelectorAll(".scroll_tile_box");
            for (let i = 0; i < objDiv.length; i++) {
              objDiv[i].scrollTop = objDiv[i].scrollHeight;
            }
          }, 550);
        }
        if (response?.page?.nextUrl) {
          setNextVideosUrl(response?.page?.nextUrl);
        } else {
          setNextVideosUrl("done");
          setIsShowMoreVideos(false);
        }
      }
    };
    useEffect(() => {
      getFreeGames();
      getPremiumGames();
      getVideosAll();
    }, []);
    useLayoutEffect(() => {
      const ctx = gsap.context((self) => {
        tl.current = gsap.fromTo(
          self.selector(".game_tile_box"),
          {
            duration: 1,
            y: 100,
            opacity: 0.5,
          },
          {
            duration: 1,
            y: 0,
            opacity: 1,
          }
        );
      }, container); // <- Scope!
      return () => ctx.revert(); // <- Cleanup!
    }, []);

    if (searchParams.get("gameId")) {
      return (
        <div className="loader-container">
          <span className="loader"></span>
        </div>
      );
    }
    return (
      //style={{ marginTop: isShowMore ? 15 : 0 }}, //${isShowMore ? "scroll_tile_box" : ""}
      <>
        <div ref={container}>
          <div className="game_tile_box">
            <div
              className={`row game_tile_row scroll_tile_box`}
              ref={scrollableDiv}
            >
              {freeGames.length &&
                listType === "Free Games" &&
                freeGames?.map((item, index) => (
                  <div
                    key={item?.ID}
                    className="col-6 col-sm-6 col-md-4 col-lg-3"
                    onClick={() => loadGame(item?.GameName, item?.GameURL)}
                  >
                    <div
                      className={`game_tile topgap-sm-20 ${
                        index > 3 ? "topgap20" : ""
                      }`}
                      style={{ backgroundImage: `url(${item?.IconURL})` }}
                    >
                      <span className="game_tile_title">{item.GameName}</span>
                    </div>
                  </div>
                ))}
              {premiumGames.length &&
                listType === "Premium Games" &&
                premiumGames?.map((item, index) => (
                  <div
                    key={item?.ID}
                    className="col-6 col-sm-6 col-md-4 col-lg-3"
                    onClick={() => loadGame(item?.GameName, item?.GameURL)}
                  >
                    <div
                      className={`game_tile topgap-sm-20 ${
                        index > 3 ? "topgap20" : ""
                      }`}
                      style={{ backgroundImage: `url(${item?.IconURL})` }}
                    >
                      <span className="game_tile_title">{item.GameName}</span>
                    </div>
                  </div>
                ))}
              {videos.length &&
                listType === "Videos" &&
                videos?.map((item, index) => (
                  <div
                    key={item?.Id}
                    className="col-6 col-sm-6 col-md-4 col-lg-3"
                    onClick={() => loadVideo(item?.VideoTitle, item.VideoURL)}
                  >
                    <div
                      className={`game_tile topgap-sm-20 ${
                        index > 3 ? "topgap20" : ""
                      }`}
                      style={{
                        backgroundImage: `url(${item?.VideoThumbnailURL})`,
                      }}
                    >
                      <span className="game_tile_title">
                        {item.VideoTitle.slice(0, 6)}...
                      </span>
                    </div>
                  </div>
                ))}
            </div>
            {isShowMoreGames && listType === "Premium Games" && (
              <div className="text-center topgap20">
                <button
                  className="btn-orange"
                  onClick={() => loadMoreData("Premium Games")}
                >
                  {isShowMore} Show more
                </button>
              </div>
            )}
            {isShowMoreVideos && listType === "Videos" && (
              <div className="text-center topgap20">
                <button
                  className="btn-orange"
                  onClick={() => loadMoreData("Videos")}
                >
                  {isShowMore} Show more
                </button>
              </div>
            )}
          </div>
        </div>
      </>
    );
  }
);

export default GameTileBox;

GameTileBox.propTypes = {
  setMoreGames: PropTypes.func,
};
