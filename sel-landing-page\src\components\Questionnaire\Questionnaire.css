.questionnaire {
  background-color: #f6f6f6;
}
.questionnaire .header {
  align-items: center;
  justify-content: center;
  padding: 25px;
}
.questionnaire .header h3 {
  font-size: 1.05rem;
  text-align: right;
  margin-right: 5px;
}
.questionnaire .highlight {
  color: #878787;
}
.questionnaire .highlight .box {
  padding: 15px;
}
.questionnaire .box p {
  padding: 25px;
  background-color: #fff;
  border-radius: 5px;
  border-bottom: 5px solid #a0a0a0;
}
.questionnaire .divider {
  background-color: #dddbdb;
  height: 3px;
  margin: 2px;
  position: relative;
}
.questionnaire .divider::before {
  content: "";
  position: absolute;
  height: 4px;
  width: 45%;
  margin-top: -1px;
  background: rgb(138, 233, 73);
  background: linear-gradient(
    90deg,
    rgb(179 255 126 / 94%) 0%,
    rgb(90 218 3) 35%,
    rgb(29 206 5) 100%
  );
  max-width: 475px;
}
.questionnaire .spinner-border {
  margin-right: 10px;
}
.questionnaire table td {
  font-weight: 600;
}
.questionnaire table th,
.questionnaire table td {
  background-color: transparent;
}
.questionnaire table th {
  padding: 50px 0;
  font-size: 0.85rem;
  color: #a0a0a0;
  font-weight: 500;
  text-align: center;
}
.questionnaire table td.content {
  max-width: 300px;
  font-weight: 600;
  font-size: 0.95rem;
}
.questionnaire table tbody tr {
  border-top: 1.5px solid #dddbdb;
}
.questionnaire table tbody tr td {
  padding: 35px 0 25px;
}
.questionnaire table td input[type="radio"] {
  accent-color: #232323;
  width: 100%;
  height: 1.15em;
  margin-top: 10px;
}
.questionnaire .btn {
  border-radius: 10px;
  background-color: #f1f1f1;
  color: #333;
  font-weight: 600;
  padding: 5px 25px;
  border: 1px solid #333;
}
.questionnaire .btn:hover {
  background-color: #d6d6d6;
  box-shadow: 4px 3px 0px 2px #333;
}
/* width */
.questionnaire .table-conatiner::-webkit-scrollbar {
  width: 02px;
}

/* Track */
.questionnaire .table-conatiner::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
.questionnaire .table-conatiner::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
.questionnaire .table-conatiner::-webkit-scrollbar-thumb:hover {
  background: #555;
}
/*mobile*/
@media only screen and (max-device-width: 480px) {
  .questionnaire .header h3 {
    text-align: left;
    margin-top: 25px;
  }
  .questionnaire .table-conatiner {
    overflow-x: auto;
  }
  .questionnaire .btn {
    margin-top: 25px;
  }
  .questionnaire table th {
    min-width: 125px;
  }
}
