"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+icu-skeleton-parser@1.8.13";
exports.ids = ["vendor-chunks/@formatjs+icu-skeleton-parser@1.8.13"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjEzL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEI7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtc2tpZG9zLXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjEzL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvaW5kZXguanM/YTkyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2RhdGUtdGltZSc7XG5leHBvcnQgKiBmcm9tICcuL251bWJlcic7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjEzL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1za2lkb3Mtd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AZm9ybWF0anMraWN1LXNrZWxldG9uLXBhcnNlckAxLjguMTMvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanM/MGJkMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAZ2VuZXJhdGVkIGZyb20gcmVnZXgtZ2VuLnRzXG5leHBvcnQgdmFyIFdISVRFX1NQQUNFX1JFR0VYID0gL1tcXHQtXFxyIFxceDg1XFx1MjAwRVxcdTIwMEZcXHUyMDI4XFx1MjAyOV0vaTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjEzL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEI7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtc2tpZG9zLXdlYnNpdGUvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjEzL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvaW5kZXguanM/NmZhNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2RhdGUtdGltZSc7XG5leHBvcnQgKiBmcm9tICcuL251bWJlcic7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjEzL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1za2lkb3Mtd2Vic2l0ZS8uL25vZGVfbW9kdWxlcy8ucG5wbS9AZm9ybWF0anMraWN1LXNrZWxldG9uLXBhcnNlckAxLjguMTMvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanM/MTY3NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBAZ2VuZXJhdGVkIGZyb20gcmVnZXgtZ2VuLnRzXG5leHBvcnQgdmFyIFdISVRFX1NQQUNFX1JFR0VYID0gL1tcXHQtXFxyIFxceDg1XFx1MjAwRVxcdTIwMEZcXHUyMDI4XFx1MjAyOV0vaTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.13/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n");

/***/ })

};
;