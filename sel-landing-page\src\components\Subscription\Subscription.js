import React, { useEffect, useRef, useState } from "react";
import axios from "axios";
import { useNavigate, useSearchParams } from "react-router-dom";
import Header from "components/Header";
import Footer from "../Footer/Footer";

/* api handler */
import { apiBasePath } from "services/Constants";

import "react-responsive-carousel/lib/styles/carousel.min.css"; // requires a loader

/*images*/
import frame71 from "icons/subscription/frame-71.png";
import sticker from "icons/subscription/sticker.png";
import lock from "icons/subscription/lock.png";
import icons1 from "icons/subscription/icons-1.png";
import time from "icons/subscription/time.png";
import person from "icons/subscription/person.png";
import frame70 from "icons/subscription/frame-70.png";
import frame28 from "icons/subscription/frame-28.png";
import rating from "icons/subscription/rating.png";
import qt from "icons/subscription/qt.png";
import arrowLeft from "icons/subscription/arrow-left.png";
import arrowright from "icons/subscription/arrow-right.png";
import viewAll from "icons/subscription/view-all.png";

// external css
import "./Subscription.css";
function Subscription() {
  const ref = useRef(null);
  const navigate = useNavigate();
  const [selectedPlan, setSelectedPlan] = useState("plan_3");
  const [selectedFaq, setSelectedFaq] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [plans, setPlans] = useState([]);
  const faqlist = [
    {
      title: "What is included in SKIDOS Pass?",
      content: "Sample content",
    },
    {
      title: "Where can I use my SKIDOS Pass?",
      content: "Sample content",
    },
    {
      title: "Can I cancel/switch plans any time?",
      content: "Sample content",
    },
  ];
  const reviewsList = [
    {
      name: "Sarah Honart",
      tag: "Mom to 7 y/o",
      content:
        "Love how this app helps the young ones understand their feelings! My daughter Zoey is very sensitive so she likes this game a lot",
    },
    {
      name: "Maria Adamms",
      tag: "School Counselor",
      content:
        "Game-changer! This app teaches children to understand and express their emotions in a healthy way.",
    },
    {
      name: "Dr. Thor Mitch",
      tag: "Child Psychologist",
      content:
        "The interactive videos are a wonderful way to help guide kids on what is right or wrong",
    },
  ];
  const activeFaq = (ind) => {
    setSelectedFaq(ind);
  };
  const getPlans = (data) =>
    axios.get(
      `${apiBasePath}subscriptionservice/v1/plan?version=5.0&l=en_us&planType=${data}`
    );
  const getPlanList = async () => {
    const planType = "acquisition";
    const token = window.localStorage.getItem("token");
    if (token) {
      setIsLoggedIn(true);
    }
    getPlans(planType).then((response) => {
      setPlans(response?.data);
      setSelectedPlan(response?.data[2].PlanID);
    });
  };
  const faqs = faqlist.map((el, ind) => {
    return (
      <div
        key={`faq_${ind}`}
        className={selectedFaq === ind ? "col-12 faq active" : "col-12 faq"}
      >
        <h3 onClick={() => activeFaq(ind)}>What is included in SKIDOS Pass?</h3>
        <div className={selectedFaq === ind ? "content active" : "content"}>
          Sample Content
        </div>
      </div>
    );
  });
  const reviews = reviewsList.map((el, ind) => {
    return (
      <div key={`el_${ind}`} className="col-12 col-sm-4">
        <div className="review">
          <img className="qt" src={qt} />
          <h4>{el.name}</h4>
          <h5>{el.tag}</h5>
          <p className="mt-4">{el.content}</p>
          <img className="rating" src={rating} />
        </div>
      </div>
    );
  });
  const handleSelectedPlan = () => {
    localStorage.setItem("selectedPlan", selectedPlan);
    navigate(isLoggedIn ? "/email" : "/email");
  };
  useEffect(() => {
    getPlanList();
  }, []);
  return (
    <div className="subscription">
      <Header />
      <div className="container">
        <div className="row">
          <div className="col-12 col-md-7">
            <h1>
              Give your child the
              <br />
              key to succeed
            </h1>
            <div className="hero-second">
              <img src={frame71} />
            </div>
          </div>
          <div className="col-12 col-md-5 mt-3">
            {plans &&
              plans.map((el, i) => {
                return (
                  <>
                    {el.PlanFreq === "annual" ? (
                      <div
                        key={`plan_${el.PlanID}`}
                        className={`price-block best-value ${
                          selectedPlan === el.PlanID ? "selected" : ""
                        }`}
                        onClick={() => setSelectedPlan(el.PlanID)}
                      >
                        <div className="pricing">
                          <div className="left">
                            <span className="best-value-badge">
                              <img src={sticker} /> Best Value
                            </span>
                            <h3>{el.PlanTextTransformed.plan}</h3>
                            <p>
                              {el.PlanTextTransformed.annualText}{" "}
                              {el.PlanTextTransformed.cpm}
                            </p>
                          </div>
                          <div className="right">
                            <h3>{el.PlanTextTransformed.price}</h3>
                            {el.PlanTextTransformed?.nonDiscountedPrice ? (
                              <>
                                <span className="base-price">
                                  {el.PlanTextTransformed?.nonDiscountedPrice}
                                </span>
                                <span className="offer-badge">20% Off</span>
                              </>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div
                        key={`plan_${el.PlanID}`}
                        className={`price-block ${
                          selectedPlan === el.PlanID ? "selected" : ""
                        }`}
                        onClick={() => setSelectedPlan(el.PlanID)}
                      >
                        <div className="pricing">
                          <div className="left">
                            <h3>{el.PlanTextTransformed.plan}</h3>
                            <p>
                              {el.PlanTextTransformed.annualText}{" "}
                              {el.PlanTextTransformed.cpm}
                            </p>
                          </div>
                          <div className="right">
                            <h3>{el.PlanTextTransformed.price}</h3>
                            {el.PlanTextTransformed?.nonDiscountedPrice ? (
                              <>
                                <span className="base-price">
                                  {el.PlanTextTransformed?.nonDiscountedPrice}
                                </span>
                                <span className="offer-badge">20% Off</span>
                              </>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                );
              })}
            <button
              className="skidos-btn-primary mt-4 arrow"
              onClick={handleSelectedPlan}
            >
              Activate
            </button>
          </div>
        </div>
      </div>
      <div className="container mt-5">
        <div className="row">
          <div className="feature-box">
            <img src={lock} />
            <h3>Unlock new skills</h3>
            <p>
              Let your child learn powerful skills to be a confident leader in
              life
            </p>
          </div>
          <div className="feature-box">
            <img src={icons1} />
            <h3>Boost school grades</h3>
            <p>
              Watch your child’s performance skyrocket with stress-free learning
            </p>
          </div>
          <div className="feature-box">
            <img src={time} />
            <h3>Smart screen time</h3>
            <p>
              Enjoy meaningful & productive screen time with value-added games
              for your child
            </p>
          </div>
          <div className="feature-box">
            <img src={person} />
            <h3>Expert-approved</h3>
            <p>
              Get the best of learning with carefully-designed activities by
              trusted educators
            </p>
          </div>
        </div>
        <div className="row text-center mt-5">
          <h2 className="mt-5">Unlock RekindleMinds + a lot MORE!</h2>
        </div>
        <div className="row bg-blue mt-5">
          <div className="col-12 col-md-6">
            <h3>
              SKIDOS Pass is your child's gateway to an entire fun learning
              universe!
            </h3>
            <p className="mt-4">
              Designed for kids aged 3-11, the SKIDOS Pass lets your child play
              40+ games while learning Math, Reading, and crucial 21st century
              skills to succeed in the modern world.
            </p>
          </div>
          <div className="col-12 col-md-6 text-center">
            <img src={frame70} />
          </div>
        </div>
        <div className="row mt-5 text-center">
          <h2 className="mt-5">Frequently asked questions</h2>
        </div>
        <div className="row mt-5 faqs">{faqs}</div>
        <div className="row mt-4 view-more-faq">
          <img src={viewAll} />
        </div>
        <div className="row mt-5 text-center">
          <h2 className="mt-5">Hear from Parents and Experts</h2>
        </div>
        <div className="row reviews mt-5">{reviews}</div>
        <div className="row mt-4 review-navigator">
          <img src={arrowLeft} />
          <img src={arrowright} />
        </div>
        <div className="row mt-5 text-center">
          <h2 className="mt-5">Awards & Recognitions</h2>
        </div>
        <div className="row justify-content-md-center mt-4 text-center">
          <div className="col-12 col-md-8">
            <div className="award">
              <img src={frame28} />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
export default Subscription;
