import React, { useState } from "react";
import AuthContainer from "./AuthContainer";
// images
import logo from "icons/auth/logo.png";
import exclIcon from "icons/auth/excl_icon.svg";
import chckIcon from "icons/auth/check_icon.svg";
// components
import FormInput from "components/common/FormInput";
import AuthPricingBlock from "./AuthPricingBlock";

export function Signup() {
  const [isRegistered, setRegistered] = useState(false);
  const [selectedBlock, setSelectedBlock] = useState(1);

  const register = () => {
    setRegistered(true);
  };

  const selectAuthPrice = (e, val) => {
    console.log(val, "ASsaas");
    e.preventDefault();
    setSelectedBlock(val);
  };

  return (
    <AuthContainer isPriceBlockSection={isRegistered}>
      <div className="container">
        {isRegistered ? (
          <div className="row">
            <div className="col-sm-12 col-md-12 col-lg-1">&nbsp;</div>
            <div className="col-sm-12 col-md-12 col-lg-5 auth_align_center">
              <div className="auth_left_container">
                <img src={logo} alt="logo" />
                <div className="auth_left_title">
                  By registering you will also get:
                </div>
                <div className="auth_left_content">
                  <img src={chckIcon} alt="icon checked" />
                  Weekly reports with your kid’s progress
                </div>
                <div className="auth_left_content">
                  <img src={chckIcon} alt="icon checked" />
                  Updates about new game releases
                </div>
                <div className="auth_left_content">
                  <img src={chckIcon} alt="icon checked" />
                  Parenting tips & advice
                </div>
              </div>
            </div>
            <div className="col-sm-12 col-md-12 col-lg-5 auth_align_middle">
              <div className="auth_right_container">
                <div className="auth_right_title">
                  Welcome to <strong>SKIDOS</strong>
                </div>
                <div className="topgap20">
                  <AuthPricingBlock
                    title="Annual Pass"
                    offer="50% OFF $11.99"
                    isSelected={selectedBlock === 1}
                    monthlyPrice="6"
                    annualPrice="72"
                    onClick={(e) => selectAuthPrice(e, 1)}
                  />
                  <AuthPricingBlock
                    title="Quarter Pass"
                    isSelected={selectedBlock === 2}
                    monthlyPrice="8"
                    annualPrice="96"
                    onClick={(e) => selectAuthPrice(e, 2)}
                  />
                  <AuthPricingBlock
                    title="Month Pass"
                    isSelected={selectedBlock === 3}
                    monthlyPrice="9"
                    annualPrice="108"
                    onClick={(e) => selectAuthPrice(e, 3)}
                  />
                </div>
                <button className="skidos-btn-primary topgap40">
                  Continue
                </button>
                <div className="auth_right_content topgap40">
                  Already have an account?{" "}
                  <span className="text-green">Sign in</span>
                </div>
              </div>
            </div>
            <div className="col-sm-12 col-md-12 col-lg-1">&nbsp;</div>
          </div>
        ) : (
          <div className="row">
            <div className="col-sm-12 col-md-12 col-lg-1">&nbsp;</div>
            <div className="col-sm-12 col-md-12 col-lg-5 auth_align_center">
              <div className="auth_left_container">
                <img src={logo} alt="logo" />
                <div className="auth_left_title">Create your account</div>
                <div className="auth_left_content">
                  We need your email address to set up an account <br /> for you
                  & start your free trial.
                </div>
              </div>
            </div>
            <div className="col-sm-12 col-md-12 col-lg-5 auth_align_middle">
              <div className="auth_right_container">
                <div className="auth_right_title">
                  Welcome to <strong>SKIDOS</strong>
                </div>
                <div className="auth_right_content">
                  <img src={exclIcon} alt="exclamation icon" />A temporary
                  password will be sent to your email.
                </div>
                <div className="topgap40">
                  <FormInput
                    type="text"
                    name="email"
                    placeholder="Enter email"
                    label="Email"
                  />
                </div>
                <button
                  className="skidos-btn-primary topgap40"
                  onClick={register}
                >
                  Create Account
                </button>
                <div className="auth_right_content topgap40">
                  Already have an account?{" "}
                  <span className="text-green">Sign in</span>
                </div>
              </div>
            </div>
            <div className="col-sm-12 col-md-12 col-lg-1">&nbsp;</div>
          </div>
        )}
      </div>
    </AuthContainer>
  );
}

export default Signup;
