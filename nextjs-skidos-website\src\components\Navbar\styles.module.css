.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background-color: rgba(255, 255, 255, 1);
  border-bottom: 1px solid #dbdbdb;
  top: 0;
  position: sticky;
  z-index: 100;
  font-family: var(--font-poppins);
  font-weight: 500;
  width: 100%;
  box-sizing: border-box;
}

.logo a {
  color: black;
  text-decoration: none;
  font-size: 1.5rem;
}

.crossImg {
  display: none;
}

.menuIcon {
  display: none;
  cursor: pointer;
}

.menuIcon .bar {
  width: 30px;
  height: 3px;
  background-color: black;
  margin: 4px 0;
  transition: 0.4s;
}

.menuIcon.change .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-5px, 6px);
}

.menuIcon.change .bar:nth-child(2) {
  opacity: 0;
}

.menuIcon.change .bar:nth-child(3) {
  transform: rotate(45deg) translate(-5px, -6px);
}

.navMenu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
  flex-wrap: nowrap;
}

.navItem {
  margin-left: 1rem;
  white-space: nowrap;
}

.navLink {
  color: #6666;
  text-decoration: none;
  padding: 0.5rem 0.5rem;
  display: block;
  position: relative;
  white-space: nowrap;
}

.navLink::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 4px;
  background-color: rgba(146, 88, 254, 1);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.navLink:hover,
.activeLink {
  color: black !important;
}

.activeLink::after {
  width: 100%;
}

.freeTrailBtn {
  font-weight: 600;
  font-size: 1.5rem;
  background-color: rgba(146, 88, 254, 1);
  color: #fff;
  border-radius: 8px;
  box-shadow: 0px 6px 0px rgba(74, 45, 128, 1);
  padding: 0.5rem 1.5rem;
  white-space: nowrap;
}

.freeTrailBtn:hover {
  color: #fff !important;
}

@media (max-width: 820px) {
  .navbar {
    padding: 0.8rem 0.8rem 0.2rem 0.8rem;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .logoImg {
    width: 140px;
    height: 45px;
  }

  .menuIcon {
    display: block;
  }

  .noScroll {
    overflow: hidden;
    height: 100%;
  }

  .navMenu {
    flex-direction: column;
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    right: -150%;
    background-color: #fff;
    align-items: center;
    transition: 0.3s;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 1rem;
    box-sizing: border-box;
  }

  .navMenu.active {
    right: 0;
    z-index: 20;
  }

  .navItem {
    margin: 1rem 0;
    font-size: 1.5rem;
    text-align: center;
  }

  .navLink {
    font-size: 1.25rem;
    padding: 0.5rem;
  }

  .freeTrailBtn {
    font-size: 1.25rem;
    padding: 0.5rem 1.25rem;
    display: inline-block;
    text-align: center;
  }

  .crossImg {
    display: block;
  }
}

@media screen and (max-height: 500px) and (orientation: landscape) {
  .specificPageNavbar {
    display: none !important;
  }
}
