// src/context/PlayerContext.js
import { createContext, useContext, useState } from "react";

const PlayerContext = createContext();

export const usePlayerContext = () => {
  return useContext(PlayerContext);
};

export const PlayerProvider = ({ children }) => {
  const [playerData, setPlayerData] = useState({
    name: "",
    selectedAge: null,
    selectedAvatar: "",
    selectedThemes: [],
    userEmail: "",
  });

  const updatePlayerData = (newData) => {
    setPlayerData((prevData) => ({
      ...prevData,
      ...newData,
    }));
  };

  return (
    <PlayerContext.Provider value={{ playerData, updatePlayerData }}>
      {children}
    </PlayerContext.Provider>
  );
};
