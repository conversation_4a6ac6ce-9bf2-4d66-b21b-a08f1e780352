.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  padding: 2rem;
  width: 90%;
  /* max-width: 90%; */
  max-height: 80%;
  overflow-y: auto;
  position: relative;
  font-family: var(--font-poppins);
}

.modalContent h2 {
  font-size: 2.3rem;
  margin: 1.5rem 0 0 0;
}

.closeButton {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 1001;
}

.openButton {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  cursor: pointer;
}

.modalImageWrapper {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  scrollbar-width: none;
  -webkit-scrollbar-width: none;
}
.popupImgBox {
  /* width: 184px;
    height: 397px; */
  object-fit: fill;
}
.popupImgBox img {
  border-radius: 8px;
}
.gameDescription {
  font-weight: 400;
  font-size: 1.5rem;
  color: rgba(0, 0, 0, 0.5);
  margin: 0;
}

.modalBtnWrappers {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.modalBtnWrappers .storesBtn {
  font-size: 1.2rem;
  width: 48%;
  border-radius: 9px;
  padding: 1rem 0.5rem;
  color: #fff;
  background-color: #1f5dfe;
  text-decoration: none;
  border: none;
  cursor: pointer;
  text-align: center;
}

.modalBtnWrappers .storesBtn:nth-child(2) {
  background-color: #00800d;
}

.noScroll {
  overflow: hidden;
}

@media (max-width: 768px) {
  .modalContent {
    width: 90%;
    border-radius: 0;
    padding: 2rem 0.5rem;
  }

  .closeButton {
    top: 0.1rem;
    right: 0.2rem;
  }
}
