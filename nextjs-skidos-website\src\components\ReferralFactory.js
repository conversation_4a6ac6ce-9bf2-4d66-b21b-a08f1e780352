"use client";

import <PERSON>rip<PERSON> from "next/script";
import { useCallback, useEffect, useState } from "react";

const ReferralFactory = () => {
  const [user, setUser] = useState(null);

  // Function to safely parse JSON from localStorage
  const parseUserDetails = useCallback(() => {
    try {
      const userDetails = JSON.parse(localStorage.getItem("UserDetails"));
      return userDetails?.Email ? userDetails : null;
    } catch (error) {
      console.error("Error parsing UserDetails:", error);
      return null;
    }
  }, []);

  // Function to update user state from localStorage
  const updateUserState = useCallback(() => {
    const userDetails = parseUserDetails();
    setUser(userDetails);
  }, [parseUserDetails]);

  useEffect(() => {
    updateUserState();

    // Create MutationObserver to watch for localStorage changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          updateUserState();
        }
      });
    });

    // Handle storage changes (cross-tab)
    const handleStorageChange = (event) => {
      if (event.key === "UserDetails") {
        updateUserState();
      }
    };

    // Watch for localStorage changes in the current tab
    const originalSetItem = localStorage.setItem;
    const originalRemoveItem = localStorage.removeItem;
    const originalClear = localStorage.clear;

    localStorage.setItem = function (...args) {
      const [key] = args;
      originalSetItem.apply(this, args);
      if (key === "UserDetails") {
        updateUserState();
      }
    };

    localStorage.removeItem = function (key) {
      originalRemoveItem.apply(this, [key]);
      if (key === "UserDetails") {
        updateUserState();
      }
    };

    localStorage.clear = function () {
      originalClear.apply(this);
      updateUserState();
    };

    // Set up event listeners
    window.addEventListener("storage", handleStorageChange);

    // Additional event listener for manual updates
    const handleManualUpdate = () => {
      updateUserState();
    };
    window.addEventListener("userDetailsUpdate", handleManualUpdate);

    // Cleanup
    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("userDetailsUpdate", handleManualUpdate);
      localStorage.setItem = originalSetItem;
      localStorage.removeItem = originalRemoveItem;
      localStorage.clear = originalClear;
    };
  }, [updateUserState]);

  // Only render scripts when user exists
  if (!user) {
    return null;
  }

  return (
    <>
      <Script
        id="rf-user-data"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.RF = {
              user: {
                first_name: ${JSON.stringify(user.first_name || user.Email || "")},
                email: ${JSON.stringify(user.Email || "")}
              }
            };
          `,
        }}
      />
      <Script
        id="rf-script"
        strategy="afterInteractive"
        src={`https://referral-factory.com/assets/js/widget.js?code=${process.env.NEXT_PUBLIC_REFERRAL_FACTORY || "cAMCScxx"}`}
      />
    </>
  );
};

export default ReferralFactory;
