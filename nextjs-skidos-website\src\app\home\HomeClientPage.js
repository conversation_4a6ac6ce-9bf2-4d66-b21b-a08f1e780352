"use client";
import { LandingReview } from "@/constants";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { gsap } from "gsap";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Carousel from "../../components/CarouselHome";
import HomeThemeCarouselCt from "./HomeThemeCarouselCt";
import styles from "./styles.module.css";

gsap.registerPlugin(ScrollToPlugin);
gsap.registerPlugin(ScrollTrigger);

const HomeClientPage = () => {
  const t = useTranslations("HomePage");

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <div className={styles.landingPageWrapper}>
        <Carousel />
        <div className={styles.nurturingGrowthWrapper}>
          <h2 className={styles.homeSubheaders} ref={(el) => setRef(el)}>
            {t("nurturingGrowth.title.part1")}{" "}
            <span className={styles.homeHighlightSubheader}>
              {t("nurturingGrowth.title.highlight")}{" "}
              <Image
                src="/images/homeArrow.png"
                width={60}
                height={60}
                className={styles.homeHighlightSubheaderImg}
                alt="Arrow"
              />
            </span>{" "}
            {t("nurturingGrowth.title.part2")}
            <br />
            {t("nurturingGrowth.title.part3")}
          </h2>
          <div className={styles.nurturingGrowthCardsWrapper}>
            <div className={styles.nurturingGrowthCards}>
              <Image
                src="/images/homeNurturing1.png"
                width={320}
                height={291}
                alt={t("nurturingGrowth.cards.card1")}
              />
              <p className={styles.nurturingGrowthCardCont}>{t("nurturingGrowth.cards.card1")}</p>
            </div>
            <div className={styles.nurturingGrowthCards}>
              <Image
                src="/images/homeNurturing2.png"
                alt={t("nurturingGrowth.cards.card2")}
                width={320}
                height={291}
              />
              <p className={styles.nurturingGrowthCardCont}>{t("nurturingGrowth.cards.card2")}</p>
            </div>
            <div className={styles.nurturingGrowthCards}>
              <Image
                src="/images/homeNurturing3.png"
                width={320}
                height={291}
                alt={t("nurturingGrowth.cards.card3")}
              />
              <p className={styles.nurturingGrowthCardCont}>{t("nurturingGrowth.cards.card3")}</p>
            </div>
          </div>
        </div>
        <div ref={(el) => setRef(el)}>
          <h2 className={styles.homeSubheaders}>
            {t("discoverThemes.title.part1")} <br />
            <span className={styles.homeHighlightSubheader}>
              {t("discoverThemes.title.highlight1")}{" "}
              <Image
                src="/images/homeHat.png"
                alt="Hat"
                width={60}
                height={60}
                className={styles.homeHighlightSubheaderImg}
              />
            </span>{" "}
            {t("discoverThemes.title.and")}{" "}
            <span className={styles.homeHighlightSubheader}>
              {t("discoverThemes.title.highlight2")}{" "}
              <Image
                src="/images/homeRemote.png"
                alt="Remote"
                width={60}
                height={60}
                className={styles.homeHighlightSubheaderImg}
              />
            </span>{" "}
          </h2>
          <HomeThemeCarouselCt />

          <div className={styles.discoverPurpleBgWrapper}>
            <h2 className={`${styles.homeSubheaders} ${styles.whiteText}`} ref={(el) => setRef(el)}>
              {t("reviews.title")}
            </h2>
            <div className={styles.reviewWrapper}>
              {LandingReview.map((item, index) => (
                <div key={index} className={styles.reviewCard} ref={(el) => setRef(el)}>
                  <div>
                    <Image src={item.starImg} width={152} height={24} alt="Rating Stars" />
                    <p>{item.content}</p>
                  </div>
                  <div className={styles.reviewCardFooter}>
                    <p>{item.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className={styles.socialProofWrapper}>
              <div className={styles.socialProofFirstRowWrapper} ref={(el) => setRef(el)}>
                <div className={styles.socialProofText}>{t("socialProof.impactMade")}</div>
                <div className={styles.socialProofSkills}>
                  <p>{t("socialProof.problemsSolved")}</p>
                  <div className={styles.imageWrapper}>
                    <Image
                      alt="Social Proof Learners"
                      src="/images/socialProofSkiils.webp"
                      layout="fill"
                      objectFit="contain"
                    />
                  </div>
                </div>
              </div>
              <div className={styles.socialProofSecondRowWrapper}>
                <div className={styles.happyLearnersWrapper}>
                  <div className={styles.imageWrapper}>
                    <Image
                      alt="Social Proof Learners"
                      src="/images/socialProofLearners.webp"
                      layout="fill"
                      objectFit="contain"
                    />
                  </div>
                  <div>
                    <p>
                      {t("socialProof.happyLearners.title")}
                      <br />
                      {t("socialProof.happyLearners.subtitle")}
                    </p>
                  </div>
                </div>
                <div className={styles.happyLearnersWrapper}>
                  <div className={styles.imageWrapper}>
                    <Image
                      alt="Social Proof Rating"
                      src="/images/socialProofRating.png"
                      layout="fill"
                      objectFit="contain"
                    />
                  </div>
                  <div>
                    <p>
                      {t("socialProof.rating.title")}
                      <br />
                      <Image
                        src="/images/4.5_star_white.png"
                        width={100}
                        height={20}
                        alt="Rating Stars"
                      />
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.awardsWrapper}>
              <div ref={(el) => setRef(el)}>
                <h2>{t("awards.title")}</h2>
              </div>
              <div className={styles.awardImgWrapper}>
                <Image
                  src="/images/Award1.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className={styles.awardImg}
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award2.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className={styles.awardImg}
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award3.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className={styles.awardImg}
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award4.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className={styles.awardImg}
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award5.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className={styles.awardImg}
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award6.webp"
                  height={120}
                  width={120}
                  alt="Award"
                  className={styles.awardImg}
                  ref={(el) => setRef(el)}
                />
                <Image
                  src="/images/Award7.png"
                  height={120}
                  width={120}
                  alt="Award"
                  className={styles.awardImg}
                  ref={(el) => setRef(el)}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default HomeClientPage;
