"use client";

import EmailOtpInput from "@/components/EmailOtpInput";
import FormCtaButton from "@/components/FormCtaButton";
import Stepper from "@/components/Stepper";
import { useAuth } from "@/context/AuthContext";
import useTogglePinkFooter from "@/hooks/useTogglePinkFooter";
import apiClient from "@/utils/axiosUtil";
import { getLocale } from "@/utils/helperFunctions";
import { webEngagelogin } from "@/utils/webengage";
import { faCheck } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import styles from "./styles.module.css";

const OTPLoginPage = () => {
  const router = useRouter();
  const [state, setState] = useState({
    step: 1,
    email: "",
    otp: "",
    loading: false,
    error: "",
    serverError: false,
  });
  const [resendState, setResendState] = useState({
    timer: 30,
    status: "waiting", // "waiting", "active", "resent"
  });
  const { login } = useAuth();
  const locale = useLocale();
  const t = useTranslations("LoginOtp");
  const lang = getLocale(locale);

  useEffect(() => {
    let interval;
    if (resendState.timer > 0 && resendState.status === "waiting") {
      interval = setInterval(() => {
        setResendState((prev) => ({ ...prev, timer: prev.timer - 1 }));
      }, 1000);
    } else if (resendState.timer === 0 && resendState.status === "waiting") {
      setResendState((prev) => ({ ...prev, status: "active" }));
    }
    return () => clearInterval(interval);
  }, [resendState.timer, resendState.status]);

  const validateEmail = useCallback(
    (email) => {
      if (!email) return t("EmailIncorrect");
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) return t("EmailIncorrect");
      return "";
    },
    [t]
  );

  const validateOtp = useCallback(
    (otp) => {
      if (otp.length !== 4 || otp.split("").some((char) => isNaN(char) || char === "")) {
        return t("OtpError");
      }
      return "";
    },
    [t]
  );

  const handleApiRequest = useCallback(
    async (url, body, successMessage, isLoadingEnabled = true) => {
      try {
        setState((prev) => ({
          ...prev,
          loading: isLoadingEnabled,
          error: "",
          serverError: false,
        }));

        const response = await apiClient.post(`${url}`, body);

        if (response) {
          return response.data;
        } else {
          setState((prev) => ({
            ...prev,
            loading: false,
            error: response?.data?.Message ?? t("OperationFailed"),
            serverError: true,
          }));
          return null;
        }
      } catch (error) {
        setState((prev) => ({
          ...prev,
          loading: false,
          error: error.response?.data?.Message || t("ErrorOccurred"),
          serverError: true,
        }));
        return null;
      }
    },
    [t]
  );

  const handleGenerateOtp = useCallback(
    async (e) => {
      e.preventDefault();
      const emailError = validateEmail(state.email);

      if (emailError) {
        setState((prev) => ({ ...prev, error: emailError }));
        return;
      }

      const data = await handleApiRequest(
        `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/login/generate_otp?l=${lang}&gameid=hungrycat&version=8.0&platform=iOS`,
        { gameid: "hungrycat", email: state.email },
        state.otp ? t("OtpRegenerated") : t("OtpSent")
      );

      if (data) {
        setState((prev) => ({ ...prev, step: 2, loading: false }));
        setResendState({ timer: 30, status: "waiting" });
      }
    },
    [state.email, state.otp, handleApiRequest, validateEmail, lang, t]
  );

  const handleVerifyOtp = useCallback(
    async (e) => {
      e.preventDefault();
      const otpError = validateOtp(state.otp);

      if (otpError) {
        setState((prev) => ({ ...prev, error: otpError }));
        return;
      }

      const data = await handleApiRequest(
        `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/login/verify_otp?l=${lang}&gameid=hungrycat&version=8.0&platform=iOS`,
        { gameid: "hungrycat", email: state.email, otp: state.otp },
        t("OtpVerified")
      );

      if (data) {
        const auth_token = localStorage.getItem("auth_token");
        document.cookie = `token=${auth_token}; Path=/; Secure; SameSite=Strict; Max-Age=${process.env.NEXT_PUBLIC_COOKIE_EXPIRY}`;
        localStorage.setItem(
          "UserDetails",
          JSON.stringify({
            email: data.Email,
            expiryDate: data.SubscriptionExpire,
            isSubscribed: data.isSubscribed,
            ...data,
          })
        );
        login(data.isSubscribed);
        webEngagelogin({ email: data.Email, userId: data.Id });
        if (data.isSubscribed) {
          router.push("/user-home-screen");
        } else {
          router.push("/profile");
        }
      }
    },
    [state.email, state.otp, validateOtp, router, handleApiRequest, login, lang, t]
  );

  const handleResendOtp = useCallback(async () => {
    if (resendState.status !== "active") return;

    const data = await handleApiRequest(
      `${process.env.NEXT_PUBLIC_USERSERVICE_BASE_URL}/login/generate_otp?l=${lang}&gameid=hungrycat&version=8.0&platform=iOS`,
      { gameid: "hungrycat", email: state.email },
      t("OtpResent"),
      false
    );

    if (data) {
      setResendState({ timer: 30, status: "resent" });
      setTimeout(() => setResendState({ timer: 30, status: "waiting" }), 3000); // Reset after 3 seconds
    }
  }, [resendState.status, state.email, handleApiRequest, lang, t]);

  const handleBack = useCallback(() => {
    if (state.step === 2) {
      setState((prev) => ({ ...prev, step: 1, error: "" }));
    } else {
      router.back();
    }
  }, [state.step, router]);

  useTogglePinkFooter(false);

  return (
    <div className={styles.loginContainer}>
      <div className={styles.container}>
        <div style={{ width: "100%" }}>
          <Stepper
            currentStep={state.step === 1 ? 3 : 4}
            handleBack={handleBack}
            steps={[1, 2, 3, 4]}
          />
        </div>
        <h1 className={styles.title}>{state.step === 1 ? t("Heading") : t("HeadingOtp")}</h1>
        {state.step === 1 ? (
          <p className={styles.helpText}>{t("Subheading")}</p>
        ) : (
          <p className={styles.helpText}>
            {t("SubheadingOtp")} {state.email}
          </p>
        )}
        <form
          className={styles.form}
          onSubmit={state.step === 1 ? handleGenerateOtp : handleVerifyOtp}
        >
          <div className={styles.formContainer}>
            {state.step === 1 ? (
              <div className={styles.inputWrapper}>
                <input
                  type="email"
                  placeholder={t("InputPlaceholder")}
                  className={`${styles.input} ${state.error ? styles.formInputError : ""}`}
                  value={state.email}
                  onChange={(e) =>
                    setState((prev) => ({ ...prev, email: e.target.value, error: "" }))
                  }
                />
              </div>
            ) : (
              <EmailOtpInput
                error={state.error}
                value={state.otp}
                onOtpChange={(value) => setState((prev) => ({ ...prev, otp: value, error: "" }))}
              />
            )}

            {state.error && (
              <div className={styles.errorMessage}>
                <Image src="/images/webGl/warning.png" height={20} width={20} alt="Warning" />
                {state.error}
              </div>
            )}

            {state.step === 2 && (
              <div className={styles.resendContainer}>
                {resendState.status === "waiting" && (
                  <button className={styles.resendBtn} disabled>
                    {t("ResendTimer", { timer: resendState.timer })}
                  </button>
                )}
                {resendState.status === "active" && (
                  <button type="button" className={styles.resendBtn} onClick={handleResendOtp}>
                    {t("Resend")}
                  </button>
                )}
                {resendState.status === "resent" && (
                  <button className={`${styles.resendBtn} ${styles.resent}`}>
                    {t("Resent")} <FontAwesomeIcon icon={faCheck} />
                  </button>
                )}
              </div>
            )}
          </div>
          <Image
            src="/images/webGl/EmailId/activeGraphic.png"
            className={styles.BackgroundImg}
            width={300}
            height={400}
            alt="BackgroundImg"
          />

          {/* 24vh */}
          <FormCtaButton
            text={state.step === 1 ? t("CtaBtnGetCode") : t("CtaLogin")}
            loading={state.loading}
            customStyles={
              state.step === 1
                ? state.error
                  ? styles.submitButtonEmailError
                  : styles.submitButtonEmail
                : state.error
                  ? styles.submitButtonOtpError
                  : styles.submitButtonOtp
            }
          />
        </form>
      </div>
    </div>
  );
};

export default OTPLoginPage;
