.left_tree {
  position: absolute;
  left: 0;
  top: 0;
}
.right_tree {
  position: absolute;
  top: 0;
  right: 0;
}
.green_layer {
  position: absolute;
  top: 85%;
  left: 0;
  width: 100%;
}
.detail_purple_layer {
  position: relative;
  min-height: 60vh;
  background-color: #2d6854;
}
.detail_purple_layer .detail_purple_bg {
  background-image: url(../../icons/detail_purple_layer.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 0 0;
  min-height: 100vh;
  width: 100%;
  top: -150%;
  left: 0;
  position: absolute;
  z-index: 1;
}
.detail_screen2 {
  position: relative;
  min-height: 80vh;
  background-color: #2d6854;
}
.detail_screen2 .detail_green_bg {
  background-image: url(../../icons/detail_green_layer.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 0 0;
  min-height: 80vh;
  width: 100%;
  top: -100%;
  left: 0;
  position: absolute;
  z-index: 2;
}
.go_back_btn {
  top: 7%;
  right: 0px;
  position: absolute;
}
.video_type_container {
  background: rgb(0 141 131 / 80%);
  background-blend-mode: multiply;
  backdrop-filter: blur(10px);
  padding: 20px 20px 60px;
  line-height: 1.8em;
  color: #fff;
  font-size: 1em;
  font-weight: 500;
  text-align: center;
  border-radius: 20px;
  margin-top: 100px;
}
.detail_content_aligner {
  padding: 0 4.6em;
}
.game_type_box {
  display: flex;
  flex-direction: row;
  column-gap: 20px;
  margin-top: -20px;
}
.game_type_tile {
  background: #b24729;
  border-radius: 15px;
  padding: 0 1em;
  font-size: 1.3em;
  font-weight: 700;
  color: #fff;
  box-shadow: 0px 3px 0px #4e0401;
  display: flex;
  flex: 1;
  align-items: center;
  cursor: pointer;
}

/* game container css starts*/
.game_box_outer {
  background: #b24729;
  border-radius: 25px;
  width: 100%;
  margin-top: 50%;
  transform: translateY(-50%);
  position: relative;
  box-shadow: 0 3px 0px #4e0401;
  padding: 25px;
  z-index: 3;
}
.game_box_name {
  font-size: 2em;
  display: inline-block;
  position: absolute;
  left: 0.5em;
  top: -0.75em;
  color: #fff;
  font-weight: 700;
  text-shadow: -1px 0px 0px #000, 1px 0px 0px #000, 0px -1px 0px #000,
    0px 1px 0px #000;
}
.video_box_inner {
  background: #000;
  padding: 0;
  border-radius: 25px;
  max-width: 830px;
  height: 465px;
  overflow: hidden;
}
.video_box_inner iframe {
  height: 100%;
  border-radius: 25px;
}
.game_frame_top {
  position: absolute;
  left: -100%;
  top: -30%;
  z-index: 2;
  height: 16px;
  width: 100%;
  background-color: #efc286;
}
.game_frame_bottom {
  position: absolute;
  left: -100%;
  top: 20%;
  z-index: 2;
  height: 16px;
  width: 100%;
  background-color: #efc286;
}
/* game container css ends*/

/* game modal css starts */
.game_modal {
  display: block;
}
.game_modal .modal-content {
  border: 3px solid #000000;
  box-shadow: 0px 4px 0px #000000;
  border-radius: 23px;
}
.game_modal .modal-content {
  padding: 40px;
}
.game_modal .modal-body {
  padding-top: 0;
}
.modal_close {
  position: absolute;
  top: 20px;
  right: 20px;
}
.modal_fiks {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -75px;
  z-index: -1;
}
.modal_moyo {
  position: absolute;
  left: -130px;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
}
.modal_neta {
  position: absolute;
  right: -85px;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
}
.modal_game_title {
  font-weight: 700;
  font-family: "Quicksand";
  font-size: 24px;
  line-height: 30px;
  text-align: center;
  color: #000;
}
.modal_game_body {
  font-family: "Quicksand";
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  text-align: center;
  color: #000;
}
.modal_input {
  background: #ededed;
  border-radius: 12px;
  border: none;
  outline: none;
  width: 100%;
  margin-bottom: 20px;
  padding: 12px;
}
@media (max-width: 960px) {
  .game_frame_top,
  .game_frame_bottom {
    left: -50%;
  }
  .detail_purple_layer .detail_purple_bg {
    top: -100%;
  }
  .detail_screen2 .detail_green_bg {
    top: -50%;
  }
  .game_box_outer {
    margin-top: 70%;
  }
  .go_back_btn {
    top: 5%;
    right: -200%;
    width: 200px;
  }
  .detail_purple_layer {
    min-height: 30vh;
  }
  .detail_screen2 {
    min-height: 70vh;
  }
  .video_box_inner {
    height: 310px;
  }
}

@media only screen and (max-width: 700px) {
  .detail_content_aligner {
    padding: 0 10px;
  }
  .game_box_outer {
    margin-top: 100%;
  }
  .go_back_btn {
    top: 30px;
    right: 45%;
    width: 200px;
  }
  .right_tree,
  .left_tree {
    max-width: 100%;
  }
}

@media only screen and (max-width: 450px) {
  .video_box_inner {
    height: 205px;
  }
  .game_box_outer {
    margin-top: 135%;
  }
  .go_back_btn {
    top: 125px;
  }
}

/* ipad pro */
@media only screen and (min-width: 1024px) and (min-height: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) {
  .detail_purple_layer {
    min-height: 45vh;
  }
  .detail_screen2 {
    min-height: 50vh;
  }
}
/* iPad */
@media all and (device-width: 1024px) and (device-height: 768px) and (orientation: landscape) {
  .game_box_outer {
    margin-top: 60%;
  }
  .video_box_inner {
    height: 330px;
  }
  .detail_content_aligner {
    padding: 0;
  }
}
/* Mobile only iphone 6/7/8 */
@media only screen and (max-width: 375px) and (orientation: portrait) {
  .game_title_container {
    margin-top: 20%;
    position: relative;
    z-index: 4;
  }
}
/* laptop 16inch */
@media only screen and (min-width: 1350px) and (min-height: 610px) {
  .detail_screen2 {
    min-height: 95vh;
  }
}
/* macbook 13.9inch */
@media only screen and (min-width: 1420px) and (min-height: 808px) {
  .detail_screen2 {
    min-height: 75vh;
  }
  .detail_purple_layer .detail_purple_bg {
    top: -100%;
  }
}
