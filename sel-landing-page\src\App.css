body {
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
::-webkit-input-placeholder {
  /* WebKit, Blink, Edge */
  color: #fff;
}
:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #fff;
  opacity: 1;
}
::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #fff;
  opacity: 1;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #fff;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #fff;
}

::placeholder {
  /* Most modern browsers support this now. */
  color: #fff;
}
/* common css for home and detail page */
@keyframes animatedBackground {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -300px 0;
  }
}
@-moz-keyframes animatedBackground {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -300px 0;
  }
}
@-webkit-keyframes animatedBackground {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -300px 0;
  }
}
@-ms-keyframes animatedBackground {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -300px 0;
  }
}
@-o-keyframes animatedBackground {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -300px 0;
  }
}

.screen1_bg {
  width: 100%;
  min-height: 1000px;
  background-image: url(./icons/sky_bg.jpg);
  background-position: 0px 0px;
  position: relative;
  background-size: cover;
}
.bg_clouds {
  width: 100%;
  min-height: 1000px;
  background-image: url(./icons/clouds.png);
  background-position: 0px 0px;
  position: relative;
  background-size: contain;

  animation: animatedBackground 30s linear infinite;
  -moz-animation: animatedBackground 30s linear infinite;
  -webkit-animation: animatedBackground 30s linear infinite;
  -ms-animation: animatedBackground 30s linear infinite;
  -o-animation: animatedBackground 30s linear infinite;
}
nav {
  z-index: 9;
}
.btn-white {
  background: #ffffff;
  border-radius: 22.5px;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0.7em 1.4em;
  border: none;
  font-weight: 700;
  cursor: pointer;
  font-size: 1em;
}
.btn-orange {
  background: #f66007;
  border-radius: 15px;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0.7em 1.4em;
  border: none;
  font-weight: 700;
  cursor: pointer;
  font-size: 1em;
  box-shadow: 0px 5px 0px #b54608;
  color: #fff;
}
.btn-orange:active {
  box-shadow: 0px 3px 0px #b54608;
}
.align_center {
  margin-top: 50%;
  margin-right: 50%;
  transform: translate(-50%, -50%);
}
.text-right {
  text-align: right;
}
.relative {
  position: relative;
}
.padding-none {
  padding: 0;
}
.topgap7 {
  margin-top: 7px;
}
.topgap10 {
  margin-top: 10px;
}
.topgap15 {
  margin-top: 15px;
}
.topgap20 {
  margin-top: 20px;
}
.topgap30 {
  margin-top: 30px;
}
.topgap40 {
  margin-top: 40px;
}
.topgap50 {
  margin-top: 50px;
}
.rightgap12 {
  margin-right: 12px;
}
.rightgap20 {
  margin-right: 20px;
}
.leftgap20 {
  margin-left: 20px;
}
.bottomgap20 {
  margin-bottom: 20px;
}
.overlap_zindex {
  z-index: 4;
}
.opacity_zero {
  opacity: 0;
}
.opacity_one {
  opacity: 1;
}
.reverse {
  transform: scaleX(-1);
}
.text-green {
  color: #00c6a3;
}
.padding-left-none {
  padding-left: 0;
}
.not-visible {
  visibility: hidden;
}
.visible-sm {
  display: none;
}
.visible-xs {
  display: none;
}
.hidden {
  display: none;
}
.full-width {
  width: 100%;
}
/* Navbar */
.header-navbar .dropdown-toggle::after {
  content: "";
  border: solid #333;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  margin-left: 12px;
  margin-top: 8px;
  position: absolute;
}
.header-navbar .dropdown:hover .dropdown-toggle::after,
.header-navbar .dropdown-toggle.show:after {
  border: solid #b6eee3;
  border-width: 0 2px 2px 0;
}
.header-navbar {
  padding: 15px 15px 10px 0;
  background-color: #fff;
}
.header-navbar .nav-item.dropdown {
  margin-right: 20px;
}
.header-navbar .nav-item.dropdown .dropdown-menu,
.header-navbar .nav-item.dropdown .dropdown-menu.show {
  background-color: #b6eee3;
  border: none;
  font-size: 1.05rem;
  font-weight: 700;
  padding: 20px;
  border-radius: 20px;
  box-shadow: 0 6px 28px rgba(0, 0, 0, 0.08);
  display: none;
}
.header-navbar .nav-item.dropdown:hover .dropdown-menu {
  opacity: 1;
}
.header-navbar .dropdown-menu .dropdown-item {
  font-weight: 600;
  border-radius: 15px;
  padding: 8px 45px 11px 15px;
  opacity: 0;
  margin-top: 15px;
  font-size: 1.05rem;
}
.header-navbar .dropdown-menu .dropdown-item:hover {
  background-color: #fff;
}
.header-navbar .nav-item.dropdown:hover .dropdown-menu,
.header-navbar .nav-item.dropdown .dropdown-menu.show {
  display: block;
}
.header-navbar .nav-item.dropdown:hover .dropdown-menu .dropdown-item,
.header-navbar .nav-item.dropdown .dropdown-menu.show .dropdown-item {
  opacity: 1;
  transition: 0.5s;
  transform: translateY(-10%);
}
.header-navbar .dropdown-menu::after {
  position: absolute;
  left: 15%;
  transform: translateX(-50%);
  top: -10px;
  width: 0;
  height: 0;
  content: "";
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #b6eee3;
}
.navbar .nav-link {
  font-weight: 600;
  color: #000;
  font-size: 1.1rem;
  margin: 0 10px;
}
.header-navbar .logo {
  height: 40px;
}
.navbar .try-btn {
  padding: 3px 20px;
  background-color: #00c6a3;
  border-radius: 12px;
  margin: auto 15px;
  line-height: 1;
  cursor: pointer;
}
.navbar .try-btn a {
  color: #fff;
  margin: auto;
}
.header-navbar .avatar {
  width: 64px;
  position: absolute;
  left: -60px;
  top: -10px;
}
.header-navbar .profile-section {
  margin-left: 75px;
}
.header-navbar .profile-section .dropdown-menu {
  margin-left: -75px;
  margin-top: 0px;
  width: 260px;
}
.header-navbar .upgrade-btn {
  padding: 08px 25px;
  background-color: #fdd12a;
  background-image: url(./icons/upgrade-btn-bg.png);
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
  width: 90%;
  font-size: 1.3rem;
  border-radius: 10px;
  cursor: pointer;
}
.header-navbar .upgrade-btn a {
  line-height: 0.5;
}
.header-navbar .profile-section .player-profile {
  margin-bottom: 50px;
}
.header-navbar .profile-section .profile-list {
  margin: 15px 0;
  text-decoration: none;
  color: #000;
}
.header-navbar .profile-section .profile-list img {
  width: 70px;
}
.header-navbar .profile-section .profile-list .player-name {
  font-size: 1.02rem;
  padding-left: 10px;
  font-weight: 600;
}
.header-navbar .profile-section .profile-list .new-player {
  cursor: pointer;
}
.header-navbar .profile-section .profile-list .new-player:after {
  content: "+";
  font-size: 1.95rem;
  position: absolute;
  margin-left: 15px;
  background: #333;
  width: 45px;
  height: 45px;
  line-height: 1;
  text-align: center;
  color: #fff;
  border-radius: 50%;
  border: 6px solid #fff;
}
.header-navbar .profile-section .profile-list .add-new {
  margin-left: 85px;
  font-size: 1.02rem;
  line-height: 2.5;
  font-weight: 600;
  cursor: pointer;
}
.header-navbar .profile-section .pages {
  font-size: 0.75rem;
  color: #3a7c64;
  margin-bottom: 10px;
  text-transform: uppercase;
}
.header-navbar .profile-section .nav-block {
  padding: 20px 25px 25px 55px;
}
.header-navbar .profile-section .nav-block h3 {
  font-weight: 700;
  color: #000;
  line-height: 0.75;
}
.header-navbar .profile-section .navbar-nav {
  flex-direction: column;
}
.header-navbar .profile-section .navbar-nav .nav-item {
  padding: 5px;
  border-radius: 15px;
}
.header-navbar .profile-section .navbar-nav .nav-item:hover {
  background-color: #fff;
}
.header-navbar .profile-section .navbar-nav .nav-item a {
  font-size: 1.02rem;
  cursor: pointer;
}
.header-navbar .player-dropdown {
  width: 150px;
}
/* DetailLeftNav styles starts*/
.detail_list_container {
  background: rgb(0 141 131 / 80%);
  border-radius: 20px;
  padding: 20px;
  max-height: 288px;
  overflow-y: scroll;
}
.detail_list_container .list_item {
  color: #ffffff;
  font-weight: 400;
  font-size: 1.1em;
  padding: 12px;
}
.detail_list_container .list_item_active {
  color: #000;
  background: #fbd02b;
  border-radius: 10px;
  font-weight: 600;
}
/* ===== Scrollbar CSS ===== */
/* Firefox */
* {
  scrollbar-width: auto;
  scrollbar-color: #1414148e #ffffff00;
}

/* Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 08px;
}

*::-webkit-scrollbar-track {
  background: #ffffff00;
}

*::-webkit-scrollbar-thumb {
  background-color: #1414148e;
  border-radius: 11px;
  border: 3px solid #ffffff00;
}
/* GameTileBox Styles Starts */
.game_tile_row {
  height: 290px;
  overflow: hidden;
  padding-top: 7px;
  padding-bottom: 7px;
  padding-right: 10px;
}
.scroll_tile_box {
  overflow-y: overlay;
  scroll-behavior: smooth;
}
.game_tile {
  background-color: #fff;
  border-radius: 30px;
  height: 125px;
  border: 3px solid #000;
  box-shadow: 0px 3px 0px #000;
  background-size: cover;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}
.game_tile:hover {
  transform: scale(1.1);
}
.game_tile_title {
  display: inline-block;
  background-color: #fff;
  font-size: 0.8em;
  color: #000;
  font-weight: 600;
  padding: 4px 10px;
  min-width: 85%;
  border-radius: 30px;
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

/* GameTypeNav style start */
.game_type_list_container .list_item {
  margin-bottom: 10px;
  cursor: pointer;
}
.gtlc_nav_icon {
  display: inline-block;
  position: relative;
}
.gtlc_nav_icon .nav_img {
  position: absolute;
  z-index: 3;
  width: 36px;
  height: auto;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.gtlc_nav_icon .nav_yellow_bg {
  position: absolute;
  z-index: 2;
  width: 96%;
  height: auto;
  top: 2px;
  left: 2px;
}
.game_type_list_container .gtlc_nav_item {
  font-size: 1em;
  color: #000;
  font-weight: 700;
  padding: 16px 26px;
  margin-left: -12px;
  position: relative;
  border-radius: 12px;
  width: 72%;
  display: inline-block;
}
.game_type_list_container .list_item_active .gtlc_nav_item {
  font-size: 1em;
  color: #fff;
  font-weight: 700;
  padding: 16px 26px;
  margin-left: -12px;
  position: relative;
  border-radius: 12px;
  width: 72%;
}
.game_type_list_container .list_item_active .gtlc_nav_item .nav_item_bg {
  background: rgb(0 141 131 / 80%);
  background-blend-mode: multiply;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 0 12px 12px 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: #fff;
  clip-path: polygon(
    0% 0,
    100% 0,
    90% 100%,
    90% 100%,
    90% 10%,
    90% 100%,
    0% 100%
  );
}
.game_type_list_container .list_item_active .gtlc_nav_item .nav_item_bg::after {
  content: "";
  background: rgb(0 141 131 / 80%);
  background-blend-mode: multiply;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  clip-path: inset(1% 1% 1% 1% round 16px);
  position: absolute;
  width: 100%;
  height: 100%;
}
.list_item_active .gtlc_nav_icon .nav_brown_bg {
  transform: rotate(-15deg);
  transition: all 0.5s linear;
}
.list_item_active .gtlc_nav_icon .nav_yellow_bg {
  position: absolute;
  z-index: 2;
  width: 96%;
  height: auto;
  top: -4px;
  left: -1px;
  transform: rotate(-15deg);
  transition: all 0.5s linear;
}
.list_item_active .gtlc_nav_icon .nav_img {
  position: absolute;
  z-index: 3;
  width: 36px;
  height: auto;
  top: 44%;
  left: 48%;
  transform: translate(-50%, -50%) rotate(15deg);
  transition: all 0.5s linear;
}

/* BeforeAnimation style start */
.before_anim_container {
  position: fixed;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  display: flex;
  z-index: 99;
}
.before_anim_container .tree_box {
  position: relative;
  display: flex;
  flex: 1;
}
.ba_top_tree {
  position: absolute;
  top: -50%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 101;
}
.ba_left_tree {
  position: absolute;
  left: -50%;
  top: 0;
  z-index: 102;
  width: 40%;
}
.ba_right_tree {
  position: absolute;
  right: -50%;
  top: 0;
  z-index: 102;
  width: 40%;
}
.ba_bottom_tree {
  position: absolute;
  bottom: -50%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  width: 60%;
}
/* css loader */
.loader-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  background-color: #fff;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loader {
  width: 75px;
  height: 75px;
  border: 3px solid #fdd12a;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
.loader::after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 65px;
  height: 65px;
  border-radius: 50%;
  border: 3px solid;
  border-color: #0c8c84 transparent;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* common form input style */
.form_input_label {
  font: normal normal bold 14px/18px Quicksand;
  color: #ffffff;
  margin-left: 30px;
  text-align: left;
  display: inline-block;
}
.form_input_styled {
  border-radius: 16px;
  border: 1px solid #fff;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 20px 30px;
  font-size: 16px;
  color: #fff;
  width: 100%;
  margin-top: 10px;
  outline: none;
}
.skidos-btn-primary {
  background-color: #f66007;
  border-radius: 16px;
  padding: 22px;
  text-align: center;
  color: #fff;
  font: normal normal bold 21px/26px Quicksand;
  width: 100%;
  border: none;
  border-bottom: 2px solid #b54608;
}
/* GameMobileNav style */
.game_mobile_nav {
  background: #fdd12a;
  border-radius: 16px;
  padding: 22px 20px;
  color: #000;
  margin-top: 20px;
}
.game_mobile_nav .left_block {
  font-weight: 700;
  font-size: 18px;
}
.game_mobile_nav .right_block {
  text-align: right;
}
.game_mobile_nav .right_block select {
  cursor: pointer;
  background-color: transparent;
  border: none;
  line-height: initial;
  outline: none;
  font-weight: 700;
  font-size: 18px;
  padding: 0;
}
.game_mobile_nav .right_block select:focus {
  border: none;
  outline: none;
  box-shadow: none;
}

/* footer */
.footer {
  height: 200px;
  background-color: #9ee8da;
  position: relative;
  z-index: 9;
  margin-top: 520px;
}
.footer:after {
  content: "";
  background-image: url(https://skidos.com/wp-content/uploads/2021/12/Blob_footer.png);
  background-position: top center !important;
  padding-top: 300px !important;
  background-size: cover;
  padding-bottom: 20px;
  background-repeat: no-repeat;
  position: absolute;
  width: 100%;
  margin-top: -375px;
  z-index: 9;
}
.footer .mascot-group {
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);
  top: -450px;
  z-index: 99;
}
.footer-nav {
  position: absolute;
  z-index: 99;
  padding: 0 125px;
  width: 100%;
  margin-top: -125px;
  margin-left: 0;
  background-color: #9ee8da;
  left: 0;
}
.footer-second {
  position: absolute;
  width: 100%;
  z-index: 99;
  bottom: 0;
  padding-top: 15px;
  background-color: #fff;
}
.footer-nav h4 {
  font-size: 1.25rem;
  font-weight: 700;
}
.footer-nav ul {
  list-style-type: none;
  margin-left: -30px;
}
.footer-nav .social-icon {
  width: 22px;
  height: 40px;
  background: url(icons/social/social-icons.png);
  background-repeat: no-repeat;
  display: inline-block;
  margin-right: 12px;
  cursor: pointer;
}
.footer-nav .social-icon.facebook {
  background-position: -10px -5px;
  background-size: cover;
}
.footer-nav .social-icon.linkedin {
  width: 35px;
  background-position: -38px -5px;
  background-size: cover;
}
.footer-nav .social-icon.youtube {
  width: 40px;
  background-position: -82px -5px;
  background-size: cover;
}
.footer-nav .social-icon.instagram {
  width: 30px;
  background-position: -135px -5px;
  background-size: cover;
}
.footer-nav li {
  padding: 06px 0;
}
.footer-nav li a {
  text-decoration: none;
  color: #000;
  font-size: 1.05rem;
}
/*modal*/
.modal {
  display: block;
  visibility: hidden;
}
.modal.show {
  visibility: visible;
  background-color: rgb(7 7 7 / 73%);
  transition: opacity 0.2s linear;
}
.modal-content {
  opacity: 0;
}
.modal.show .modal-content {
  opacity: 1;
  transition: 0.2s linear;
}
.value-proposition ul {
  background-color: #2598bf;
  transform: rotate(-3deg);
  border-radius: 25px;
  z-index: 9;
  position: relative;
  padding: 45px 20px 30px 45px;
  margin-top: -40px;
  margin-left: 5px;
}
.value-proposition li {
  font-size: 1rem;
  list-style-type: none;
  margin-left: -15px;
  color: #fff;
  padding: 10px 15px 10px 50px;
  transform: rotate(3deg);
}
.value-proposition li span {
  width: 18px;
  height: 18px;
  background-color: #fd6cc6;
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  margin-left: -30px;
  margin-top: 5px;
}
.value-proposition li span::before {
  content: "L";
  color: #ffffff;
  transform: matrix(-0.71, -0.71, -0.71, 0.71, 0, 0);
  position: absolute;
  margin-left: 6px;
  margin-top: -7px;
  border-radius: 50%;
  font: normal normal bold 18px/26px Quicksand;
}
.value-proposition {
  min-width: 695px;
}
.value-proposition .modal-content {
  border-radius: 25px;
}
.value-proposition .moyo-well-done {
  position: absolute;
  bottom: 10px;
  left: -110px;
  max-width: 275px;
}
.value-proposition .skidos-logo {
  margin-bottom: 20px;
}
.value-proposition .premium-pass {
  color: #000;
  font: normal normal bold 13px/26px Quicksand;
  text-transform: uppercase;
}
.value-proposition .premium-pass span {
  color: #dda300;
}
.value-proposition .premium-pass img {
  width: 25px;
}
.value-proposition h3 {
  font: normal normal bold 24px/26px Quicksand;
  line-height: 1.25;
  text-align: left;
}
.value-proposition .premium-btn {
  background-color: #fdd12a;
  background-image: url(./icons/primium-btn-bg.png);
  font: normal normal bold 15px/26px Quicksand;
  border-radius: 18px;
  border-bottom: 3px solid #dfb20a;
  padding: 10px 35px;
  background-size: inherit;
  background-repeat: no-repeat;
}
.value-proposition .premium-section {
  padding: 25px;
}
.value-proposition .login-action {
  font-size: 0.9rem;
}
.value-proposition .login-action span {
  font: normal normal bold 13px/26px Quicksand;
  color: #00c6a3;
  cursor: pointer;
  text-decoration: underline;
}
.modal .close-btn {
  background: url(./icons/close-btn.png) no-repeat;
  background-size: cover;
  width: 12px;
  height: 12px;
  display: inline-block;
  position: absolute;
  right: 20px;
  cursor: pointer;
  top: 20px;
}
/* iPad */
@media (max-width: 960px) {
  .hidden-sm {
    display: none;
  }
  .visible-sm {
    display: block;
  }
  .topgap-sm-0 {
    margin-top: 0;
  }
  .topgap-sm-20 {
    margin-top: 20px;
  }
  .game_tile_row {
    height: 300px;
  }
  .ba_left_tree {
    height: 65vh;
  }
  .ba_bottom_tree {
    width: 100vw;
  }
  .ba_left_tree {
    width: auto;
    height: 100%;
    max-width: 350px;
  }
  .ba_right_tree {
    height: 100%;
    width: auto;
    max-width: 350px;
  }
  .value-proposition {
    min-width: 625px;
  }
  .value-proposition ul {
    padding: 25px 20px 30px 35px;
  }
  .value-proposition ul li {
    padding: 10px 15px 10px 40px;
  }
  .value-proposition .premium-btn {
    padding: 10px 30px;
  }
  .mobile-header-nav {
    position: fixed;
    width: 80%;
    background-color: #b6eee3;
    z-index: 99;
    right: 0;
    top: 0;
    height: 100%;
    font-weight: 600;
    overflow-y: auto;
  }
  .mobile-header-nav .pages {
    font-size: 0.75rem;
    color: #3a7c64;
    margin-bottom: 10px;
    text-transform: uppercase;
  }
  .mobile-header-nav .nav-block {
    padding: 20px 25px 25px 55px;
  }
  .mobile-header-nav .nav-block h3 {
    font-weight: 700;
    color: #000;
    line-height: 0.75;
  }
  .mobile-header-nav .nav-block .login-link {
    color: #000;
  }
  .mobile-header-nav .nav-link {
    font-weight: 600;
    font-size: 1.05rem;
    padding: 12px 0;
  }
  .mobile-header-nav .try-free,
  .mobile-header-nav .upgrade-btn {
    padding: 20px;
    text-align: center;
    border-top: 1px solid rgba(0, 198, 163, 0.4);
    position: absolute;
    width: 100%;
    bottom: 0;
  }
  .mobile-header-nav .try-free a {
    display: inline-block;
    width: 100%;
    padding: 8px;
    background-color: #00c6a3;
    color: #fff;
    font-size: 1.25rem;
    border-radius: 10px;
    cursor: pointer;
    text-decoration: none;
  }
  .mobile-header-nav .close-btn {
    background: url(./icons/close-btn.png) no-repeat;
    background-size: cover;
    width: 15px;
    height: 15px;
    display: inline-block;
    position: absolute;
    left: 20px;
    cursor: pointer;
    top: 20px;
  }
  .mobile-header-nav .dropdown-toggle::after {
    content: "";
    border: solid #333;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    margin-left: 12px;
    margin-top: 8px;
    position: absolute;
  }
  .mobile-header-nav .dropdown-menu {
    background: #b6eee3;
    border: none;
    padding: 0;
  }
  .mobile-header-nav .dropdown-menu .dropdown-item {
    font-weight: 600;
    font-size: 1.05rem;
    padding: 0px 15px;
    display: inline-block;
    line-height: 2.2;
  }
  .mobile-header-nav .dropdown-menu.show {
    transform: none !important;
    position: relative !important;
  }
  .mobile-header-nav .player-profile .player-img,
  .mobile-header-nav .player-profile .player-name {
    width: 40%;
    display: inline-block;
    font-size: 1.35rem;
    font-weight: 700;
  }
  .mobile-header-nav .player-profile .player-name {
    width: 60%;
    padding-left: 15px;
  }
  .mobile-header-nav .player-profile .player-img img {
    width: 95px;
    background-color: #90e7d6;
    border-radius: 50%;
    padding: 3px;
    margin-top: -25px;
  }
  .mobile-header-nav .player-profile span a {
    font-size: 0.8rem;
    background-color: #42c3a1;
    color: #fff;
    padding: 6px 16px;
    border-radius: 10px;
    font-weight: 500;
  }
  .mobile-header-nav .player-profile {
    margin-bottom: 50px;
  }
  .mobile-header-nav .profile-list img {
    width: 70px;
  }
  .mobile-header-nav .profile-list .player-name {
    font-size: 1.25rem;
    padding-left: 10px;
  }
  .mobile-header-nav .profile-list {
    margin: 15px 0;
    text-decoration: none;
    color: #000;
  }
  .mobile-header-nav .profile-list .new-player:after {
    content: "+";
    font-size: 2.75rem;
    position: absolute;
    margin-left: 10px;
    background: #333;
    width: 50px;
    height: 50px;
    line-height: 0.75;
    text-align: center;
    color: #fff;
    border-radius: 50%;
    border: 7px solid #fff;
  }
  .mobile-header-nav .profile-list .add-new {
    margin-left: 80px;
    font-size: 1.25rem;
    line-height: 2.5;
  }
  .mobile-header-nav .upgrade-btn span {
    padding: 10px;
    background-color: #fdd12a;
    background-image: url(./icons/upgrade-btn-bg.png);
    background-repeat: no-repeat;
    background-position: center;
    display: inline-block;
    width: 90%;
    font-size: 1.3rem;
    border-radius: 10px;
    cursor: pointer;
  }
  .footer-nav {
    padding: 0 25px;
  }
}
/* Mobile */
@media only screen and (max-width: 700px) {
  .hidden-xs {
    display: none;
  }
  .visible-xs {
    display: block;
  }
  .topgap-xs-0 {
    margin-top: 0;
  }
  .topgap-xs-20 {
    margin-top: 20px;
  }
  .game_tile_row {
    height: 305px;
  }
  .ba_top_tree {
    width: 100vw;
  }
  .ba_left_tree {
    width: auto;
    height: 100%;
    max-width: 350px;
  }
  .ba_right_tree {
    height: 100%;
    width: auto;
    max-width: 350px;
  }
  .ba_bottom_tree {
    width: 100vw;
  }
  .value-proposition {
    min-width: auto;
    transform: scale(0.9);
  }
  .value-proposition .moyo-well-done {
    display: none;
  }
  .value-proposition h3 {
    font: normal normal bold 32px/26px Quicksand;
    line-height: 1.25;
  }
  .value-proposition .premium-btn {
    font: normal normal bold 20px/26px Quicksand;
    padding: 15px 35px;
  }
  .value-proposition ul {
    margin-right: 15px;
  }
  .value-proposition .premium-section {
    padding-top: 0;
  }
  .footer {
    height: 500px;
  }
  .footer-nav,
  .footer-second {
    text-align: center;
  }
}

@media only screen and (max-width: 450px) {
  .game_tile_row {
    height: 165px;
  }
  .game_mobile_nav .left_block {
    font-size: 16px;
  }
  .game_mobile_nav .right_block select {
    font-size: 16px;
  }
  .game_tile_row {
    height: 350px;
  }
  .fixed-game-box {
    top: 10% !important;
  }
  .header-navbar {
    position: fixed;
    width: 100%;
    z-index: 99;
    top: 0;
  }
  .footer-second {
    z-index: 99;
    bottom: -50px;
  }
  .detail_screen2 {
    min-height: 88vh;
  }
}
/* iphone 6/7/8 */
@media only screen and (max-width: 375px) {
  .detail_screen2 .detail_green_bg {
    top: -70%;
  }
  .fixed-game-box {
    margin-top: 12%;
  }
  .home_play_music {
    top: 15%;
  }
  .gtc_content {
    max-height: 125px;
    overflow: auto;
  }
}
/* iPad */
@media all and (device-width: 1024px) and (device-height: 768px) and (orientation: landscape) {
  .navbar .nav-link {
    margin: 0 5px;
  }
}
/* iPad Pro*/
@media all and (device-width: 1366px) and (device-height: 1024px) and (orientation: landscape) {
  .detail_screen2 {
    min-height: 65vh;
  }
  .detail_purple_layer .detail_purple_bg {
    top: -115%;
  }
}
