.privacyHeading {
  text-align: center;
  font-family: var(--font-nevermind-bold);
}

.privacyContainer {
  font-family: var(--font-poppins);
  margin: 4px 20px 5px 20px;
  line-height: 1.38rem;
  font-size: 0.9rem;
}

.privacyContainer p {
  font-size: 17px !important;
  font-weight: 400 !important;
  font-style: normal !important;
  line-height: 1.4em !important;
  letter-spacing: 0px !important;
  padding-bottom: 27px;
  text-align: justify !important;
  font-size: 0.8rem;
  font-family: Arial, sans-serif;
  color: #000000;
  background-color: transparent;
  font-weight: 400;
  font-style: normal;
  font-variant: normal;
  text-decoration: none;
  vertical-align: baseline;
  white-space: pre-wrap;
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
}

.privacyPargrapDark {
  font-family: var(--font-poppins);
  font-weight: bold;
  margin-top: 30px;
}

.privacyApplications {
  margin-top: 4rem;
}

.spanApplicationTitle {
  font-weight: bold;
}

.notificationHeading {
  margin-top: 4rem;
  font-size: 1.1rem;
  line-height: 2.38rem;
  font-weight: bold;
}

.Cook<PERSON> {
  font-weight: bold;
}

.tableContainer {
  display: flex;
  margin: 50px 80px 50px 80px;
}

@media (max-width: 430px) {
  .tableContainer {
    margin: 1rem 0.4rem 1rem 0.4rem;
  }
}

.table {
  border-collapse: collapse;
  width: 100%;
}

.firstHeading {
  border: 1px solid black;
}

.secondHeading {
  border: 1px solid black;
}

.thirdHeading {
  border: 1px solid black;
}

.td {
  border: 1px solid black;
  padding: 8px;
  text-align: left;
}

.th {
  background-color: #f4f4f4;
}

.firstChild {
  width: 200px;
  border: 1px solid black;
  align-content: flex-start;
  padding-left: 8px;
}

.secondChild {
  width: 250px;
  border: 1px solid black;
  align-content: flex-start;
  padding-left: 8px;
}

.thirdChild {
  width: 130px;
  border: 1px solid black;
  align-content: flex-start;
  padding-left: 8px;
}
