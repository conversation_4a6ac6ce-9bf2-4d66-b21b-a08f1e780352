import React from "react";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import RekindleMinds from "components/RekindleMinds";
import Subscription from "components/Subscription";
import Email from "components/Email";
import Questionnaire from "components/Questionnaire";
import Iris from "components/IRIS/Iris";

const AppRoutes = () => {
  return (
    <React.Fragment>
      <Router basename={"/rekindle"}>
        <Routes>
        <Route exact path="/" element={<RekindleMinds />} />
          <Route exact path="/iris" element={<Iris />} />
          <Route exact path="/rekindle" element={<RekindleMinds />} />
          {/* <Route path="/subscription" element={<Subscription />} />
          <Route path="/email" element={<Email />} />
          <Route path="/questionnaire" element={<Questionnaire />} /> */}
          {/* <Route exact path="/video" element={<Video />} />
          <Route exact path="/login" element={<Login />} />
          <Route exact path="/register" element={<Signup />} /> */}
        </Routes>
      </Router>
    </React.Fragment>
  );
};

export default AppRoutes;
