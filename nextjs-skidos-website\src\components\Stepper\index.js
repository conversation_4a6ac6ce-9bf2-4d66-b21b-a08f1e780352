import Image from "next/image";
import "./Stepper.css";

const Stepper = ({ currentStep, handleBack, steps = [1, 2, 3, 4, 5] }) => {
  return (
    <div className="stepper-back-wrapper">
      <div>
        {currentStep > 1 && (
          <Image
            src="/images/webGl/backArrow.png"
            width={10}
            height={10}
            className="back-button"
            onClick={handleBack}
            alt="Back Arrow"
          />
        )}
      </div>
      <div className="stepper">
        {steps.map((step, index) => (
          <div key={index} className="step">
            <div className={`circle ${index + 1 <= currentStep ? "active" : ""}`}>
              {index < currentStep && (
                <Image src="/images/webGl/check.png" alt="Check Icon" width={12} height={12} />
              )}
            </div>
            {index < steps.length - 1 && (
              <div className={`line ${index + 1 < currentStep ? "completed" : ""}`} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Stepper;
