.overlay {
  position: fixed;

  top: 0;

  left: 0;

  width: 100%;

  height: 100%;

  background-color: rgba(0, 0, 0, 0.5);

  display: flex;

  justify-content: center;

  align-items: center;

  z-index: 99;
}

.popup {
  background-color: #fff;

  padding: 20px;

  border-radius: 10px;

  text-align: center;

  width: 300px;

  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.title {
  font-size: 20px;

  color: #333;

  margin-bottom: 10px;

  font-family: var(--font-nevermind-bold);
}

.message {
  font-size: 1.1rem;

  color: #555;

  margin-bottom: 20px;

  font-family: var(--font-nevermind-light);
}

.button {
  width: 60%;

  background-color: #6a5acd;

  color: #fff;

  padding: 10px 20px;

  border: none;

  border-radius: 5px;

  cursor: pointer;

  margin-bottom: 10px;
}

.link {
  display: block;

  color: #6a5acd;

  margin-top: 10px;

  text-decoration: underline;

  font-size: 0.7rem;

  cursor: pointer;
}
