"use client";
import styles from "./styles.module.css";
import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollToPlugin } from "gsap/ScrollToPlugin";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import useIsMobile from "@/hooks/useIsMobile";
import Link from "next/link";
import Image from "next/image";
// import styles from "./styles.module.css";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { themeCardsRow1, themeCardsRow2, HomeStreet } from "@/constants";

gsap.registerPlugin(ScrollToPlugin, ScrollTrigger);

const scrollAnimation = (wrapper, card) => {
  gsap.fromTo(
    card,
    { opacity: 0, y: 50 },
    {
      opacity: 1,
      y: 0,
      duration: 0.5,
      ease: "power2.out",
      stagger: 0.5,
      scrollTrigger: {
        trigger: wrapper,
        start: "top 80%",
        end: "top 50%",
        once: true,
      },
    }
  );
};

const HomeThemeCarouselCt = () => {
  const isMobile = useIsMobile();
  const containerRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    scrollAnimation(`.${styles.nurturingGrowthCardsWrapper}`, `.${styles.nurturingGrowthCards}`);

    const container = containerRef.current;
    if (!container) return;
    const maxScrollLeft = container.scrollWidth - container.clientWidth;

    animationRef.current = gsap.to(container, {
      scrollTo: { x: maxScrollLeft },
      duration: 10,
      repeat: -1,
      ease: "none",
      yoyo: true,
    });

    container.addEventListener("mouseenter", () => animationRef.current.pause());
    container.addEventListener("mouseleave", () => animationRef.current.play());

    return () => {
      animationRef.current.kill();
      container.removeEventListener("mouseenter", () => animationRef.current.pause());
      container.removeEventListener("mouseleave", () => animationRef.current.play());
    };
  }, []);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <div ref={containerRef} className={styles.discoverThemesContainer}>
      <div className={styles.discoverThemesWrapper}>
        {themeCardsRow1.map((theme) => {
          return (
            <Link
              href={{
                pathname: `/products`,
                query: {
                  theme: theme.themeName,
                },
              }}
              className="noLinkStyle"
              key={theme.themeName}
              prefetch={false}
            >
              <div className={styles.discoverThemesCard}>
                <Image
                  src={theme.themeImg}
                  alt={theme.themeName}
                  width={230}
                  height={150}
                  className={styles.discoverThemesCardImg}
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                />
                <p>{theme.themeName}</p>
                <p>{theme.themeDesc}</p>
              </div>
            </Link>
          );
        })}
      </div>
      <div className={styles.discoverThemesWrapper}>
        {themeCardsRow2.map((theme) => {
          return (
            <Link
              href={{
                pathname: `/products`,
                query: {
                  theme: theme.themeName,
                },
              }}
              className="noLinkStyle"
              key={theme.themeName}
            >
              <div className={styles.discoverThemesCard}>
                <Image
                  src={theme.themeImg}
                  alt={theme.themeName}
                  width={230}
                  height={150}
                  className={styles.discoverThemesCardImg}
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNctmhZPQAGUwJv7ObBEQAAAABJRU5ErkJggg=="
                />
                <p>{theme.themeName}</p>
                <p>{theme.themeDesc}</p>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default HomeThemeCarouselCt;
