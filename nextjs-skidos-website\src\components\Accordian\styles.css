.container {
  /* max-width: 1512px; */
  max-width: 1728px;
  margin: 0 auto;
}

.wrapper {
  overflow: hidden;
  border-radius: 12px;
  background-color: #f9f9f9;
  margin: 2rem 0;
  color: #5b5b5b;
  font-family: var(--font-poppins);
}

.wrapper .question-container {
  width: 100%;
  text-align: left;
  padding: 5px 10px 0px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  font-size: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #5b5b5b;
  font-family: var(--font-poppins);
}

.wrapper:hover {
  background-color: #f2ecff;
  background-image: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.04), transparent);
}

.wrapper .arrow {
  font-size: 2rem;
  transition: 0.5s ease-in-out;
}

.arrow.active {
  rotate: 180deg;
}

.wrapper .answer-container {
  padding: 0 1rem;
  transition: height 0.5s ease-in;
}

.wrapper .answer-content {
  padding: 1rem 0;
  font-size: 1rem;
}
