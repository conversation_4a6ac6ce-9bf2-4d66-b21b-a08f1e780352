"use client";

import setLanguageValue from "@/actions/set-language";
import Cookies from "js-cookie";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import styles from "./styles.module.css";

const LanguageSelector = () => {
  const router = useRouter();
  const t = useTranslations("languageSelector");
  const [isOptionsVisible, setIsOptionsVisible] = useState(false);
  const [locale, setLocale] = useState("en");

  useEffect(() => {
    // Set the initial locale if available in cookies (only on the client side)
    const savedLocale = Cookies.get("locale") || "en";
    setLocale(savedLocale);
  }, []);

  // Handle toggle for language options visibility
  const toggleLanguageOptions = () => {
    setIsOptionsVisible((prev) => !prev);
  };

  const handleLanguageChange = (lang) => {
    setLanguageValue(lang);
    setIsOptionsVisible(false);
    setLocale(lang);

    // Save language preference in cookies
    Cookies.set("locale", lang, { expires: 365 });

    window.scrollTo({ top: 0, behavior: "smooth" });

    if (router && router.push) {
      router.refresh();
    }
  };

  return (
    <>
      {isOptionsVisible && (
        <div className={styles.overlay} onClick={() => setIsOptionsVisible(false)}></div>
      )}
      <div className={styles.languageSelectorWrapper} onClick={toggleLanguageOptions}>
        <div>
          <Image
            src="/images/LangTranslatorIcon.webp"
            width={32}
            height={30}
            alt={t("translator_alt")}
            className={styles.langTranslatorIcon}
          />
        </div>
        <div>{locale.toUpperCase()}</div>
        <div>
          <Image
            src="/images/SelectMenuIcon.webp"
            width={15}
            height={15}
            alt={t("menu_selector_alt")}
            className={styles.langTranslatorIcon}
          />
        </div>
      </div>
      {isOptionsVisible && (
        <div className={styles.languageOptionsToggleWrapper}>
          <ul>
            <li
              className={locale === "en" ? styles.selectedLanguage : ""}
              onClick={() => handleLanguageChange("en")}
            >
              English (US)
            </li>
            <li
              className={locale === "uk" ? styles.selectedLanguage : ""}
              onClick={() => handleLanguageChange("uk")}
            >
              English (UK)
            </li>
            <li
              className={locale === "da" ? styles.selectedLanguage : ""}
              onClick={() => handleLanguageChange("da")}
            >
              Danish
            </li>
            <li
              className={locale === "br" ? styles.selectedLanguage : ""}
              onClick={() => handleLanguageChange("br")}
            >
              Portuguese
            </li>
            <li
              className={locale === "nb" ? styles.selectedLanguage : ""}
              onClick={() => handleLanguageChange("nb")}
            >
              Norwegian
            </li>
            <li
              className={locale === "sv" ? styles.selectedLanguage : ""}
              onClick={() => handleLanguageChange("sv")}
            >
              Swedish
            </li>
          </ul>
        </div>
      )}
    </>
  );
};

export default LanguageSelector;
