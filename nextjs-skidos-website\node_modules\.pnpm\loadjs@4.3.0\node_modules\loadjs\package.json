{"name": "loadjs", "version": "4.3.0", "license": "MIT", "description": "Tiny async loader for modern browsers", "keywords": ["async", "loader", "dependency manager"], "homepage": "https://github.com/kubetail-org/loadjs", "bugs": {"url": "https://github.com/kubetail-org/loadjs/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/kubetail-org/loadjs.git"}, "main": "dist/loadjs.umd.js", "scripts": {"build-all": "gulp build-all", "build-dist": "gulp build-dist", "build-examples": "gulp build-examples", "build-tests": "gulp build-tests", "gulp": "gulp", "http-server": "http-server"}, "devDependencies": {"del": "^5.1.0", "gulp": "^4.0.2", "gulp-jshint": "^2.1.0", "gulp-rename": "^2.0.0", "gulp-uglify": "^3.0.2", "gulp-umd": "^2.0.0", "jshint": "^2.11.0"}}