"use client";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect } from "react";
import Colorine from "../../../public/images/aboutUs/fiks.webp";
import Layout from "../../../public/images/aboutUs/History.webp";
import <PERSON>yo from "../../../public/images/aboutUs/moyo.webp";
import Neta from "../../../public/images/aboutUs/neta.webp";
import styles from "./styles.module.css";

const AboutPage = () => {
  const t = useTranslations("AboutPage");

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <title>{t("meta.title")}</title>
      <meta name="description" content={t("meta.description")} />
      <div className={styles.aboutUsContainer}>
        <div className={styles.OurMissionContainer}>
          <div className={styles.MissionFlexCenter} ref={(el) => setRef(el)}>
            <div className={styles.MissionTitle}>
              {t("mission.title.our")}{" "}
              <span className={styles.MissionHighlightSubheader}>
                {t("mission.title.highlight")}{" "}
                <Image
                  src="/images/aboutUs/Arrow.png"
                  width={46}
                  height={46}
                  className={styles.MissionArrowImg}
                  alt={t("mission.arrowAlt")}
                />
              </span>
            </div>
            <p className={styles.MissionText}>{t("mission.description")}</p>
          </div>
        </div>

        <div className={styles.historyCoreValuesWrapper}>
          <div className={styles.about} ref={(el) => setRef(el)}>
            <div className={styles.aboutContent}>
              <h1 className={styles.aboutHighlightsubheader1} style={{ position: "sticky" }}>
                {t("history.title")}
              </h1>
            </div>
            <div className={styles.historyScroll}>
              <Image
                src={Layout}
                alt={t("history.imageAlt")}
                className={styles.aboutHighlightImg}
              />
            </div>
          </div>
        </div>

        <div className={styles.meetHeading} ref={(el) => setRef(el)}>
          {t("gang.title")}
        </div>

        <div className={styles.isFlexCenter1} style={{ background: "#D2991F" }}>
          <div className={styles.imageContainer}>
            <Image src={Moyo} alt={t("gang.moyo.name")} className={styles.moyoImage} />
          </div>
          <div className={styles.paragarp}>
            <h1>{t("gang.moyo.name")}</h1>
            <p>{t("gang.moyo.description")}</p>
          </div>
        </div>

        <div className={styles.isFlexCenter1} style={{ background: "#19788E" }}>
          <div className={styles.paragarp}>
            <h1>{t("gang.fiks.name")}</h1>
            <p>{t("gang.fiks.description")}</p>
          </div>
          <div className={styles.imageContainer}>
            <Image src={Colorine} alt={t("gang.fiks.name")} className={styles.colorineImage} />
          </div>
        </div>

        <div className={styles.isFlexCenter1} style={{ background: "#EC622B" }}>
          <div className={styles.imageContainer}>
            <Image src={Neta} alt={t("gang.neta.name")} className={styles.netaImage} />
          </div>
          <div className={styles.paragarp}>
            <h1>{t("gang.neta.name")}</h1>
            <p>{t("gang.neta.description")}</p>
          </div>
        </div>
      </div>
    </>
  );
};
export default AboutPage;
