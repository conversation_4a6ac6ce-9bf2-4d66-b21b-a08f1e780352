/* eslint-disable no-console, no-undef */
const fs = require("fs");
const path = require("path");
const https = require("https");

// Load environment variables from .env file if it exists
try {
  const envPath = path.join(__dirname, "../../.env");
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, "utf8");
    envContent.split("\n").forEach((line) => {
      const [key, value] = line.split("=");
      if (key && value && !process.env[key]) {
        process.env[key] = value.trim();
      }
    });
  }
} catch (error) {
  // Ignore errors loading .env file
}

// Configuration
const MESSAGES_DIR = "./messages";
const LANGUAGES = [
  { lang: "en", code: "en" }, // Master language (English)
  { lang: "uk", code: "en-GB" }, // British English
  { lang: "da", code: "da" }, // Danish
  { lang: "br", code: "pt" }, // Portuguese (Brazilian)
  { lang: "nb", code: "nb" }, // Norwegian
  { lang: "sv", code: "sv" }, // Swedish
];

const MASTER_INDEX = 0;

// Azure Translator V3 Configuration
const TRANSLATOR_ENDPOINT = "https://api.cognitive.microsofttranslator.com";
const TRANSLATOR_API_VERSION = "3.0";

// Get API key from environment variable
const TRANSLATOR_KEY = process.env.AZURE_TRANSLATOR_KEY || "f4382a6fbb9f4586a224f6dcb390319e";

if (!TRANSLATOR_KEY) {
  console.error("❌ Error: AZURE_TRANSLATOR_KEY environment variable is required");
  console.log("Please set your Azure Translator API key:");
  console.log("  Windows: set AZURE_TRANSLATOR_KEY=your_key_here");
  console.log("  Linux/Mac: export AZURE_TRANSLATOR_KEY=your_key_here");
  process.exit(1);
}

// Language code mapping for Microsoft Translator V3 API
const getTranslatorCode = (locale) => {
  const codeMap = {
    en: "en", // English
    uk: "en-GB", // British English
    da: "da", // Danish
    br: "pt", // Portuguese (Brazilian)
    nb: "nb", // Norwegian
    sv: "sv", // Swedish
  };
  return codeMap[locale] || "en";
};

// HTTP request helper for Azure Translator V3 API
const makeTranslatorRequest = (texts, fromLang, toLang) => {
  return new Promise((resolve, reject) => {
    const fromCode = getTranslatorCode(fromLang);
    const toCode = getTranslatorCode(toLang);

    const url = `${TRANSLATOR_ENDPOINT}/translate?api-version=${TRANSLATOR_API_VERSION}&from=${fromCode}&to=${toCode}`;
    const postData = JSON.stringify(texts.map((text) => ({ Text: text })));

    const options = {
      method: "POST",
      headers: {
        "Ocp-Apim-Subscription-Key": TRANSLATOR_KEY,
        "Content-Type": "application/json",
        "Content-Length": Buffer.byteLength(postData),
      },
    };

    const req = https.request(url, options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          if (res.statusCode === 200) {
            const response = JSON.parse(data);
            const translations = response.map((item) => item.translations[0].text);
            resolve(translations);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
};

// British English specific transformations
const applyBritishTransformations = (text) => {
  const americanToBritish = {
    color: "colour",
    flavor: "flavour",
    behavior: "behaviour",
    labor: "labour",
    neighbor: "neighbour",
    center: "centre",
    meter: "metre",
    theater: "theatre",
    catalog: "catalogue",
    program: "programme",
    practice: "practise",
    license: "licence",
    customize: "customise",
    realize: "realise",
    analyze: "analyse",
    // Add more transformations as needed
  };

  let britishText = text;
  for (const [american, british] of Object.entries(americanToBritish)) {
    britishText = britishText.replace(new RegExp(american, "gi"), british);
  }
  return britishText;
};

class TranslationManager {
  constructor() {
    this.data = LANGUAGES;
    this.sources = {};
    this.tasks = {};
    this.masterTree = {};
  }

  initialize() {
    // Initialize data structure for each language
    this.data.forEach((lang) => {
      lang.texts = {};
      lang.modified = false;
    });

    this.sources = {};
    this.tasks = {};

    this.loadResources(() => {
      this.prepareTasks();
      this.executeTasks(() => {
        this.dumpResources(() => {
          console.log("✅ Translation completed successfully!");
        });
      });
    });
  }

  loadResources(callback, langIndex = 0) {
    const filePath = path.join(MESSAGES_DIR, `${this.data[langIndex].lang}.json`);

    fs.readFile(filePath, "utf8", (err, content) => {
      if (!err) {
        const translations = JSON.parse(content);

        if (langIndex === MASTER_INDEX) {
          this.masterTree = JSON.parse(JSON.stringify(translations));
        }

        this.prepareData(langIndex, translations);

        // Store source texts from master language
        if (langIndex === MASTER_INDEX) {
          for (const text in this.data[langIndex].texts) {
            this.sources[text] = langIndex;
          }
        }
      } else {
        console.error(`Error loading ${this.data[langIndex].lang}:`, err);
      }

      if (langIndex === this.data.length - 1) {
        return callback();
      }

      this.loadResources(callback, ++langIndex);
    });
  }

  prepareData(langIndex, obj, position = "") {
    for (const key in obj) {
      if (typeof obj[key] === "object") {
        this.prepareData(langIndex, obj[key], position + key + ".");
      } else {
        this.data[langIndex].texts[position + key] = obj[key];
      }
    }
  }

  prepareTasks() {
    for (let langIndex = 0; langIndex < this.data.length; langIndex++) {
      if (langIndex === MASTER_INDEX) continue;

      // Initialize task for this language
      this.tasks[langIndex] = {
        from: MASTER_INDEX,
        to: langIndex,
        texts: [],
        keys: [],
      };

      // Find missing translations
      for (const key in this.sources) {
        if (!this.data[langIndex].texts[key]) {
          this.data[langIndex].modified = true;
          this.tasks[langIndex].texts.push(this.data[MASTER_INDEX].texts[key]);
          this.tasks[langIndex].keys.push(key);
        }
      }
    }
  }

  executeTasks(callback) {
    const tasksList = Object.values(this.tasks);
    let completedTasks = 0;

    tasksList.forEach((task) => {
      this.executeTask(task, () => {
        completedTasks++;
        if (completedTasks === tasksList.length) {
          callback();
        }
      });
    });

    // If no tasks, call callback immediately
    if (tasksList.length === 0) {
      callback();
    }
  }

  executeTask(task, callback) {
    if (task.texts.length === 0) {
      return callback();
    }

    this.translateArray(
      task.texts,
      this.data[task.from].lang,
      this.data[task.to].lang,
      (translations) => {
        translations.forEach((translation, index) => {
          const key = task.keys[index];
          this.data[task.to].texts[key] = translation;
        });
        callback();
      }
    );
  }

  async translateArray(texts, fromLang, toLang, callback) {
    if (texts.length === 0) {
      return callback([]);
    }

    // Special handling for British English
    if (toLang === "uk") {
      const britishTexts = texts.map((text) => applyBritishTransformations(text));
      return callback(britishTexts);
    }

    try {
      const batches = this.minimizeArrayForTranslation(texts);
      const results = [];

      for (const batch of Object.values(batches)) {
        const translations = await makeTranslatorRequest(batch, fromLang, toLang);
        results.push(...translations);

        // Add delay between batches to avoid rate limiting
        if (Object.values(batches).length > 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      callback(results);
    } catch (error) {
      console.error(`Translation error (${fromLang} -> ${toLang}):`, error.message);
      // Return original texts as fallback
      callback(texts);
    }
  }

  minimizeArrayForTranslation(texts) {
    const batches = { 0: [] };
    let totalLength = 0;
    let batchIndex = 0;

    texts.forEach((text) => {
      totalLength += text.length;

      if (totalLength > 10000) {
        // Microsoft's character limit per request
        totalLength = text.length;
        batchIndex++;
        batches[batchIndex] = [text];
      } else {
        batches[batchIndex].push(text);
      }
    });

    return batches;
  }

  dumpResources(callback) {
    const modifiedLanguages = this.data.filter((x) => x.modified);
    let remainingWrites = modifiedLanguages.length;

    if (remainingWrites === 0) {
      return callback();
    }

    modifiedLanguages.forEach((lang) => {
      const translations = this.reconstructTranslations(lang.texts);
      const filePath = path.join(MESSAGES_DIR, `${lang.lang}.json`);

      fs.writeFile(filePath, JSON.stringify(translations, null, 2), "utf8", (err) => {
        if (err) {
          console.error(`Error saving ${lang.lang} translations:`, err);
        } else {
          console.log(`✅ Updated ${lang.lang} translations`);
        }

        remainingWrites--;
        if (remainingWrites === 0) {
          callback();
        }
      });
    });
  }

  reconstructTranslations(texts) {
    const result = {};

    for (const [key, value] of Object.entries(texts)) {
      let current = result;
      const parts = key.split(".");

      for (let i = 0; i < parts.length - 1; i++) {
        current[parts[i]] = current[parts[i]] || {};
        current = current[parts[i]];
      }

      current[parts[parts.length - 1]] = value;
    }

    return result;
  }
}

// Create and run the translator
const translator = new TranslationManager();
translator.initialize();
