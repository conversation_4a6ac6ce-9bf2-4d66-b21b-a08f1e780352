import React from "react";

const Modal = ({ children, show, className }) => (
  <div
    className={`modal ${show ? "show" : "hide"}`}
    tabIndex="-1"
    role="dialog"
  >
    <div
      className={`modal-dialog modal-dialog-centered ${className}`}
      role="document"
    >
      <div className="modal-content">
        <div className="modal-body">{children}</div>
      </div>
    </div>
  </div>
);

export default Modal;
