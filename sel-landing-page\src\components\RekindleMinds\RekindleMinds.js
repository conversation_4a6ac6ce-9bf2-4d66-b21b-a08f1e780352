import Header from "components/Header";
import Footer from "../Footer/Footer";
import "react-responsive-carousel/lib/styles/carousel.min.css"; // requires a loader
import { Carousel } from "react-responsive-carousel";
import ReactGA from "react-ga4";

/*images*/
import howItWorksImg from "icons/rekindle-minds/how-it-works.png";
import whyPlayImg from "icons/rekindle-minds/why-play.png";
import progressReportImg from "icons/rekindle-minds/progress-report.png";
import certificationOne from "icons/rekindle-minds/certification-one.png";
import certificationTwo from "icons/rekindle-minds/certification-two.png";
import certificationThree from "icons/rekindle-minds/certification-three.png";
import certificationFour from "icons/rekindle-minds/certification-four.png";
import reviewOne from "icons/rekindle-minds/review-one.png";
import reviewTwo from "icons/rekindle-minds/review-two.png";
import reviewThree from "icons/rekindle-minds/review-three.png";
import leftArrow from "icons/rekindle-minds/left-arrow.png";
import rightArrow from "icons/rekindle-minds/right-arrow.png";
import bikeRacing from "icons/rekindle-minds/bike-racing.png";
import doctor from "icons/rekindle-minds/Doctor.png";
import bath from "icons/rekindle-minds/Bath.png";
import superstore from "icons/rekindle-minds/Supertstore.png";

/*player */
import Plyr from "plyr-react";
import "plyr-react/plyr.css";

import Hls from "hls.js";

// external css
import "./RekindleMinds.css";
function RekindleMinds() {
  // const ref = useRef(null);
  /*subscription status*/

  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen",
  ];
  // const goFullScreen = () => {
  //   ref.current.plyr.fullscreen.enter();
  // };

  const eventTrackerGa4 = (category, action, label, nonInteraction) => {
    ReactGA.event({ category, action, label, nonInteraction });
  };

  return (
    <div className="rekindle-minds">
      <Header isSubscribed={false} />
      <div className="hero">
        <iframe
          src="https://images.skidos.com/rekindle/landing-page.html"
          width="100%"
          height="680px"
          frameBorder="0"
          title="Rekindle Minds"
        >
          Browser not compatible.
        </iframe>
        <div className="container ">
          <div className="row d-flex justify-content-center header">
            <div className="col-12 col-md-7 text-center">
              <h1 className="h2">
                A mindful game designed by teachers to boost your child’s
                emotional growth and learning
              </h1>
            </div>
          </div>
        </div>
      </div>
      <div className="container" id="player-container">
        <div className="row d-flex justify-content-center">
          <div className="col-12 col-md-8">
            <div className="player-container">
              <Plyr
                id="plyr"
                controls
                options={{ volume: 0.1, controls }}
                source={{
                  type: "video",
                  sources: [
                    {
                      src: "https://www.youtube.com/watch?v=yrgI2U9hAc8",
                      provider: "youtube",
                    },
                  ],
                }}
                // ref={ref}
              />
            </div>
          </div>
        </div>
        <div className="row container-block mt-7">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <img src={whyPlayImg} alt="Why Play Rekindle Minds" />
          </div>
          <div className="col-12 col-md-6">
            <h2>Why play Rekindle Minds?</h2>
            <p>
              Rekindle Minds takes children on a playful deep-dive into their
              emotions.This delightful interactive game helps them build strong
              relationships, manage stress and develop a positive, confident
              personality.
            </p>
          </div>
        </div>
        <div className="row container-block mt-7">
          <div className="col-12 col-md-6 ">
            <h2>How does it work?</h2>
            <p>
              Explore, learn and grow with 150+ immersive stories, quizzes,
              puzzles, and games - designed by trusted educators to develop your
              child’s EQ
            </p>
          </div>
          <div className="col-12 col-md-6 block-img d-flex justify-content-end">
            <img src={howItWorksImg} alt="Who it Works" />
          </div>
        </div>
      </div>
      <div className="ribbon-block">
        <div className="ribbon"></div>
      </div>
      <div className="container rekindle-minds mt-7">
        <div className="row container-block">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <img src={progressReportImg} alt="Progress Report" />
          </div>
          <div className="col-12 col-md-6">
            <h2>Expert-approved guide to SEL</h2>
            <p>
              Lay a strong foundation for social-emotional learning with 5
              playful islands designed as per the CASEL framework
            </p>
          </div>
        </div>
        <div className="row mt-3">
          <div className="col-12">
            <div className="rekindle-carousel mt-7">
              <Carousel
                infiniteLoop={true}
                autoPlay={true}
                showStatus={false}
                showIndicators={false}
                swipeable={false}
                renderArrowPrev={(clickHandler, hasPrev) => {
                  return (
                    <div className={`arrow left-arrow`} onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <img src={leftArrow} alt="Left" />
                      </button>
                    </div>
                  );
                }}
                renderArrowNext={(clickHandler, hasNext) => {
                  return (
                    <div className={`arrow right-arrow`} onClick={clickHandler}>
                      <button className="btn btn-primary">
                        <img src={rightArrow} alt="Right" />
                      </button>
                    </div>
                  );
                }}
              >
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <img src={reviewTwo} className="left" alt="Left" />
                      <img src={reviewOne} className="center" alt="Center" />
                      <img src={reviewThree} className="right" alt="Right" />
                      <div className="dots">
                        <span className="active"></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      “Love how this app helps the young ones understand their
                      feelings! My daughter Zoey is very sensitive so she likes
                      this game a lot! She loves playing with the character
                      Moyo“
                      <br /> <br />- Sarah Honart, Mom to 7 y/o
                    </p>
                  </div>
                </div>
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <img src={reviewOne} className="left" alt="Left" />
                      <img src={reviewTwo} className="center" alt="Center" />
                      <img src={reviewThree} className="right" alt="Right" />
                      <div className="dots">
                        <span></span>
                        <span className="active"></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "Game-changer! This app teaches children to understand and
                      express their emotions in a healthy way."
                      <br /> <br />- Maria Adamms, School Counselor
                    </p>
                  </div>
                </div>
                <div className="row carousel">
                  <div className="col-12 col-md-5">
                    <div className="review-image">
                      <img src={reviewTwo} className="left" alt="Left" />
                      <img src={reviewThree} className="center" alt="Center" />
                      <img src={reviewTwo} className="right" alt="Right" />
                      <div className="dots">
                        <span></span>
                        <span></span>
                        <span className="active"></span>
                      </div>
                    </div>
                  </div>
                  <div className="col-12 col-md-7 right-block">
                    <h2>Hear from Educators and Parents</h2>
                    <p>
                      "The interactive videos are a wonderful way to help guide
                      kids on what is right or wrong."
                      <br /> <br />- Dr. Thor Mitch, Child Psychologist
                    </p>
                  </div>
                </div>
              </Carousel>
            </div>
          </div>
        </div>

        <div className="row d-flex justify-content-center mt-7">
          <span className="games-heading">Explore Now In Top SKIDOS Games</span>
          <div className="games-block">
            <div className="games">
              <img
                width="200"
                height="160"
                src={bikeRacing}
                alt="Skidos Games"
              />
              <span>Bike Racing</span>
              <a href="https://apps.apple.com/us/app/cool-math-games-kids-racing/id1319262120">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "rekindle_bike_racing_download_clicked",
                      "Bike Racing Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <img width="200" height="160" src={doctor} alt="Skidos Games" />
              <span>Doctor</span>
              <a href="https://apps.apple.com/us/app/doctor-games-for-kids/id1506886061">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "rekindle_doctor_download_clicked",
                      "Doctor Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <img width="200" height="160" src={bath} alt="Skidos Games" />
              <span>Bath</span>
              <a href="https://apps.apple.com/us/app/learning-games-for-kids/id1483744837">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "rekindle_bath_download_clicked",
                      "Bath Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
            <div className="games">
              <img
                width="200"
                height="160"
                src={superstore}
                alt="Skidos Games"
              />
              <span>Superstore</span>
              <a href="https://apps.apple.com/us/app/fun-games-kids-preschool-math/id1497549298">
                <button
                  onClick={() =>
                    eventTrackerGa4(
                      "button",
                      "rekindle_superstore_download_clicked",
                      "Superstore Download Button",
                      false
                    )
                  }
                >
                  Download
                </button>
              </a>
            </div>
          </div>
        </div>
        <div className="row container-block d-flex justify-content-center mt-7">
          <span className="games-heading">
            Safe Play- Learning For Your Kid
          </span>
          <div className="certification-block">
            <div className="certification">
              <img src={certificationOne} alt="Skidos Certification" />
              <p className="mt-2">Strict privacy compliance</p>
            </div>
            <div className="certification">
              <img src={certificationTwo} alt="Skidos Certification" />
              <p className="mt-2">Designed by educators & experts</p>
            </div>
            <div className="certification">
              <img src={certificationThree} alt="Skidos Certification" />
              <p className="mt-2">Guided by CASEL framework</p>
            </div>
            <div className="certification">
              <img src={certificationFour} alt="Skidos Certification" />
              <p className="mt-2">Secure learning environment</p>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
export default RekindleMinds;
