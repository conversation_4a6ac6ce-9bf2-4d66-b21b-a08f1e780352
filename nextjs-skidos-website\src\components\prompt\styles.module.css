.container {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 50;
  overflow: hidden;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  text-align: center;
  margin-top: auto;
}

.rotatingIcon {
  margin-bottom: 1rem;
  width: 50px;
  height: 50px;
}

.title {
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
  margin-bottom: 1.5rem;
}

.rotateImageContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 300px;
}

.rotateMainImage {
  width: 250px;
  height: auto;
  max-height: 200px;
  object-fit: contain;
  animation: spin 1.6s linear infinite;
}

.bottomImage {
  width: 300px;
  height: auto;
  max-height: 300px;
  object-fit: contain;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(90deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

/* Specific adjustments for smaller screens */
@media (max-width: 375px) and (max-height: 667px) {
  .content {
    padding-bottom: 20px;
  }

  .rotatingIcon {
    width: 50px;
    height: 50px;
    margin-bottom: 1rem;
  }

  .title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .rotateImageContainer {
    max-width: 250px;
    margin-bottom: 1rem;
  }

  .rotateMainImage {
    width: 200px;
    max-height: 150px;
  }

  .bottomImage {
    width: 250px;
    max-height: 250px;
  }
}

/* Adjustments for laptops and desktops */
@media (min-width: 1024px) and (max-width: 1040px) {
  .container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content {
    max-width: 600px;
    padding: 20px;
    border-radius: 20px;
  }

  .rotatingIcon {
    width: 80px;
    height: 80px;
    margin-bottom: 1.5rem;
  }

  .title {
    font-size: 3rem;
    margin-bottom: 2rem;
  }

  .rotateImageContainer {
    max-width: 500px;
    margin-bottom: 2rem;
  }

  .rotateMainImage {
    width: 400px;
    max-height: 300px;
  }

  .bottomImage {
    width: 400px;
    max-height: 400px;
  }
}
@media screen and (max-height: 500px) and (orientation: landscape) {
  .rotatingIcon {
    width: 30px;
    height: 30px;
  }
  .title {
    font-size: 1.5rem;
  }
  .rotateMainImage {
    width: 110px;
    height: 110px;
  }
  .bottomImage {
    width: 150px;
    height: 100px;
  }
}
