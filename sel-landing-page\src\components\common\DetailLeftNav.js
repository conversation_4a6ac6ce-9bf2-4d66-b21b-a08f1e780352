import React from "react";
import PropTypes from "prop-types";

export function DetailLeftNav(props) {
  return (
    // <div className="detail_list_container">
    //   <div className="list_item list_item_active">All games</div>
    //   {props.list.map((item) => (
    //     <div className="list_item" key={item}>
    //       {item}
    //     </div>
    //   ))}
    // </div>
    <></>
  );
}

export default DetailLeftNav;

DetailLeftNav.propTypes = {
  list: PropTypes.array.isRequired,
};
