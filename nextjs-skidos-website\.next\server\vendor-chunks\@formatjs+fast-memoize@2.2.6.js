"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+fast-memoize@2.2.6";
exports.ids = ["vendor-chunks/@formatjs+fast-memoize@2.2.6"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.6/node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+fast-memoize@2.2.6/node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.6/node_modules/@formatjs/fast-memoize/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.6/node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+fast-memoize@2.2.6/node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+fast-memoize@2.2.6/node_modules/@formatjs/fast-memoize/lib/index.js\n");

/***/ })

};
;