/* common container style */
.auth_container {
  height: 100vh;
  background: url(../../icons/auth/sky_bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  background-position: 0% 17%;
  overflow: hidden;
}
.auth_light_purple {
  height: 100vh;
  background: url(../../icons/auth/purple_light_bg.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 0% -36%;
  position: relative;
}
.auth_left_tree {
  position: absolute;
  top: -19%;
  left: 0;
  z-index: 1;
}
.auth_right_tree {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}
.auth_purple_bush {
  position: absolute;
  top: 65%;
  left: 0;
  z-index: 2;
  width: 100%;
}
.auth_content {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 3;
}
/* auth component style */
.auth_left_container {
  margin-top: 25%;
}
.auth_left_title {
  font: normal normal bold 24px/30px Quicksand;
  color: #fff;
  margin-top: 60px;
}
.auth_left_content {
  font: normal normal normal 16px/26px Quicksand;
  color: #fff;
  margin-top: 20px;
}
.auth_left_content img {
  margin-right: 12px;
  vertical-align: text-bottom;
}
.auth_right_container {
  background: transparent linear-gradient(180deg, #049b97 0%, #154ab3 100%) 0%
    0% no-repeat padding-box;
  mix-blend-mode: multiply;
  border-radius: 34px;
  opacity: 0.95;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  padding: 25px 22px;
}
.auth_right_title {
  color: #fff;
  text-align: center;
  font: normal normal normal 30px/38px Quicksand;
  color: #fff;
}
.auth_right_content {
  text-align: center;
  font: normal normal normal 16px/20px Quicksand;
  color: #fff;
  margin-top: 20px;
}
.auth_right_content img {
  margin-right: 10px;
  vertical-align: middle;
}
.auth_align_middle {
  display: flex;
  align-items: center;
  height: 100vh;
}
.auth_align_middle .auth_right_container {
  width: 100%;
  margin-top: auto;
  margin-bottom: auto;
}
.auth_pricing_block {
  border: 1px solid #fff;
  border-radius: 16px;
  padding: 27px;
  position: relative;
  margin-top: 30px;
  color: #fff;
  cursor: pointer;
}
.auth_pricing_block .apb_title {
  font: normal normal bold 16px/20px Quicksand;
  display: inline-block;
  background-color: #1481a5;
  padding: 5px 10px;
  position: absolute;
  top: -18px;
  left: 18px;
  backdrop-filter: blur(16px);
}
.auth_pricing_block .apb_offer {
  display: inline-block;
  background: #c01885 0% 0% no-repeat padding-box;
  border-radius: 16px;
  padding: 5px 15px;
  position: absolute;
  top: -18px;
  right: 30px;
  font: normal normal bold 16px/20px Quicksand;
}
.auth_pricing_block .apb_price {
  font: normal normal bold 18px/23px Quicksand;
}
.auth_pricing_block .apb_price .currency {
  font: normal normal bold 24px/23px Quicksand;
}
.auth_pricing_block .apb_content {
  font: normal normal normal 16px/20px Quicksand;
}
.auth_pricing_block .apb_radio {
  display: inline-block;
  border-radius: 50%;
  height: 28px;
  width: 28px;
  background-color: transparent;
  border: 1px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
}
.auth_pricing_block .apb_radio .selected {
  display: inline-block;
  border-radius: 50%;
  height: 14px;
  width: 14px;
  background-color: #fff;
}
.auth_link {
  color: #00c6a3;
  cursor: pointer;
}
.auth_link:hover {
  color: #00c6a3;
}

@media only screen and (max-width: 960px) {
  .auth_container {
    height: auto;
    overflow-y: auto;
  }
  .auth_light_purple {
    height: 160vh;
    overflow-y: hidden;
    background-size: contain;
    background-position: 0% 90%;
  }
  .auth_left_container {
    margin-top: 4%;
  }
  .auth_align_center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .auth_left_title {
    margin-top: 25px;
  }
  .auth_align_middle {
    align-items: flex-start;
    justify-content: center;
  }
  .auth_align_middle .auth_right_container {
    margin-top: 70px;
    margin-bottom: initial;
    max-width: 450px;
  }
  .auth_left_tree {
    top: 0;
    max-width: 160px;
  }
  .auth_right_tree {
    max-width: 200px;
  }
  .auth_light_purple {
    height: 135vh;
  }
  .auth_purple_bush {
    top: 50%;
  }
}
@media only screen and (max-width: 700px) {
  .auth_container {
    height: auto;
    overflow-y: auto;
  }
  .auth_light_purple {
    min-height: 150vh;
    height: auto;
    background-size: contain;
    background-position: 0% 90%;
  }
  .auth_purple_bush {
    top: auto;
    bottom: 0;
  }
  .auth_left_container {
    margin-top: 0;
  }
  .auth_left_tree {
    display: none;
  }
  .auth_right_tree {
    display: none;
  }
  .auth_align_middle .auth_right_container {
    margin-top: 40px;
  }
}
/* ipad pro */
@media only screen and (min-width: 1024px) and (min-height: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) {
  .auth_light_purple {
    height: 54vh;
    background-position: 0% -90%;
  }
  .auth_align_middle .auth_right_container {
    margin-top: 30%;
    margin-bottom: inherit;
  }
  .auth_align_middle {
    height: auto;
  }
}
